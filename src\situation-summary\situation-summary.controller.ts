import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { SituationSummary, User } from '../models';
import { ERROR } from '../utils/error';
import * as situationSummaryService from './situation-summary.service';
import { ISearchSituationSummaryQuery } from './situation-summary';
import { ISearchQuery } from 'src/types/req';

export const createSituationSummary = async (
  req: Request & { body: SituationSummary },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await situationSummaryService.create(req.body);
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateSituationSummary = async (
  req: Request & { body: SituationSummary } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await situationSummaryService.update(
      Number(req.params.id),
      req.body,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteSituationSummary = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await situationSummaryService.remove(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getSituationSummary = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await situationSummaryService.findById(
      Number(req.params.id),
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const searchSituationSummaries = async (
  req: Request & { query: ISearchQuery<ISearchSituationSummaryQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await situationSummaryService.search(
      req.query,
      req.user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

// export const exportToDocx = async (
//   req: Request & { params: { id: number } },
//   res: Response,
//   next: NextFunction,
// ) => {
//   try {
//     const result = await situationSummaryService.exportDocx(
//       Number(req.params.id),
//     );
//     res.setHeader(
//       'Content-Type',
//       'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
//     );
//     res.setHeader(
//       'Content-Disposition',
//       'attachment; filename=situation-summary.docx',
//     );
//     return res.send(result);
//   } catch (e) {
//     if (e instanceof Error) {
//       return res.status(status.BAD_REQUEST).json({
//         part: 'body',
//         code: ERROR.BAD_REQUEST,
//         message: e.message.toString(),
//         statusCode: 400,
//       });
//     }
//     next(e);
//   }
// };

// export const exportBook = async (
//   req: Request,
//   res: Response,
//   next: NextFunction,
// ) => {
//   try {
//     const result = await situationSummaryService.exportBook(req.query);
//     res.setHeader(
//       'Content-Type',
//       'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
//     );
//     res.setHeader('Content-Disposition', 'attachment; filename=duty-book.docx');
//     return res.send(result);
//   } catch (e) {
//     if (e instanceof Error) {
//       return res.status(status.BAD_REQUEST).json({
//         part: 'body',
//         code: ERROR.BAD_REQUEST,
//         message: e.message.toString(),
//         statusCode: 400,
//       });
//     }
//     next(e);
//   }
// };

export const docxSituationSummary = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await situationSummaryService.exportDocxById(
      Number(req.params.id),
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
