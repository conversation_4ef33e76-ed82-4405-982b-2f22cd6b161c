import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import EntityModel from './entity.model';

@Entity('shiftHandovers')
export class ShiftHandover extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'shift_now_id',
    type: 'int',
  })
  shiftNowId?: number;

  @Column({
    name: 'shift_feature_id',
    type: 'int',
  })
  shiftFeatureId?: number;

  @Column({
    name: 'guard_post_id',
    type: 'int',
  })
  guardPostId?: number;

  @Column({
    name: 'qneid_supervisor',
    type: 'nvarchar',
    length: 250,
  })
  qneidSuperVisor?: string;

  @Column({
    name: 'task_content',
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  taskContent?: string;

  @Column({
    name: 'task_continue',
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  taskContinue?: string;

  @Column({
    name: 'book_handover',
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  bookHandover?: string;

  @Column({
    name: 'vehicle_count_start',
    type: 'int',
  })
  vehicleCountStart?: number;

  @Column({
    name: 'vehicle_entry_count_during',
    type: 'int',
  })
  vehicleEntryCountDuring?: number;

  @Column({
    name: 'vehicle_exit_count_during',
    type: 'int',
  })
  vehicleExitCountDuring?: number;

  @Column({
    name: 'vehicle_count_handover',
    type: 'int',
  })
  vehicleCountHandover?: number;

  @Column({
    name: 'equipment_handover',
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  equipmentHandover?: string;
}
