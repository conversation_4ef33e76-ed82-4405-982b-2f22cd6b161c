import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  createAttendance,
  createAttendanceReason,
  docxAttendanceList,
  getAttendanceList,
  getAttendanceReport,
  getDashboardDetailByEQN,
  getDashboardStats,
} from './timesheet.controller';
import {
  eCreateAttendanceLog,
  eCreateAttendanceReason,
  eTimesheetDetailQuery,
  eTimesheetListQuery,
  eTimesheetQuery,
  jCreateAttendanceLog,
  jCreateAttendanceReason,
  jTimesheetDetailQuery,
  jTimesheetListQuery,
  jTimesheetQuery,
} from './timesheet.validation';

const router = express.Router();

router.get(
  '/dashboard/stats',
  checkAuth,
  createValidator('query', jTimesheetQuery, eTimesheetQuery),
  getDashboardStats,
);

router.get(
  '/attendance/list',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewExportTimeSheet,
  //   RolePerms.viewTimeSheetManagement,
  //   RolePerms.viewTongHopChamCong,
  //   RolePerms.viewChamCong,
  // ]),
  createValidator('query', jTimesheetListQuery, eTimesheetListQuery),
  getAttendanceList,
);

router.get(
  '/attendance',
  checkAuth,
  createValidator('query', jTimesheetQuery, eTimesheetQuery),
  getAttendanceReport,
);

router.post(
  '/attendance',
  checkAuth,
  createValidator('body', jCreateAttendanceLog, eCreateAttendanceLog),
  createAttendance,
);

router.post(
  '/attendance/reason',
  checkAuth,
  createValidator('body', jCreateAttendanceReason, eCreateAttendanceReason),
  createAttendanceReason,
);

router.get(
  '/eqn/detail',
  checkAuth,
  createValidator('query', jTimesheetDetailQuery, eTimesheetDetailQuery),
  getDashboardDetailByEQN,
);

router.get(
  '/attendance/tkbc/docx',
  checkAuth,
  // hasPerm([RolePerms.viewExportTimeSheet, RolePerms.viewTimeSheetManagement]),
  createValidator('query', jTimesheetListQuery, eTimesheetListQuery),
  docxAttendanceList,
);

export default router;
