import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  deleteUser,
  getById,
  getMyself,
  login,
  loginByKeycloak,
  logout,
  postUser,
  putChangePassUser,
  putUser,
  search,
  updateStatusUsers,
} from './users.controllers';
import {
  eIdInParams,
  eMessage,
  jBodyChangePassUser,
  jBodyLogin,
  jBodyLoginKeycloak,
  jBodyPostUser,
  jBodyPuttUser,
  jIdInParams,
  jIdInUpdateStatus,
  jLogout,
  jSearchUsers,
} from './users.validation';
// import { hasPerm } from '../middlewares/has-perm.middleware';
// import { RolePerms } from '../constants';

const routerUser = express.Router();

routerUser.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editUser]),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPostUser,
    eMessage,
  ),
  postUser,
);

routerUser.put(
  '/:id',
  checkAuth,
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPuttUser,
    eMessage,
  ),
  putUser,
);

routerUser.get(
  '/:id',
  checkAuth,
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  getById,
);

routerUser.put(
  '/:id/change-pass',
  checkAuth,
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyChangePassUser,
    eMessage,
  ),
  putChangePassUser,
);

routerUser.put(
  '/:id/status',
  checkAuth,
  // hasPerm([RolePerms.editUser]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  deleteUser,
);

routerUser.get(
  '/',
  checkAuth,
  // hasPerm([RolePerms.viewUser]),
  createValidator(
    eMessage.part ? eMessage.part : 'query',
    jSearchUsers,
    eMessage,
  ),
  search,
);

routerUser.post(
  '/login',
  createValidator(eMessage.part ? eMessage.part : 'body', jBodyLogin, eMessage),
  login,
);

routerUser.post(
  '/login/keycloak',
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyLoginKeycloak,
    eMessage,
  ),
  loginByKeycloak,
);

routerUser.get('/token/me', checkAuth, getMyself);

routerUser.put(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editUser]),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jIdInUpdateStatus,
    eMessage,
  ),
  updateStatusUsers,
);

routerUser.post(
  '/logout',
  createValidator(eMessage.part ? eMessage.part : 'body', jLogout, eMessage),
  logout,
);

export default routerUser;
