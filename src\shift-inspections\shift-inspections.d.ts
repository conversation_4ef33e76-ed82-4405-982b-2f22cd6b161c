export interface ISearchShiftInspectionQuery {
  qneId?: string;
  datetime?: Date[];
  posts?: number[];
  page: number;
  limit: number;
  fullText?: string;
}

export interface ICreateUpdateShiftInspection {
  qneId: string;
  codeOrg: string;
  datetime: Date;
  contentCheck?: string;
  posts?: string;
  note?: string;
  weaponCheck?: string;
  equipCheck?: string;
  situationCheck?: string;
  guardShiftId?: number;
}
