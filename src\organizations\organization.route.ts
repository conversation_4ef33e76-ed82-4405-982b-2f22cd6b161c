import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  dropOrganization,
  getAllFlatOrganization,
  getAllOfManager,
  getAllOfRole,
  getOrganizationById,
  getSearchOrganizationsOfManager,
  getSearchOrganizationsOfRole,
  getTreeByCode,
  postOrganization,
  updateOrganization,
} from './organization.controller';
import {
  eAddOrganizationBody,
  eIdInParams,
  eSearchAllQuery,
  jBodyOrganizations,
  jBodyPutOrganizations,
  jIdInParams,
  jSearchAllOrganizations,
} from './organization.validation';

const routerOrganization = express.Router();

routerOrganization.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editOrganizations]),
  createValidator(
    eAddOrganizationBody.part ?? 'body',
    jBodyOrganizations,
    eAddOrganizationBody,
  ),
  postOrganization,
);

routerOrganization.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editOrganizations]),
  createValidator(eIdInParams.part ?? 'params', jIdInParams, eIdInParams),
  createValidator(
    eAddOrganizationBody.part ?? 'body',
    jBodyPutOrganizations,
    eAddOrganizationBody,
  ),
  updateOrganization,
);

routerOrganization.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.deleteOrganizations]),
  dropOrganization,
);

routerOrganization.get(
  '/tree/all',
  checkAuth,
  // // hasPerm([RolePerms.viewOrganizations]),
  createValidator(
    eSearchAllQuery.part ?? 'query',
    jSearchAllOrganizations,
    eSearchAllQuery,
  ),
  getAllOfRole,
);

routerOrganization.get(
  '/tree/search',
  checkAuth,
  // // hasPerm([RolePerms.viewOrganizations]),
  createValidator(
    eSearchAllQuery.part ?? 'query',
    jSearchAllOrganizations,
    eSearchAllQuery,
  ),
  getSearchOrganizationsOfRole,
);

routerOrganization.get(
  '/:id',
  checkAuth,
  // // hasPerm([RolePerms.viewOrganizations]),
  createValidator(eIdInParams.part ?? 'params', jIdInParams, eIdInParams),
  getOrganizationById,
);

routerOrganization.get(
  '/tree/manager-all',
  checkAuth,
  // // hasPerm([RolePerms.viewOrganizations]),
  createValidator(
    eSearchAllQuery.part ?? 'query',
    jSearchAllOrganizations,
    eSearchAllQuery,
  ),
  getAllOfManager,
);
routerOrganization.get(
  '/tree/manager-search',
  checkAuth,
  // // hasPerm([RolePerms.viewOrganizations]),
  createValidator(
    eSearchAllQuery.part ?? 'query',
    jSearchAllOrganizations,
    eSearchAllQuery,
  ),
  getSearchOrganizationsOfManager,
);

routerOrganization.get(
  '/',
  checkAuth,
  // // hasPerm([RolePerms.viewOrganizations]),
  createValidator(
    eSearchAllQuery.part ?? 'query',
    jSearchAllOrganizations,
    eSearchAllQuery,
  ),
  getAllFlatOrganization,
);

routerOrganization.get(
  '/:code/tree',
  checkAuth,
  // // hasPerm([RolePerms.viewOrganizations]),
  getTreeByCode,
);

export default routerOrganization;
