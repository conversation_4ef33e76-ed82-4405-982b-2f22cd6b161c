import { eQN, GuardPost, GuardShift, ShiftPost, ShiftStaff } from '@src/models';

export type ISearchGuardShiftQuery = {
  date?: string;
  startTime?: string;
  endTime?: string;
  codeOrg?: string;
  note?: string;
  orderNum?: number;
  limit: number;
  page: number;
};

export type ISearchGuardScheduleQuery = {
  date?: string;
  guardPostId?: number[];
  configShiftId?: number[];
};

export interface ShiftStaffWithUser extends ShiftStaff {
  eQN: eQN | null;
}

export interface ShiftPostWithStaff extends ShiftPost {
  shiftStaffs: ShiftStaffWithUser[];
}

export interface GuardShiftWithPosts extends GuardShift {
  shiftPosts: ShiftPostWithStaff[];
}

export interface GuardPostWithShifts extends GuardPost {
  id: number;
  name: string;
  location: string;
  guardShifts: GuardShiftWithPosts[];
}

export interface GuardScheduleResponse {
  guardPost: GuardPostWithShifts[];
}

export type ICreateGuardSchedule = {
  guardPostId: number;
  userId: number;
  date?: Date;
  startTime?: Date;
  endTime?: Date;
  codeOrg?: string;
  note?: string;
  orderNum?: number;
  eQNId?: string[];
};

export type IDuplicateGuardSchedule = {
  oldDate: Date;
  newDate: Date;
};

export type IDeleteGuardPostInGuardSchedule = {
  date: Date;
};
