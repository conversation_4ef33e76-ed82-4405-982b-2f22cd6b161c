import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGenerated<PERSON><PERSON>umn,
  Jo<PERSON><PERSON><PERSON>um<PERSON>,
  BeforeInsert,
} from 'typeorm';
import EntityModel from './entity.model';
import { Guest } from './guest.model';
import { Type } from './type.model';

@Entity('identifyCards')
export class IdentifyCard extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'guest_id',
    type: 'int',
  })
  guestId?: number;

  @Column({
    name: 'identification_number',
    type: 'nvarchar',
    length: 50,
  })
  identificationNumber?: string;

  @Column({
    name: 'document_type_id',
    type: 'int',
  })
  documentTypeId?: number;
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'document_type_id' })
  documentType?: Type;

  @Column({
    name: 'issue_date',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  issueDate?: Date;

  @Column({
    name: 'expiry_date',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  expiryDate?: Date;

  @Column({
    name: 'issuing_authority',
    type: 'nvarchar',
    length: 250,
    nullable: true,
    default: null,
  })
  issuingAuthority?: string;

  @Column({
    type: 'nvarchar',
    length: 2000,
    nullable: true,
    default: null,
  })
  notes?: string;

  @Column({
    type: 'simple-array',
    nullable: true,
    default: null,
  })
  images?: string[];

  @ManyToOne(() => Guest, (guest) => guest.identifyCards)
  @JoinColumn({ name: 'guest_id' })
  guest?: Guest;

  @BeforeInsert()
  beforeCreate() {
    const cols = [this.identificationNumber, this.issuingAuthority];
    this.fullText = cols.join(' ')?.trim();
  }
}
