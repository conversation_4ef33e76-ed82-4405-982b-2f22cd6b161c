import { MigrationInterface, QueryRunner } from "typeorm";

export class T1753754016546 implements MigrationInterface {
    name = 'T1753754016546'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP CONSTRAINT "FK_20db3f19fff11cb028d83af8729"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP COLUMN "maDonVi"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD "maDonVi" nvarchar(17) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD CONSTRAINT "FK_ff04f0560ae52c6c80efeccba8b" FOREIGN KEY ("maDonVi") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP CONSTRAINT "FK_ff04f0560ae52c6c80efeccba8b"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP COLUMN "maDonVi"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD "maDonVi" varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD "code" uniqueidentifier`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD CONSTRAINT "FK_20db3f19fff11cb028d83af8729" FOREIGN KEY ("code") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
