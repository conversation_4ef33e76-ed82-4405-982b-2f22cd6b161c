import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import * as equipHandoverService from './equip-handover.service';
import { ISearchQuery } from '../types/req';
import { ISearchEquipHandoverQuery } from './equip-handover';

export const createEquipHandover = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const equipHandover = req.body;
    const result = await equipHandoverService.create(equipHandover);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateEquipHandover = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = req.params.id;
    const data = req.body;
    const result = await equipHandoverService.update(Number(id), data);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Bàn giao trang thiết bị không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getAllEquipHandovers = async (
  req: Request & { query: ISearchQuery<ISearchEquipHandoverQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const query = req.query;
    const result = await equipHandoverService.getAll(query);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getEquipHandover = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = req.params.id;
    const result = await equipHandoverService.findById(Number(id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Bàn giao trang thiết bị không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteEquipHandover = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = req.params.id;
    const result = await equipHandoverService.deleteOne(Number(id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Bàn giao trang thiết bị không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
