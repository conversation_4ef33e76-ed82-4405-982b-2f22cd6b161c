import jwt from 'jsonwebtoken';
import { ERROR } from '../utils/error';
import config, { database } from '../config';
import { Log, Organization, Role, Type, User } from '../models';
import bcrypt from 'bcryptjs';
import { ISearchQuery } from '../types/req';
import { IGetUsersQuery } from './users';
import { createLinks, DEFAULT_LIMIT } from '../utils/pagination';
import log from '../utils/log';
import { LOG } from '../constants';
import { IApiError } from '../types/validation';
import { insertLog } from '../logs/logs.service';
import { specificCommonPrefix } from '../utils/common';
import { In } from 'typeorm';
import dayjs from 'dayjs';
import axios from 'axios';
import { KEYCLOAK_CONFIG, KEYCLOAK_ENDPOINTS } from '../config/keycloak';

function createFullText(user: Partial<User>): string {
  const fields = [
    user.name,
    user.contact,
    user.username,
    user.organization?.name,
    user.role?.name,
    user.notes,
    user.rank?.name,
    user.position?.name,
  ];
  return fields.filter(Boolean).join(' ');
}
export const insertUser = async (userBody: User, user?: User, ip?: string) => {
  const errors: IApiError['errors'] = [];
  const repoUser = database.getRepository(User);
  const repoOrg = database.getRepository(Organization);
  const repoRole = database.getRepository(Role);
  const repoType = database.getRepository(Type);

  const exitsName = await repoUser
    .createQueryBuilder('users')
    .withDeleted()
    .select('')
    .where('users.username= :username', { username: userBody.username })
    .andWhere('users.status =1')
    .getRawMany();

  if (exitsName && exitsName.length > 0) {
    errors.push({
      field: 'username',
      value: `username:${userBody.username}`,
      message: 'Tên username đã tồn tại.',
    });
  }
  if (userBody.organizationId) {
    const findOrg = await repoOrg.findOne({
      where: { id: userBody.organizationId },
      withDeleted: true,
    });
    if (findOrg == null) {
      errors.push({
        value: `organizationId:${userBody.organizationId}`,
        message: 'Không tồn tại organizationId',
      });
    } else {
      userBody.organization = findOrg;
      userBody.orgCode = findOrg.code;
    }
  }
  if (userBody.roleId) {
    const findRole = await repoRole.findOne({
      where: { id: userBody.roleId },
      withDeleted: true,
    });
    if (findRole == null) {
      errors.push({
        value: `roleId:${userBody.roleId}`,
        message: 'Không tồn tại roleId',
      });
    } else {
      if (
        findRole.name === config.supperAdminRoleName &&
        user?.role?.name !== config.supperAdminRoleName
      ) {
        errors.push({
          value: `roleId:${userBody.roleId}`,
          message: 'Bạn không có quyền tạo tài khoản super admin',
        });
      }
      userBody.role = findRole;
    }
  }
  if (userBody.rankId) {
    const findRank = await repoType.findOne({
      where: { id: userBody.rankId },
      withDeleted: true,
    });
    if (findRank == null) {
      errors.push({
        value: `rankId:${userBody.rankId}`,
        message: 'Không tồn tại cấp bậc',
      });
    } else {
      userBody.rank = findRank;
    }
  }
  if (userBody.positionId) {
    const findPosition = await repoType.findOne({
      where: { id: userBody.positionId },
      withDeleted: true,
    });
    if (findPosition == null) {
      errors.push({
        value: `positionId:${userBody.positionId}`,
        message: 'Không tồn tại chức vụ',
      });
    } else {
      userBody.rank = findPosition;
    }
  }
  if (userBody.manageOrgCode) {
    const findOrg = await repoOrg.findOne({
      where: { code: userBody.manageOrgCode },
      withDeleted: true,
    });
    if (findOrg == null) {
      errors.push({
        value: `manageOrgCode:${userBody.manageOrgCode}`,
        message: 'Không tồn tại organizationCode',
      });
    } else {
      userBody.manageOrganization = findOrg;
    }
  }

  const error = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
    errors,
  };
  if (errors && errors.length > 0) {
    return { error };
  }

  userBody.fullText = createFullText(userBody);

  const temp = repoUser.create({
    ...userBody,
    lastPasswordChangeTime: new Date(),
    lastLoginTime: new Date(),
  });
  const newObj = await repoUser.save(temp);
  await insertLog({
    ...(user && { content: `Created new user: ${userBody.name}` }),
    ...(ip && { ip }),
    ...(user && { userId: user.id }),
    typeId: LOG.CREATE,
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  return { newObj };
};
export const updateUser = async (
  id: number,
  body: Partial<User>,
  user?: User,
  ip?: string,
) => {
  const errors: IApiError['errors'] = [];
  const repoUser = database.getRepository(User);
  const repoOrg = database.getRepository(Organization);
  const repoType = database.getRepository(Type);

  const existed = await repoUser.findOne({
    where: { id, status: true },
    withDeleted: true,
  });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  if (body.username && body.username !== existed.username) {
    const exitsName = await repoUser
      .createQueryBuilder('users')
      .withDeleted()
      .select('')
      .where('users.username= :username', { username: body.username })
      .andWhere('users.status =1')
      .getRawMany();

    if (exitsName && exitsName.length > 0) {
      errors.push({
        value: `username:${body.username}`,
        message: 'Tên username đã tồn tại.',
      });
    }
  }
  if (body.organizationId && body.organizationId !== existed.organizationId) {
    const findOrg = await repoOrg.findOne({
      where: { id: body.organizationId },
      withDeleted: true,
    });
    if (findOrg == null) {
      errors.push({
        value: `organizationId:${body.organizationId}`,
        message: 'Không tồn tại organizationCode',
      });
    } else {
      body.organization = findOrg;
    }
  }
  if (body.rankId && body.rankId !== existed.rankId) {
    const findRank = await repoType.findOneBy({ id: body.rankId });
    if (findRank == null) {
      errors.push({
        value: `rankId:${body.rankId}`,
        message: 'Không tồn tại cấp bậc',
      });
    } else {
      body.rank = findRank;
    }
  }
  if (body.positionId && body.positionId !== existed.positionId) {
    const findPosition = await repoType.findOneBy({ id: body.positionId });
    if (findPosition == null) {
      errors.push({
        value: `positionId:${body.positionId}`,
        message: 'Không tồn tại chức vụ',
      });
    } else {
      body.rank = findPosition;
    }
  }
  if (body.manageOrgCode && body.manageOrgCode !== existed.manageOrgCode) {
    const findOrg = await repoOrg.findOneBy({ code: body.manageOrgCode });
    if (findOrg == null) {
      errors.push({
        value: `manageOrgCode:${body.manageOrgCode}`,
        message: 'Không tồn tại organizationCode',
      });
    } else {
      body.manageOrganization = findOrg;
    }
  }
  const error = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
    errors,
  };

  if (errors && errors.length > 0) {
    return { error };
  }
  const userUpdate = { ...existed, ...body };
  userUpdate.fullText = createFullText(userUpdate);

  const log =
    user && ip
      ? {
          content: `Update user: ${userUpdate.name}; Updated fields: ${Object.entries(
            userUpdate,
          )
            .filter(
              ([key, value]) =>
                key !== 'fullText' && value !== existed[key as keyof User],
            )
            .map(
              ([key, value]) =>
                `${key}: ${existed[key as keyof User]} => ${value}`,
            )
            .join('; ')}`,
          ip,
          userId: user.id,
          typeId: LOG.UPDATE,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      : undefined;

  const updateObj = await repoUser.save(userUpdate);
  if (log) await insertLog(log);

  return { updateObj };
};
export const findById = async (id: number) => {
  const repo = database.getRepository(User);
  const user = await repo.findOne({
    where: { id },
    relations: ['organization', 'position', 'rank', 'role'],
    withDeleted: true,
  });
  if (!user) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  return user;
};
export const updatePassUser = async (
  id: number,
  password: string,
  newPassword: string,
) => {
  const errors: IApiError['errors'] = [];
  const repoUser = database.getRepository(User);

  const existed = await repoUser.findOne({
    where: { id, status: true },
    withDeleted: true,
  });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  if (password) {
    // const passwordMatched = await bcrypt.compare(password, existed.password);
    // if (passwordMatched) {
    existed.password = await bcrypt.hash(newPassword, 12);
    existed.lastPasswordChangeTime = new Date();
    // } else {
    //   errors.push({
    //     value: `password:${existed.username}`,
    //     message: 'Mật khẩu cũ không trùng với mật khẩu đã đặt.',
    //   });
    // }
  }

  const error = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Mật khẩu cũ không trùng với mật khẩu đã đặt.',
    errors,
  };
  if (errors && errors.length > 0) {
    return { error };
  }

  const updateObj = await repoUser.save(existed);
  const user = await repoUser.findOneBy({ id: updateObj.id });

  return { user };
};
export const updateStatusUser = async (ids: number[]) => {
  const repoUser = database.getRepository(User);

  const existUsers = await repoUser.find({
    where: { id: In(ids) },
    withDeleted: true,
  });

  const existUserIds = existUsers.map((item) => item.id);

  const notExistUserIds = ids.filter((item) => !existUserIds.includes(item));

  const updateSuccess = await repoUser.update(
    { id: In(existUserIds) },
    { status: false },
  );
  if (updateSuccess.affected === 0) {
    return updateSuccess;
  } else return { updateSuccess, errors: notExistUserIds };
};
export const findAll = async (query: ISearchQuery<IGetUsersQuery>) => {
  const userRepository = database.getRepository(User);
  const conditions = userRepository
    .createQueryBuilder('user')
    .leftJoinAndSelect('user.organization', 'organization')
    .leftJoinAndSelect('user.position', 'position')
    .leftJoinAndSelect('user.rank', 'rank')
    .leftJoinAndSelect('user.role', 'role')
    .orderBy('user.id', 'DESC');

  if (query.search && query.search !== '') {
    conditions.andWhere('LOWER(user.fullText) LIKE LOWER(:search)', {
      search: `%${query.search}%`,
    });
  }

  if (query.name && query.name !== '') {
    conditions.andWhere('LOWER(user.name) LIKE LOWER(:name)', {
      name: `%${query.name}%`,
    });
  }

  if (query.username && query.username !== '') {
    conditions.andWhere('user.username LIKE :username', {
      username: `%${query.username}%`,
    });
  }
  if (query.contact && query.contact !== '') {
    conditions.andWhere('user.contact LIKE :contact', {
      contact: `%${query.contact}%`,
    });
  }
  if (query.organizationsCode) {
    const organizationCodes = Array.isArray(query.organizationsCode)
      ? specificCommonPrefix(query.organizationsCode)
      : specificCommonPrefix([query.organizationsCode]);

    organizationCodes.forEach((code) => {
      conditions.orWhere(`user.orgCode LIKE :orgCode`, { orgCode: `${code}%` });
    });
  }

  if (query.ranksId) {
    const rankIds = Array.isArray(query.ranksId)
      ? query.ranksId
      : [query.ranksId];
    conditions.andWhere(`user.rankId IN (:...rankIds)`, { rankIds });
  }

  if (query.positionsId) {
    const positionIds = Array.isArray(query.positionsId)
      ? query.positionsId
      : [query.positionsId];
    conditions.andWhere(`user.positionId IN (:...positionIds)`, {
      positionIds,
    });
  }
  if (query.roleIds) {
    const roleIds = Array.isArray(query.roleIds)
      ? query.roleIds
      : [query.roleIds];
    conditions.andWhere(`user.roleId IN (:...roleIds)`, { roleIds });
  }
  if (query.startDate && query.endDate) {
    conditions.andWhere(`user.lastLoginTime BETWEEN :startDate AND :endDate`, {
      startDate: new Date(query.startDate),
      endDate: new Date(query.endDate),
    });
  }
  const limit: number = query.limit ? Number(query.limit) : DEFAULT_LIMIT;
  const page: number = query.page ? Number(query.page) : 1;

  const totalItems = await conditions.getCount();
  const totalPages = Math.ceil(totalItems / limit);

  const links = createLinks('/users?', query, page, totalPages);

  const users = await conditions
    .skip((page - 1) * limit)
    .take(limit)
    .getMany();

  return {
    meta: {
      totalItems,
      itemsPerPage: query.limit,
      totalPages,
      currentPage: query.page,
      items: [...users],
    },
    links,
  };
};

const createToken = (user: {
  id?: number;
  username?: string;
  ip?: string;
  orgCode?: string;
  manageOrgCode?: string;
}) =>
  jwt.sign(user, config.jwtsecret, { expiresIn: Number(config.token_time) });

export const login = async (
  user: { username: string; password: string },
  ip: string,
) => {
  const repoUser = database.getRepository(User);
  const repoLog = database.getRepository(Log);

  const existed = await repoUser.findOneBy({ username: user.username });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  const passwordMatched = await bcrypt.compare(
    user.password,
    existed.password || '',
  );
  if (!passwordMatched) {
    throw new Error(ERROR.AUTHENTICATION_ERROR);
  }
  log.debug(`Token living time: ${config.token_time}`);

  const token = createToken({
    id: existed.id,
    username: existed.username,
    ip,
    orgCode: existed.orgCode,
    manageOrgCode: existed.manageOrgCode,
  });

  const logNew: Partial<Log> = {
    content: 'Đăng nhập hệ thống thành công',
    ip: ip,
    userId: existed.id,
    typeId: LOG.LOGIN,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  logNew.fullText = `${logNew.content} ${logNew.ip}`;
  const tempLog = repoLog.create(logNew);
  await repoLog.save(tempLog);
  log.debug('tempLog:', tempLog);
  await repoUser.update({ id: existed.id }, { lastLoginTime: new Date() });
  const isOverdue = dayjs().isAfter(
    dayjs(existed.lastPasswordChangeTime).add(6, 'month'),
  );

  return { token, isOverdue };
};

export const getAllInfoById = async (id: number) => {
  const repo = database.getRepository(User);
  const user = await repo.findOne({
    where: { id, status: true },
    relations: [
      'organization',
      'role',
      'role.rolePermissions',
      'role.rolePermissions.permission',
    ],
    withDeleted: true,
  });
  if (!user) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  return user;
};

export const keycloakLoginCheck = async (
  body: { code: string; redirectUri: string },
  ip: string,
) => {
  // keycloak token request
  const params = new URLSearchParams();
  params.append('grant_type', 'authorization_code');
  params.append('code', body.code);
  params.append('redirect_uri', body.redirectUri);
  params.append('client_id', KEYCLOAK_CONFIG.KEYCLOAK_CLIENT_ID);
  params.append('client_secret', KEYCLOAK_CONFIG.KEYCLOAK_CLIENT_SECRET);

  const response = await axios.post(KEYCLOAK_ENDPOINTS.TOKEN, params);

  const { id_token, refresh_token } = response.data;
  const decodedToken = jwt.decode(id_token) as {
    sub: string;
    email: string;
    preferred_username: string;
  };

  // Extract user information from the decoded token
  const repoUser = database.getRepository(User);
  const repoLog = database.getRepository(Log);

  const existed = await repoUser.findOneBy({
    username: decodedToken.preferred_username,
  });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  log.debug(`Token living time: ${config.token_time}`);

  const token = createToken({
    id: existed.id,
    username: existed.username,
    ip,
    orgCode: existed.orgCode,
    manageOrgCode: existed.manageOrgCode,
  });

  const logNew: Partial<Log> = {
    content: 'Đăng nhập hệ thống thành công bằng Keycloak',
    ip: ip,
    userId: existed.id,
    typeId: LOG.LOGIN,
    createdAt: new Date(),
    updatedAt: new Date(),
  };
  logNew.fullText = `${logNew.content} ${logNew.ip}`;
  const tempLog = repoLog.create(logNew);
  await repoLog.save(tempLog);
  log.debug('tempLog:', tempLog);
  await repoUser.update({ id: existed.id }, { lastLoginTime: new Date() });

  return { token, isOverdue: false, keycloakRefreshToken: refresh_token };
};

export const logout = async (keycloakRFToken: string): Promise<void> => {
  const params = new URLSearchParams();
  params.append('client_id', KEYCLOAK_CONFIG.KEYCLOAK_CLIENT_ID);
  params.append('refresh_token', keycloakRFToken);
  params.append('client_secret', KEYCLOAK_CONFIG.KEYCLOAK_CLIENT_SECRET);
  await axios.post(KEYCLOAK_ENDPOINTS.LOGOUT, params);
};
