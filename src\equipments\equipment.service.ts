import { ISearchQuery } from 'src/types/req';
import { database } from '../config';
import { Equipment } from '../models';
import { ERROR } from '../utils/error';
import { ISearchEquipmentQuery } from './equipment';
import { createLinks } from '../utils/pagination';

export const create = async (data: Equipment) => {
  const repo = database.getRepository(Equipment);
  const newEquipment = repo.create(data);
  const saved = await repo.save(newEquipment);

  return { data: saved };
};

export const update = async (id: number, data: Partial<Equipment>) => {
  const repo = database.getRepository(Equipment);

  const existing = await repo.findOne({ where: { id }, withDeleted: true });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.update(id, data);
  return { message: 'Cập nhật thành công' };
};

export const remove = async (id: number) => {
  const repo = database.getRepository(Equipment);
  const existing = await repo.findOne({ where: { id }, withDeleted: true });

  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.delete(id);
  return { message: 'Xóa thành công' };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(Equipment);
  const equipment = await repo.findOne({ where: { id }, withDeleted: true });

  if (!equipment) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  return equipment;
};

export const search = async (query: ISearchQuery<ISearchEquipmentQuery>) => {
  const repo = database.getRepository(Equipment);
  const qb = repo.createQueryBuilder('equipment').withDeleted();
  qb.leftJoinAndSelect('equipment.type', 'type');

  if (query.name) {
    qb.andWhere('equipment.name LIKE :name', { name: `%${query.name}%` });
  }

  if (query.codeOrg) {
    qb.andWhere('equipment.codeOrg = :codeOrg', { codeOrg: query.codeOrg });
  }

  if (query.status) {
    qb.andWhere('equipment.status = :status', { status: query.status });
  }

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit);

  const [data, total] = await qb.getManyAndCount();
  const links = createLinks(
    '/equipment/search?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};
