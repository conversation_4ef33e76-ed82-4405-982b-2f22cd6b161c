import config, { database } from '../config';
import { Organization, User } from '../models';
import { ERROR } from '../utils/error';
import { ISearchOrganizationsQuery } from './organization';
import { ISearchQuery } from '../types/req';
import { ITreeNode } from '../types/tree';
import { createLinks, DEFAULT_LIMIT, DEPTH } from '../utils/pagination';
import { IApiError } from '../types/validation';
import { LevelData } from '../constants';
import { getCodeOrgSearchByLevel } from '../utils/common';

function isValidOrganizationCode(code: string): boolean {
  const regex = /^(\d{2}\.){5}\d{1,2}$/;
  return regex.test(code) && code.length === 17;
}
function createFullText(organization: Partial<Organization>): string {
  const fields = [
    organization.name,
    organization.shortName,
    organization.code,
    organization.desc,
  ];
  return fields.filter(Boolean).join(' ');
}
const appendAncestors = (
  ancestors: Organization[],
  orgsData: Organization[],
  flatOrgs: ITreeNode[],
) => {
  for (const org of ancestors) {
    const index = orgsData.findIndex((d) => d.id === org.id);
    if (index === -1) {
      orgsData.push(org);
      flatOrgs.push({
        id: org.id,
        parentId: org.parentId, // Fix: Change type to string | number
        name: org.name,
        level: 0,
        hasChildren: false,
        code: org.code,
        shortName: org.shortName,
        desc: org.desc,
        orderNumber: org.orderNumber ?? 0,
        isEnable: org.isEnable ?? false,
      });
    }
  }
};
const sortTree = (nodes: ITreeNode[]) => {
  nodes.sort(
    (a, b) => (a.orderNumber ?? Infinity) - (b.orderNumber ?? Infinity),
  );
  nodes.forEach((node) => sortTree(node.children || [])); // Recursively sort children
};

export const getAll = async () => {
  const repoComponents = database.getRepository(Organization);
  const conditions = repoComponents
    .createQueryBuilder('organizations')
    .withDeleted()
    .leftJoinAndSelect('organizations.parent', 'parent')
    .orderBy('organizations.name', 'ASC');
  const orgs = await conditions.getMany();
  const result = orgs.map((item) => {
    if (item.parent) {
      return { ...item, name: `${item.parent.name}/${item.name}` };
    } else return item;
  });
  result.sort((org1, org2) => {
    const name1 = (org1.name ?? '').toLowerCase();
    const name2 = (org2.name ?? '').toLowerCase();
    if (name1 < name2) {
      return -1;
    }
    if (name1 > name2) {
      return 1;
    }
    return 0;
  });
  return result;
};

export const findOrganizationById = async (id: string) => {
  console.log('id:', id);
  const repoOrganization = database.getRepository(Organization);
  const organization = await repoOrganization.findOne({
    where: [{ id: id }],
    withDeleted: true,
  });
  if (!organization) throw new Error(ERROR.DATA_NOT_FOUND);
  return organization;
};

export const insertOrganization = async (organization: Organization) => {
  const errors: IApiError['errors'] = [];
  const repoOrganization = database.getRepository(Organization);

  if (organization.parentId) {
    const parentOrg = await repoOrganization.findOne({
      where: { id: organization.parentId },
      withDeleted: true,
    });
    if (!parentOrg) {
      errors.push({
        field: 'parentId',
        value: `parentId: ${organization.parentId}`,
        message: 'Không tồn tại đơn vị cha',
      });
    } else {
      organization.parent = parentOrg;
      organization.depth = Number(parentOrg.depth) + 1;
    }
  } else {
    organization.depth = 0;
  }

  const existedNameOrganization = await repoOrganization
    .createQueryBuilder('organizations')
    .withDeleted()
    .select('')
    .where('organizations.name= :name', { name: organization.name })
    .andWhere('organizations.parentId= :parentId', {
      parentId: organization.parentId,
    })
    .getRawMany();

  if (existedNameOrganization && existedNameOrganization.length > 0) {
    errors.push({
      field: 'name',
      value: `name: ${organization.name}`,
      message: 'Tên organization đã tồn tại.',
    });
  }

  if (isValidOrganizationCode(organization?.code || '')) {
    const existedCodeOrganization = await repoOrganization
      .createQueryBuilder('organizations')
      .withDeleted()
      .select('')
      .where('organizations.code= :code', { code: organization.code })
      .getRawMany();

    if (existedCodeOrganization && existedCodeOrganization.length > 0) {
      errors.push({
        field: 'code',
        value: `code: ${organization.code}`,
        message: 'Code organization đã tồn tại.',
      });
    }
  } else {
    errors.push({
      field: 'code',
      value: `code: ${organization.code}`,
      message: 'Code không đúng cấu trúc.',
    });
  }

  const error: IApiError = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
    errors,
    statusCode: 400,
  };
  if (errors && errors.length > 0) {
    return { error };
  }

  organization.fullText = createFullText(organization);

  const tempOrganization = repoOrganization.create(organization);
  const newOrganization = await repoOrganization.save(tempOrganization);

  if (organization.parent && organization.parent.hasChild !== true) {
    const orgParent = organization.parent;
    orgParent.hasChild = true;
    await repoOrganization.save(orgParent);
  }

  return { newOrganization };
};

export const putOrganization = async (
  id: string,
  organization: Organization,
) => {
  const errors: IApiError['errors'] = [];
  const repoOrganization = database.getRepository(Organization);

  const existedOrg = await repoOrganization.findOne({
    where: { id },
    withDeleted: true,
  });
  if (!existedOrg) throw new Error(ERROR.DATA_NOT_FOUND);

  if (organization.parentId && organization.parentId !== existedOrg.parentId) {
    const parentOrg = await repoOrganization.findOne({
      where: { id: organization.parentId },
      withDeleted: true,
    });
    if (!parentOrg) {
      errors.push({
        field: 'parentId',
        value: `parentId: ${organization.parentId}`,
        message: 'Không tồn tại đơn vị cha',
      });
    } else {
      organization.parent = parentOrg;
      organization.depth = Number(organization.depth)
        ? Number(parentOrg.depth) + 1
        : 1;
    }
  } else {
    organization.parentId = undefined;
  }

  if (organization.name && existedOrg.name !== organization.name) {
    const existedNameOrganization = await repoOrganization
      .createQueryBuilder('organizations')
      .withDeleted()
      .select('')
      .where('organizations.name= :name', { name: organization.name })
      .andWhere('organizations.parentId= :parentId', {
        parentId: organization.parentId,
      })
      .getRawMany();

    if (existedNameOrganization && existedNameOrganization.length > 0) {
      errors.push({
        field: 'name',
        value: `name: ${organization.name}`,
        message: 'Tên organization đã tồn tại.',
      });
    }
  }

  if (organization.code && existedOrg.code !== organization.code) {
    if (isValidOrganizationCode(organization.code)) {
      const existedCodeOrganization = await repoOrganization
        .createQueryBuilder('organizations')
        .withDeleted()
        .select('')
        .where('organizations.code= :code', { code: organization.code })
        .getRawMany();

      if (existedCodeOrganization && existedCodeOrganization.length > 0) {
        errors.push({
          field: 'code',
          value: `code: ${organization.code}`,
          message: 'Code organization đã tồn tại.',
        });
      }
    } else {
      errors.push({
        field: 'code',
        value: `code: ${organization.code}`,
        message: 'Code không đúng cấu trúc.',
      });
    }
  }

  const error: IApiError = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
    errors,
    statusCode: 400,
  };
  if (errors && errors.length > 0) {
    return { error };
  }

  organization.fullText = createFullText(organization);
  if (organization.parentId && organization.parentId !== existedOrg.parentId) {
    await repoOrganization.delete([existedOrg.id || '']);
    const tempOrganization = repoOrganization.create(organization);
    const newOrganization = await repoOrganization.save(tempOrganization);

    if (organization.parent && organization.parent.hasChild !== true) {
      const orgParent = organization.parent;
      orgParent.hasChild = true;
      await repoOrganization.save(orgParent);
    }
    return { newOrganization };
  } else {
    const tempOrganization = { ...existedOrg, ...organization };
    const newOrganization = await repoOrganization.save(tempOrganization);
    return { newOrganization };
  }
};

export const deleteOrganization = async (id: string) => {
  const repoOrganization = database.getRepository(Organization);

  const organization = await repoOrganization.findOne({
    where: { id },
    withDeleted: true,
  });
  if (!organization) throw new Error(ERROR.DATA_NOT_FOUND);

  const children = await repoOrganization.find({
    where: { parentId: organization.id, isEnable: true },
    withDeleted: true,
  });
  if (children && children.length > 0) {
    throw new Error(ERROR.DATA_EXISTED);
  }
  organization.isEnable = false;
  await repoOrganization.save(organization);
};

function listToTree(organizations: ITreeNode[]): ITreeNode[] {
  const map: Map<string, ITreeNode> = new Map();
  organizations.forEach((org) => {
    return map.set((org.id || '').toString(), {
      id: (org.id || '').toString(),
      parentId: org.parentId ? org.parentId.toString() : '',
      name: org.name,
      hasChildren: org.hasChildren ? org.hasChildren : false,
      orderBy: org.orderBy,
      code: org.code,
      shortName: org.shortName,
      desc: org.desc,
      orderNumber: org.orderNumber,
      isEnable: org.isEnable,
      children: [],
    });
  });

  const tree: ITreeNode[] = [];
  organizations.forEach((org) => {
    const parent = map.get(org.parentId?.toString() || '');
    if (parent && parent.children) {
      parent.children.push(map.get((org?.id ?? '').toString())!);
    } else {
      if (org.id !== undefined && org.id !== null) {
        tree.push(map.get(org.id.toString())!);
      }
    }
  });
  sortTree(tree);
  return tree;
}

export const findAllOrgTreeOfRole = async (
  query: ISearchQuery<ISearchOrganizationsQuery>,
  user?: User,
) => {
  const orgsData: Organization[] = [];
  const flatOrgs: ITreeNode[] = [];

  const limit = query.limit ? Number(query.limit) : DEFAULT_LIMIT;
  const page = query.page ? Number(query.page) : 1;
  const depth = query.depth ? Number(query.depth) : DEPTH;

  if (user && user.organization) {
    if (user.role && user.role.levelData == LevelData.SELF) {
      query.keyLike = user.organization.code;
    } else if (
      user.role &&
      user.role.levelData !== undefined &&
      [LevelData.REGION, LevelData.DIVISION].includes(user.role.levelData)
    ) {
      query.keyLike = getCodeOrgSearchByLevel(
        user.organization?.code || '',
        user.role.levelData,
      );
    }
  }

  const organizationRepository = database.getTreeRepository(Organization);

  const conditions = organizationRepository
    .createQueryBuilder('organizations')
    .orderBy('organizations.orderNumber', 'ASC');

  if (user && user?.role?.levelData == LevelData.ALL)
    conditions.where('organizations.parentId is null');
  if (query.keyLike) {
    conditions.andWhere('organizations.code like :keyLike', {
      keyLike: `${query.keyLike}%`,
    });
  }

  const totalItems = await conditions.getCount();
  const totalPages = Math.ceil(totalItems / limit);

  const links = createLinks(
    '/organizations/tree/all?',
    query,
    page,
    totalPages,
  );

  const organizations = await conditions
    .skip((page - 1) * limit)
    .take(limit)
    .orderBy('organizations.orderNumber', 'ASC')
    .getMany();

  if (organizations && organizations.length > 0) {
    for (const org of organizations) {
      const children = depth
        ? await organizationRepository.findDescendants(org, { depth })
        : await organizationRepository.findDescendants(org);
      appendAncestors(children, orgsData, flatOrgs);
    }
  }
  const rootOrganizations = listToTree(flatOrgs);
  sortTree(rootOrganizations);

  return {
    meta: {
      totalItems: totalItems,
      itemsPerPage: query.limit || DEFAULT_LIMIT,
      totalPages,
      currentPage: page,
      items: rootOrganizations.map((item) => {
        if (item.parentId) delete item.parentId;
        return item;
      }),
    },
    links,
  };
};

export const searchTreeOrganizationOfRole = async (
  query: ISearchQuery<ISearchOrganizationsQuery>,
  user?: User,
) => {
  const organizationRepository = database.getTreeRepository(Organization);
  let donViQuanTri = user?.manageOrgCode;

  organizationRepository.metadata.columns =
    organizationRepository.metadata.columns.map((x) => {
      if (x.databaseName === 'mpath') {
        x.isVirtual = false;
      }
      return x;
    });

  const isSupperAdmin = user?.role?.name === config.supperAdminRoleName;
  if (isSupperAdmin) {
    donViQuanTri = config.rootCode;
  }

  const rootOrg = await organizationRepository
    .createQueryBuilder('organizations')
    .where('organizations.code = :code', { code: donViQuanTri })
    .getOne();

  // Get all relevant organizations in one query
  const allDonVis = await organizationRepository
    .createQueryBuilder('organizations')
    .where('organizations.code = :code', { code: donViQuanTri })
    .orWhere('organizations.mpath LIKE :path', { path: `%${rootOrg?.mpath}%` })
    .addOrderBy('organizations.orderNumber', 'ASC')
    .getMany();

  // Create a map of all DonVis

  const donViMap = new Map();
  allDonVis.forEach((donVi) => {
    donViMap.set(donVi.id, {
      id: donVi.id,
      parentId: donVi.parentId,
      name: donVi.name,
      hasChild: donVi.hasChild,
      code: donVi.code,
      shortName: donVi.shortName,
      desc: donVi.desc,
      orderNumber: donVi.orderNumber,
      isEnable: donVi.isEnable,
      children: [],
    });
  });

  // Build parent-child relationships
  allDonVis.forEach((donVi) => {
    if (donVi.parentId && donViMap.has(donVi.parentId)) {
      // Add this DonVi as a child to its parent
      const parent = donViMap.get(donVi.parentId);
      const child = donViMap.get(donVi.id);
      parent.children.push(child);
    }
  });

  // Get root level DonVis
  const rootDonVis = allDonVis
    .filter(
      (donVi) => donVi.code === donViQuanTri || !donViMap.has(donVi.parentId),
    )
    .map((donVi) => donViMap.get(donVi.id))
    .sort((a, b) => a.orderNumber - b.orderNumber);

  const totalPages = Math.ceil(allDonVis.length / query.limit);
  const links = createLinks(
    '/organizations/tree/search?',
    query,
    query.page,
    totalPages,
  );

  return {
    rootOrg,
    allDonVis,
    meta: {
      totalItems: allDonVis.length,
      itemsPerPage: query.limit || DEFAULT_LIMIT,
      totalPages,
      currentPage: query.page,
      items: rootDonVis,
    },
    links,
  };
};

export const findAllOrgTreeOfManager = async (
  query: ISearchQuery<ISearchOrganizationsQuery>,
) => {
  const orgsData: Organization[] = [];
  const flatOrgs: ITreeNode[] = [];

  const limit = query.limit ? Number(query.limit) : DEFAULT_LIMIT;
  const page = query.page ? Number(query.page) : 1;
  const depth = query.depth ? Number(query.depth) : DEPTH;

  const organizationRepository = database.getTreeRepository(Organization);

  const conditions = organizationRepository
    .createQueryBuilder('organizations')
    .where('organizations.parentId is null')
    .orderBy('organizations.orderNumber', 'ASC');

  const totalItems = await conditions.getCount();
  const totalPages = Math.ceil(totalItems / limit);

  const links = createLinks(
    '/organizations/tree/manager-all?',
    query,
    page,
    totalPages,
  );

  const organizations = await conditions
    .skip((page - 1) * limit)
    .take(limit)
    .orderBy('organizations.orderNumber', 'ASC')
    .getMany();

  if (organizations && organizations.length > 0) {
    for (const org of organizations) {
      const children = depth
        ? await organizationRepository.findDescendants(org, { depth })
        : await organizationRepository.findDescendants(org);

      appendAncestors(children, orgsData, flatOrgs);
    }
  }
  const rootOrganizations = listToTree(flatOrgs);

  return {
    meta: {
      totalItems: totalItems,
      itemsPerPage: query.limit || DEFAULT_LIMIT,
      totalPages,
      currentPage: page,
      items: rootOrganizations.map((item) => {
        if (item.parentId) delete item.parentId;
        return item;
      }),
    },
    links,
  };
};
export const searchTreeOrganizationOfManager = async (
  query: ISearchQuery<ISearchOrganizationsQuery>,
) => {
  const organizationRepository = database.getTreeRepository(Organization);
  const orgsData: Organization[] = [];
  const flatOrgs: ITreeNode[] = [];

  const conditions = organizationRepository.createQueryBuilder('organizations');
  if (query.name)
    conditions.andWhere('LOWER(organizations.name) LIKE LOWER(:name)', {
      name: `%${query.name}%`,
    });
  if (query.search) {
    conditions.andWhere('LOWER(organizations.fullText) LIKE LOWER(:search)', {
      search: `%${query.search}%`,
    });
  }
  if (query.ids) {
    conditions.andWhere('organizations.id IN (:...ids)', { ids: query.ids });
  }
  if (query.codes) {
    conditions.andWhere('organizations.code IN (:...codes)', {
      codes: query.codes,
    });
  }

  const organizations = await conditions.getMany();
  const links = createLinks('/organizations/tree/manager-search?', query, 1, 1);

  if (organizations && organizations.length > 0) {
    for (const org of organizations) {
      const children = await organizationRepository.findAncestors(org);
      appendAncestors(children, orgsData, flatOrgs);
    }
  }
  const organizationTree = listToTree(flatOrgs);

  return {
    meta: {
      totalItems: organizations.length,
      itemsPerPage: query.limit || DEFAULT_LIMIT,
      totalPages: 1,
      currentPage: 1,
      items: organizationTree,
    },
    links,
  };
};

export const findTreeByCode = async (code: string, depth?: number) => {
  const repo = database.getTreeRepository(Organization);
  const existed = await repo.findOneBy({ code });

  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  const curTree = depth
    ? await repo.findDescendantsTree(existed, { depth })
    : await repo.findDescendantsTree(existed);

  return curTree;
};

export const getAllIdsByArray = (data?: Organization[]) => {
  const allOrgs: Organization[] = [];
  const traverse = (obj: Organization) => {
    if (obj) {
      allOrgs.push(obj);
    }
    obj.children?.forEach(traverse);
  };
  if (Array.isArray(data)) {
    data.forEach(traverse);
  } else if (data) {
    traverse(data);
  }
  return allOrgs;
};

/**
 * Lấy tất cả các tổ chức con (bao gồm cả tổ chức cha) từ một tổ chức
 * @param orgCode orgCode của tổ chức cha hoặc mã tổ chức (code)
 * @param user User hiện tại (nếu không có orgCode)
 * @returns Danh sách tất cả các tổ chức con dạng mảng phẳng
 */
export const getAllChildOrganizations = async (
  orgCode?: string,
  user?: User,
) => {
  try {
    orgCode = orgCode || user?.manageOrgCode;
    if (!orgCode) {
      return [];
    }

    // Lấy tổ chức và tất cả các tổ chức con
    const organizations = await database.getTreeRepository(Organization).find({
      where: { code: orgCode },
      relations: [
        'children',
        'children.children',
        'children.children.children',
        'children.children.children.children',
        'children.children.children.children.children',
        'children.children.children.children.children.children',
        'children.children.children.children.children.children.children',
        'children.children.children.children.children.children.children.children',
      ],
    });

    if (!organizations || organizations.length === 0) {
      return [];
    }

    // Chuyển đổi cây thành mảng phẳng
    const allOrgWithChildren = getAllIdsByArray(organizations);
    return allOrgWithChildren;
  } catch (error) {
    console.error('Failed to get all child organizations:', error);
    throw error;
  }
};
