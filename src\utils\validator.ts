import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import Jo<PERSON> from 'joi';
import { IApiError } from '../types/validation';
import { ERROR } from './error';
import log from './log';

const createValidator = (
  part: keyof Request,
  schema: Joi.ObjectSchema | Joi.Schema,
  apiError?: IApiError,
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    log.debug('req[part]:', JSON.stringify(req[part], null, 2));
    const { error } = schema.validate(req[part], {
      abortEarly: false,
    });

    log.debug(
      'Validation error:',
      error ? JSON.stringify(error, null, 2) : null,
    );
    if (error) {
      const defaultMessage = {
        statusCode: ERROR.VALIDATION_ERROR,
        message: '<PERSON><PERSON>m tra các tham số đầu vào!',
      };
      return res.status(status.BAD_REQUEST).json({
        ...(apiError || defaultMessage),
        errors: error.details.map((detail) => {
          return {
            key: detail.context ? detail.context.label : detail.path[1],
            message: detail.message,
          };
        }),
      });
    }
    next();
  };
};

export { createValidator };
