import { MigrationInterface, QueryRunner } from 'typeorm';

export class T1753270063233 implements MigrationInterface {
  name = 'T1753270063233';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "user-guide-content" ("slug" nvarchar(250) NOT NULL, "content" nvarchar(MAX), CONSTRAINT "PK_2857ae4b76db7a915a115c18ad0" PRIMARY KEY ("slug"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user-guides" ("slug" nvarchar(250) NOT NULL, "name" nvarchar(250) NOT NULL, "parentSlug" nvarchar(250) NOT NULL, "order" tinyint NOT NULL CONSTRAINT "DF_c8805e687efb957ac8e5dc6dd24" DEFAULT 0, CONSTRAINT "PK_44ca5e998f057d503498fe67c0c" PRIMARY KEY ("slug"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "permissions" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_da04f89054f39981438894dfe30" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_14936cb23d7de4c7b31b5cef053" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "name" nvarchar(100) NOT NULL, "displayName" nvarchar(100), "desc" nvarchar(500), "status" tinyint NOT NULL CONSTRAINT "DF_bbf6febffd0f64508b38a2cd514" DEFAULT 1, "resource" nvarchar(500), "parentId" int, CONSTRAINT "PK_920331560282b8bd21bb02290df" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_68489757328f76f1986dfe08c2" ON "permissions" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "roles_permissions" ("id" int NOT NULL IDENTITY(1,1), "roleId" int NOT NULL, "permissionId" int NOT NULL, CONSTRAINT "UQ_5829481fc2a13d85b9b6bf3bd53" UNIQUE ("roleId", "permissionId"), CONSTRAINT "PK_298f2c0e2ea45289aa0c4ac8a02" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "roles" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_4d018866397b1e7e78d03b45662" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_c13070745ded32a88c920015f7e" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "name" nvarchar(100) NOT NULL, "desc" nvarchar(500), "status" bit NOT NULL CONSTRAINT "DF_14958a120176d4e1e8be423977c" DEFAULT 1, "levelData" nvarchar(100), CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_beefd8342da401788c55687e22" ON "roles" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "military_types" ("id" varchar(36) NOT NULL, "date_created" datetime2 CONSTRAINT "DF_b45fc00f9821f12fc41ef18f2f8" DEFAULT GETDATE(), "date_updated" datetime2 CONSTRAINT "DF_a2cc5b84a4611200bcb2d3efab5" DEFAULT GETDATE(), "is_enable" bit CONSTRAINT "DF_a4d7248ec0daab3b2f0dd55e4d3" DEFAULT 1, "name" nvarchar(255), "short_name" nvarchar(50) NOT NULL, "order_number" int, "deletedAt" datetime2, CONSTRAINT "PK_3b64d5cb9dffa32f8f4ac5cb335" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "position_categories" ("id" varchar(36) NOT NULL, "code" varchar(50), "date_created" datetime2 CONSTRAINT "DF_122241e58ae68e8584a8295c915" DEFAULT GETDATE(), "date_updated" datetime2 CONSTRAINT "DF_46a8faf04dd5fc608cc0c3d708e" DEFAULT GETDATE(), "is_enable" bit CONSTRAINT "DF_eb5fa3b2e81f7f9f94530dee073" DEFAULT 1, "name" nvarchar(255), "order_number" int, "short_name" nvarchar(50), "type" varchar(50), "deletedAt" datetime2, CONSTRAINT "PK_d7667b4ddcd050b64eb1fe0085f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "military_ranks" ("id" varchar(36) NOT NULL, "date_created" datetime2 CONSTRAINT "DF_2a88e4b68e52209209d264016e5" DEFAULT GETDATE(), "date_updated" datetime2 CONSTRAINT "DF_9ffaf4194aa791aed518538fc50" DEFAULT GETDATE(), "is_enable" bit CONSTRAINT "DF_0f3e0627d2819fc713689a1b391" DEFAULT 1, "name" nvarchar(255), "short_name" nvarchar(50) NOT NULL, "order_number" int, "deletedAt" datetime2, CONSTRAINT "PK_40d02243d950284cc4728c98573" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "eQN" ("id" varchar(36) NOT NULL, "eqn" nvarchar(36) NOT NULL, "full_name" nvarchar(255) NOT NULL, "short_name" nvarchar(255), "orgCode" nvarchar(17) NOT NULL, "reinforcement_org_id" nvarchar(17) NOT NULL, "type_id" varchar(36) NOT NULL, "rank_id" varchar(36) NOT NULL, "status_id" nvarchar(255), "position_id" varchar(36), "phone_number" varchar(20), "is_enable" bit NOT NULL CONSTRAINT "DF_eb12073260a7f06e42538f021fb" DEFAULT 1, "address_books" varchar(255), "birth_place_ward_id" varchar(255), "birthday" date, "cccd" varchar(20), "cccd_issued_ward_id" varchar(255), "gender" bit, "disabled_reason_id" varchar(255), "identification" varchar(255), "image" varchar(255), "createdAt" datetime2 CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e" DEFAULT GETDATE(), "updatedAt" datetime2 CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910" DEFAULT GETDATE(), "deletedAt" datetime2, CONSTRAINT "PK_bcf412613b5b7a2e1036670f011" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "organizationGroup" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_9118ef68f7e71bc9d824db23372" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_64b9e50762cb7429f940490fdce" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" varchar(36) NOT NULL, "name" nvarchar(255) NOT NULL, "shortName" nvarchar(255), "order" smallint NOT NULL CONSTRAINT "DF_e9150cc8a3fbd273c495dc2305d" DEFAULT 0, "Parameters" ntext, "createdById" int, "updatedById" int, CONSTRAINT "PK_3011a552687a279e1bbd70cb14b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_74f9e67092b539d9ed96def730" ON "organizationGroup" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "organizations" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_404ec0c661e183132d6cc865781" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_ba18a9fad8464e44886f8ca2eba" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_6b031fcd0863e3f6b44230163f9" DEFAULT NEWSEQUENTIALID(), "code" nvarchar(17) NOT NULL, "name" nvarchar(100) NOT NULL, "desc" nvarchar(200), "isEnable" bit NOT NULL CONSTRAINT "DF_8e6ce3bb660373cdf188436e19d" DEFAULT 1, "hasChild" bit NOT NULL CONSTRAINT "DF_d82e0076e91eaa71a27470e642f" DEFAULT 0, "shortName" nvarchar(50) NOT NULL, "orderNumber" smallint CONSTRAINT "DF_4d11dcbb23635da34d77d6e66e0" DEFAULT 0, "depth" tinyint, "path" nvarchar(1000), "parentId" uniqueidentifier, "mpathFullName" nvarchar(4000), "orgGroupId" varchar(36), "mpath" nvarchar(255) CONSTRAINT "DF_0d262aa42d95a8f2ae2daba0773" DEFAULT '', CONSTRAINT "UQ_7e27c3b62c681fbe3e2322535f2" UNIQUE ("code"), CONSTRAINT "PK_6b031fcd0863e3f6b44230163f9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e3ce0230c4fac41ac62dd583eb" ON "organizations" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "types" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_512f1af750a92e8aee03ca9a44b" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_e93a97bcab62fd54b72da6ed947" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "name" nvarchar(100) NOT NULL, "desc" nvarchar(200), "scope" nvarchar(50) NOT NULL, "status" bit NOT NULL CONSTRAINT "DF_12bbd0c3b1087e07cce554f7bfa" DEFAULT 1, "value" nvarchar(500), CONSTRAINT "PK_33b81de5358589c738907c3559b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6613a1e72563714795d764971c" ON "types" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "logs" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_1d21181bfc9b5cc798be90d723a" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_953b93af84b32b74e611a528712" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "content" nvarchar(2000) NOT NULL, "ip" nvarchar(50) NOT NULL, "typeId" int NOT NULL, "userId" int NOT NULL, CONSTRAINT "PK_fb1b805f2f7795de79fa69340ba" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6d8b2172ce6e6ec75dec3613ce" ON "logs" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "users" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_204e9b624861ff4a5b268192101" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_0f5cbe00928ba4489cc7312573b" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "username" nvarchar(250), "password" nvarchar(250), "name" nvarchar(250), "status" bit NOT NULL CONSTRAINT "DF_3676155292d72c67cd4e090514f" DEFAULT 1, "contact" nvarchar(500), "notes" nvarchar(500), "orgCode" nvarchar(100) NOT NULL, "lastLoginTime" datetime2, "image" nvarchar(300), "lastPasswordChangeTime" datetime2, "qneid" nvarchar(24), "manageOrgCode" nvarchar(17), "eQNId" varchar(36), "rankId" int, "positionId" int, "organizationId" uniqueidentifier, "roleId" int, CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d7b08ee886bf7dc996b38c5d6a" ON "users" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_450a05c0c4de5b75ac8d34835b" ON "users" ("password") `,
    );
    await queryRunner.query(
      `CREATE TABLE "shiftInspections" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_e1d3d73f255da3c9e4e36ec54d3" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_c5f08846b169784394be3653d58" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "code_org" nvarchar(17) NOT NULL, "datetime" datetime2 NOT NULL, "posts" nvarchar(2000) NOT NULL, "content_check" nvarchar(2000), "qne_id" nvarchar(250) NOT NULL, "weapon_check" nvarchar(2000), "equip_check" nvarchar(2000), "situation_check" nvarchar(2000), "note" nvarchar(2000), "guard_shift_id" int, CONSTRAINT "PK_624d5fd938a579ddc27f44637ae" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8675f1b6e332930b1767ec2f79" ON "shiftInspections" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "guardShifts" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_1b7839da8dfdc373900b2974d03" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_bd8e982df92959307a4fb540908" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "date" datetime2 NOT NULL, "start_time" datetime2 NOT NULL, "end_time" datetime2 NOT NULL, "code_org" nvarchar(17) NOT NULL, "note" nvarchar(2000), "order_num" int NOT NULL, CONSTRAINT "PK_8d22a4ea2e0cf527f1135e38bed" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_028b624b79338071e8f067ab7c" ON "guardShifts" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "guardPosts" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_46fbbef48106e28e5af7181942f" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_9b66df30f533388ebba7708ea38" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "name" nvarchar(250) NOT NULL, "code_org" nvarchar(17) NOT NULL, "order_num" int NOT NULL, "location" nvarchar(500) NOT NULL, "code" nvarchar(500), CONSTRAINT "PK_18b952d5d73f44d013b377575b2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_ac4fd287bf653006f07bc02e25" ON "guardPosts" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "shiftStaff" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_5c0de5f2f998809930fbca918be" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_ec1af3a0c265fa5f45fdab19b9c" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "shift_post_id" int NOT NULL, "qneid" varchar(36), "is_leader" bit NOT NULL CONSTRAINT "DF_d2cb29c1c191685007a76296f56" DEFAULT 0, CONSTRAINT "PK_30cd501af2b4ccce82ef39eb621" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_56beba5e6ea00c4f448fc3e10c" ON "shiftStaff" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "shiftPosts" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_8be5c3b04fa04aacaac13ff1440" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_50ecc0a9d02eaefab907710322d" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "guard_shift_id" int, "guard_post_id" int NOT NULL, CONSTRAINT "PK_21c39bcd4e06a63771f49c7749b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b1a63054a0e8767eb59d92a09d" ON "shiftPosts" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "workingSchedules" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_c6ebad89c80f2c776fc2647bc0a" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_e4eb10c98079265508cd6d630bc" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "start_time" time NOT NULL, "end_time" time NOT NULL, "code_org" nvarchar(17) NOT NULL, "season" nvarchar(50) NOT NULL, "months" nvarchar(250) NOT NULL, "status" nvarchar(50) NOT NULL, "effectiveFrom" varchar(20) NOT NULL CONSTRAINT "DF_8e4e4d29629befa4eaa92e17892" DEFAULT '', "effectiveTo" varchar(20) NOT NULL CONSTRAINT "DF_f012f30b4b8c9ff0347996e0d14" DEFAULT '', "yearSpan" bit NOT NULL CONSTRAINT "DF_bf1c2cbcbcb8c17061f7e4f0216" DEFAULT 0, CONSTRAINT "PK_b3a296d1e735d744683612c37d3" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e1dcc6a62c42b4a7c50688fe0f" ON "workingSchedules" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "shiftHandovers" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_ae73a55c23a29925a42fb6afd96" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_2fa6f0ac70fd38b0466a14e8e5d" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "shift_now_id" int NOT NULL, "shift_feature_id" int NOT NULL, "guard_post_id" int NOT NULL, "qneid_supervisor" nvarchar(250) NOT NULL, "task_content" nvarchar(250), "task_continue" nvarchar(250), "book_handover" nvarchar(250), "vehicle_count_start" int NOT NULL, "vehicle_entry_count_during" int NOT NULL, "vehicle_exit_count_during" int NOT NULL, "vehicle_count_handover" int NOT NULL, "equipment_handover" nvarchar(250), CONSTRAINT "PK_12f1c45c8e4989313dcaf5c7eb2" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c87fd152a288a6e6be144c6465" ON "shiftHandovers" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "dutySchedules" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_34edb472192b00866534c458a0b" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_4b57662e5b04e3e1b6b4ddcae0c" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "name" nvarchar(250) NOT NULL, "month" int NOT NULL, "year" int NOT NULL, "code_org" nvarchar(17) NOT NULL, "effective_date" datetime NOT NULL, "name_man" nvarchar(250) NOT NULL, "note" nvarchar(500), CONSTRAINT "PK_85cb513f207cce52941a3d26acd" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4dd5e8953af8e259a5cd586e8d" ON "dutySchedules" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "detailDutys" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_d3860c3dab1fd7e903e1cd1448f" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_510cc3371c7bea7ed27ba8eae74" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" bigint NOT NULL IDENTITY(1,1), "duty_schedule_id" int NOT NULL, "day" int NOT NULL, "date" date NOT NULL, "main_id" varchar(36), "assistant_id" varchar(36), "status" bit NOT NULL CONSTRAINT "DF_0aa2b703c4a83dd95db13c03203" DEFAULT 1, "note" nvarchar(2000), CONSTRAINT "PK_29ede952ef4ef00eed97742b203" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_a97e8ad04d51dd04a2184a0e75" ON "detailDutys" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "situationSummaries" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_3ea1ca6804b0379a0f41cf8048b" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_500612d589569698989d8c20db6" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "detail_duty_now_id" bigint NOT NULL, "detail_duty_feature_id" bigint NOT NULL, "total_per" int NOT NULL, "present_per" int NOT NULL, "absent_per" nvarchar(max), "equipment_take_out" nvarchar(2000), "task_main" nvarchar(2000), "task_contain" nvarchar(2000), "task_sudden" nvarchar(2000), "pros_and_cons" nvarchar(2000), "task_continue" nvarchar(2000), "book_handover" nvarchar(2000), "name_command" nvarchar(250), "name_duty_now" nvarchar(250) NOT NULL, "name_duty_feature" nvarchar(250) NOT NULL, "date" datetime2 NOT NULL, "code_org" nvarchar(17) NOT NULL, CONSTRAINT "PK_a83e6203770368d0762fd23312d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_22d7f49ce8b44dc7bb8c06b7b3" ON "situationSummaries" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "files" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_2901752f1d771f97a8bb45cb4c9" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_7437cbd77a8bd7c769d4df7a271" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "name" nvarchar(250) NOT NULL, "thumbnail" nvarchar(500), "type" nvarchar(50) NOT NULL, "key" nvarchar(500) NOT NULL, "mime" nvarchar(250) NOT NULL, "size" int NOT NULL CONSTRAINT "DF_bd90525617447c5e15ce95db270" DEFAULT 0, "report_error_id" int, "user_id" int NOT NULL, CONSTRAINT "PK_6c16b9093a142e0e7613b04a3d9" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_dca2af07a8af68376bb23714df" ON "files" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "handle_errors" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_eb07f717310bff97948eb293f86" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_4fe398f5e844fdf5a429ccc3d44" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "user_id" int NOT NULL, "due_date_start" datetime, "due_date_end" datetime, "content" nvarchar(2000), "status" nvarchar(50) NOT NULL, "report_error_id" int, CONSTRAINT "PK_81b4e6c77a54d02980c598e77d8" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_88101aa5202d95b54ec9b35ec1" ON "handle_errors" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "report_errors" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_ebf57d105fd411f666a70672825" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_9efb382c75eaf625ef6aa85471a" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "user_id" int NOT NULL, "content" nvarchar(2000), "type" nvarchar(250), "time" datetime2, "status" nvarchar(50) NOT NULL, CONSTRAINT "PK_450d1e475e37f1dabecfe8f91da" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_769afd2a110476c32f34b84e25" ON "report_errors" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "quan_so_truc" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_dcd1d39995e6395dddf8cfc84b4" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_e1ceb114f33b9e2d2f373df0cfd" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_9c26601284c89ecfa4305d91458" DEFAULT NEWSEQUENTIALID(), "thang" int NOT NULL, "nam" int NOT NULL, "maDonVi" varchar(17), "quanSoTruc" int NOT NULL, "tongSuatTruc" int NOT NULL, "code" uniqueidentifier, CONSTRAINT "PK_9c26601284c89ecfa4305d91458" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_2fe49fc263a7b3ac03f404dadd" ON "quan_so_truc" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "quan_so_truc_chi_tiet" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_cbcbfa0722fa489e974a68fa959" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_de737844fad91a5fba7b6ec6b01" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_cc1d1e4371ea952fba1b557b638" DEFAULT NEWSEQUENTIALID(), "soLuong" int NOT NULL, "ngay" int NOT NULL, "isTangCuong" bit NOT NULL CONSTRAINT "DF_d2919a153e41fbcc7c0b7e1c396" DEFAULT 0, "idQuanSoTruc" uniqueidentifier NOT NULL, CONSTRAINT "PK_cc1d1e4371ea952fba1b557b638" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_3ef524f791d88a219a91738290" ON "quan_so_truc_chi_tiet" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "identifyCards" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_a9a825cd0119a7c36743f1a6ba8" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_cefa169941d8b6ac43678a4b318" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "guest_id" int NOT NULL, "identification_number" nvarchar(50) NOT NULL, "document_type_id" int NOT NULL, "issue_date" datetime2, "expiry_date" datetime2, "issuing_authority" nvarchar(250), "notes" nvarchar(2000), "images" ntext, CONSTRAINT "PK_7830348c6d505b88b33248dac28" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_6abb550b0a2426ee28cc34746b" ON "identifyCards" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "guests" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_2af8db70ff4b03052f87f57e384" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_c62f2e8b8f191cad7ae0c7c1afa" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "fullName" nvarchar(250) NOT NULL, "date_of_birth" datetime2, "sex_id" int, "nationality" nvarchar(100), "permanent_address" nvarchar(500), "occupation" nvarchar(250), "phone_number" nvarchar(20) NOT NULL, "office" nvarchar(250) NOT NULL, "avatar" text, "notes" nvarchar(2000), CONSTRAINT "PK_4948267e93869ddcc6b340a2c46" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1edb175a16c23b9c95c51d892a" ON "guests" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "pre-registered-guest" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_afc1c3992c15de0f9ed6869b121" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_a7fff8e2e053ea980639f8f3786" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "meet_who_id" varchar(36), "meet_org_code" nvarchar(17), "guest_id" int, "guest_type_id" int NOT NULL, "code_org" nvarchar(17), "guest_code_org" nvarchar(17), "guest_organization_name" nvarchar(250), "check_in_time" datetime2 NOT NULL, "estimate_out_time" datetime2, "status" bit NOT NULL CONSTRAINT "DF_e27897d8eb200fcd4fbefcb0d7b" DEFAULT 0, "purpose_category_id" int NOT NULL, "purpose_of_visit" nvarchar(1000), "group_name" nvarchar(250), "leader_name" nvarchar(250), "leader_position" nvarchar(250), "number_of_visitors" int NOT NULL CONSTRAINT "DF_eeb83e0fa8ed9b21ace7a313948" DEFAULT 1, "number_of_vehicles" int NOT NULL CONSTRAINT "DF_b826439b382e7b62a016ed39d61" DEFAULT 0, "parent_id" int, "guest_card_id" nvarchar(50) NOT NULL, "return_time" datetime2, "duration_inside" int, "notes" nvarchar(1000), "is_leader" bit NOT NULL CONSTRAINT "DF_0dce2ee08c218f5850b699a6e95" DEFAULT 0, CONSTRAINT "PK_88b8fc696137d8c7c5d3ecf4091" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0a63a673c0d67805cc767f69f6" ON "pre-registered-guest" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "pending_guest" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_e0343c4518cbe34db4557999721" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_baae8c10cdde3d0733bc69b7549" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "id_vehicle_log" nvarchar(50), "vehicle_data" nvarchar(2000), "default_image" text, "vehicle_type_id" int, "vehicle_classification_id" int, "etiquette" nvarchar(200), "guard_post_id" int, "guest_card_id" nvarchar(50), "code_org" nvarchar(17), CONSTRAINT "PK_d31e477285cb0875d89f9c1c328" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5b5f7991e7aa8e876101b1b4d2" ON "pending_guest" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "ngay_truc" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_5c1d87d50e9dfd3a1eae2077dfd" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_af6dd9fb71cf1070f65df4aa9a2" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_335f5c88d0db1943c4501b42dec" DEFAULT NEWSEQUENTIALID(), "ngay" int NOT NULL, "thang" int NOT NULL, "nam" int NOT NULL, "tongQuanSo" int NOT NULL, "isTangCuong" bit NOT NULL CONSTRAINT "DF_5eb6844a33352eb42af97dbb6fd" DEFAULT 0, "date" date NOT NULL, CONSTRAINT "PK_335f5c88d0db1943c4501b42dec" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b69e085c345e5d7fd36265b49b" ON "ngay_truc" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "order_type_categories" ("id" varchar(36) NOT NULL, "code" nvarchar(50) NOT NULL, "date_created" datetime2 CONSTRAINT "DF_589bc1929bb4fa44be5d2d8a60f" DEFAULT GETDATE(), "date_updated" datetime2 CONSTRAINT "DF_00dc382e7c5a5a617e8228b5604" DEFAULT GETDATE(), "has_days_on_road" bit CONSTRAINT "DF_5b383d9247fe22cf9a0ec64539c" DEFAULT 0, "is_absent" bit CONSTRAINT "DF_320887b19e14b10a6c91e0e1965" DEFAULT 0, "is_enable" bit CONSTRAINT "DF_a52d7adcc021753f0b3b2d13366" DEFAULT 1, "name" nvarchar(255), "order_number" int, "verify_date" bit CONSTRAINT "DF_8510f49ee3bd4f584caaff25a0a" DEFAULT 0, CONSTRAINT "PK_18b68b826832ed64c5774928cb1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "loai_hinh_truc_chi_tiet" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_463353a785e3b400bc18366fee5" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_c7ee0f1e31e74be2b2340fee54a" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_5314e6408be58b4702c15e70d03" DEFAULT NEWSEQUENTIALID(), "maLoaiHinhTruc" nvarchar(255) NOT NULL, "nhiemVu" nvarchar(255) NOT NULL, "noiDung" nvarchar(255), "stt" int NOT NULL CONSTRAINT "DF_fd27cc11a436c94f4cd427032ab" DEFAULT 0, CONSTRAINT "PK_5314e6408be58b4702c15e70d03" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0e0307500d869d314e458c6dca" ON "loai_hinh_truc_chi_tiet" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "dm_loai_hinh_truc" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_0096faa4319f1558dd4e46371df" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_862446e92886e960f9482e70847" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_eeadf3a3929ccebb4fd1c304f30" DEFAULT NEWSEQUENTIALID(), "ma" varchar(36) NOT NULL, "tenDayDu" nvarchar(255) NOT NULL, "maDonVis" ntext NOT NULL, "chucVus" ntext NOT NULL, "parentId" nvarchar(255), "soLuong" int NOT NULL CONSTRAINT "DF_19e2b981c5686c3d0be1ea56d58" DEFAULT 0, "stt" int NOT NULL CONSTRAINT "DF_88123e8f264ff262792fa91f31a" DEFAULT 0, CONSTRAINT "PK_eeadf3a3929ccebb4fd1c304f30" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_49788986d7c7fb77dc0ba27b80" ON "dm_loai_hinh_truc" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "lich_truc" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_d256104877c4430a1f72c57e115" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_678c9971f83ecb15c833280b22f" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_9f20a71ae752553021452609b15" DEFAULT NEWSEQUENTIALID(), "ten" nvarchar(255) NOT NULL, "ngayKi" date NOT NULL, "thang" int NOT NULL, "nam" int NOT NULL, "thuTruong" nvarchar(255) NOT NULL, "noiDung" nvarchar(255) NOT NULL, "maLoaiHinhTruc" uniqueidentifier, CONSTRAINT "PK_9f20a71ae752553021452609b15" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_20ab7fd0eedd41ae6bfb1ee622" ON "lich_truc" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "errors" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_d7a1e52fe1bd0a09245eecea043" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_614b8a77d41ff3f37acd7ec2907" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "name" nvarchar(500) NOT NULL, "content" nvarchar(2000), "handle_method" nvarchar(2000), "type" nvarchar(250), "status" nvarchar(50) NOT NULL, CONSTRAINT "PK_f1ab2df89a11cd21f48ff90febb" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_e0591022a3ef57611e0a6bb17a" ON "errors" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "cat_lich_truc" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_6891506b67d84367cc8bc8fc251" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_04e76308e1728b952369456a7a9" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_04569f45e736ddc1cc6d973121f" DEFAULT NEWSEQUENTIALID(), "idNgayTruc" uniqueidentifier, "idLichTruc" uniqueidentifier, "maDonVi" varchar(255), "maLoaiHinhTruc" uniqueidentifier, "vai" nvarchar(255) NOT NULL, "eqn" nvarchar(255) NOT NULL, "stt" int NOT NULL, "ghiChu" nvarchar(255) NOT NULL, "code" uniqueidentifier, CONSTRAINT "PK_04569f45e736ddc1cc6d973121f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5aec1e73d1654048eccaa69696" ON "cat_lich_truc" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "cau_hinh_eqn_loai_hinh_truc" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_9881a4716d9b305ca5322681f91" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_3b8d518207a25937dee8f0e3222" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" uniqueidentifier NOT NULL CONSTRAINT "DF_684ce1427d5f6aa5f182d1d00e5" DEFAULT NEWSEQUENTIALID(), "eqn" nvarchar(255) NOT NULL, "maLoaiHinhTruc" nvarchar(255) NOT NULL, "value" nvarchar(255) NOT NULL, "idLyDo" int, CONSTRAINT "PK_684ce1427d5f6aa5f182d1d00e5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b80d6310f0467242ac95c0b752" ON "cau_hinh_eqn_loai_hinh_truc" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "equipHandovers" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_a8d0746f31c8a5229b731afdd70" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_08dd1a6c95a048055fb2963ffce" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "detail_duty_now_id" int NOT NULL, "detail_duty_feature_id" int NOT NULL, "date" datetime2 NOT NULL, "values" nvarchar(2000) NOT NULL, "equipment_situation" nvarchar(2000), "name_duty_now" nvarchar(250) NOT NULL, "name_duty_feature" nvarchar(250) NOT NULL, "code_org" nvarchar(17) NOT NULL, CONSTRAINT "PK_2e3ebe4999f3726b2bfd65d9ad1" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_04c92d6720a4fb0a97fc17ee92" ON "equipHandovers" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "equipments" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_a095202a885d94748276865a416" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_469b8c93fdfbe1c7d979412fba4" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "name" nvarchar(250) NOT NULL, "unit_id" int NOT NULL, "code_org" nvarchar(17) NOT NULL, "count" int NOT NULL, "desc" nvarchar(2000), "status" nvarchar(50) NOT NULL, CONSTRAINT "PK_250348d5d9ae4946bcd634f3e61" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_b12b5479ea20a3c5a29a80eefb" ON "equipments" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "attendanceLogs" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_c771e12bb84d8286b84d6455206" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_e39c5ff393db1a7ffb4b178b897" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "qneid" varchar(36) NOT NULL, "required_start_time" datetime2 NOT NULL, "code_org" nvarchar(17) NOT NULL, "late_duration" int NOT NULL, "reason_text" nvarchar(2000), "approved_by" nvarchar(250) NOT NULL, "approved_at" datetime2 NOT NULL, "location_check" nvarchar(500) NOT NULL, "check_type_id" int NOT NULL, "attendance_image" nvarchar(MAX), "check_in_time" datetime2 NOT NULL, "check_date" datetime2 NOT NULL, "status" varchar(50), "biometricData" nvarchar(MAX), CONSTRAINT "PK_bdbf2a64e872a60b02b25ae693c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_1c8b0d2db248800cab41bba7ee" ON "attendanceLogs" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "absences" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_d9d29a8beb5c06c3298ea70a1e1" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_a7870cf443a678b46304d0a1b92" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "qneid" varchar(36) NOT NULL, "start_date" datetime2 NOT NULL, "end_date" datetime2 NOT NULL, "absence_type_id" int NOT NULL, "note" nvarchar(2000), "code_org" nvarchar(100) NOT NULL, "approved_by" nvarchar(250) NOT NULL, "approved_at" datetime2 NOT NULL, CONSTRAINT "PK_bd79346866fea8ac6f269252748" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c10cbccb565297138b24dedf24" ON "absences" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "guestLogs" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_4b5b9cc581bf4af73b4bf776ab3" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_14290258a3419790b20d5cb4349" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "id_vehicle_log" nvarchar(50), "vehicle_data" nvarchar(255), "meet_who_id" varchar(36), "meet_org_code" nvarchar(17), "guest_id" int, "guest_type_id" int NOT NULL, "code_org" nvarchar(17), "guest_code_org" nvarchar(17), "guest_organization_name" nvarchar(250), "is_civil" bit NOT NULL CONSTRAINT "DF_ecb9b799671a04be901d9dad705" DEFAULT 0, "check_in_time" datetime2 NOT NULL, "check_out_time" datetime2, "estimate_out_time" datetime2, "default_image" text, "vehicle_type_id" int, "vehicle_classification_id" int, "etiquette" nvarchar(200), "guard_post_id" int, "status_id" int NOT NULL, "purpose_category_id" int NOT NULL, "purpose_of_visit" nvarchar(1000), "group_name" nvarchar(250), "leader_name" nvarchar(250), "leader_position" nvarchar(250), "number_of_visitors" int NOT NULL CONSTRAINT "DF_f59656bbbd8b8418113d74a89de" DEFAULT 1, "number_of_vehicles" int NOT NULL CONSTRAINT "DF_080a87ea288372cd2628da27a00" DEFAULT 0, "number_of_visitors_out" int CONSTRAINT "DF_d3f589c400ad216a962bdb36087" DEFAULT 0, "parent_id" int, "duration_inside" int, "guest_card_id" nvarchar(50), "issued_time" datetime2, "return_time" datetime2, "detail_duty_id" bigint, "detail_duty_out_id" bigint, "guest_id_by" varchar(36), "notes" nvarchar(1000), "is_leader" bit NOT NULL CONSTRAINT "DF_c900218716e7db0c0a543ee0228" DEFAULT 0, "is_send_message_for_meet_user" bit NOT NULL CONSTRAINT "DF_c592f054b44140513edd18a9cc4" DEFAULT 0, CONSTRAINT "PK_c8a8ecec1c2e4634dc6441c152b" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8b9d9110f57296c02891be69e9" ON "guestLogs" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "configShifts" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_ed390d5cfa8199b71bd5456a911" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_88276bb75553e2454c948662b48" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "start_time" nvarchar(255), "end_time" nvarchar(255), "code_org" nvarchar(17) NOT NULL, "order_num" int NOT NULL, CONSTRAINT "PK_f16ecd6146aba55bead8450701d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_673fb4e0bc8a30f59a12af52cf" ON "configShifts" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "daily_stats" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_47b5fd1c74c4e74743c6a74ee8e" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_0cb86c1003ea6797d890eaa40a9" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" int NOT NULL IDENTITY(1,1), "report_date" datetime2 NOT NULL, "org_id" uniqueidentifier NOT NULL, "code_org" nvarchar(17) NOT NULL CONSTRAINT "DF_56c3bd22acb2dd6dd2adb2f8d66" DEFAULT '', "total_count" int NOT NULL CONSTRAINT "DF_670fe9ee3804e2df7b2dd19c8a8" DEFAULT 0, "dung_gio_count" int NOT NULL CONSTRAINT "DF_cdbd9b7516036bc45d94cfde6dd" DEFAULT 0, "khong_cham_count" int NOT NULL CONSTRAINT "DF_8005eb7955746be3cf50b80c221" DEFAULT 0, "di_muon_count" int NOT NULL CONSTRAINT "DF_16e7127ad905760e7717b2ebc83" DEFAULT 0, "vang_co_ly_do_count" int NOT NULL CONSTRAINT "DF_22e8253e37cf6e0b3c3df5e9ec8" DEFAULT 0, "nghi_phep_count" int NOT NULL CONSTRAINT "DF_441b11c47bb8e9d32d9e26284e3" DEFAULT 0, "note" nvarchar(2000), CONSTRAINT "PK_d1830b57aa5fafc5cb26a09aa73" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_c2843c10a4fbd93c12e255c5d7" ON "daily_stats" ("fullText") `,
    );
    await queryRunner.query(
      `CREATE TABLE "permissions_closure" ("id_ancestor" int NOT NULL, "id_descendant" int NOT NULL, CONSTRAINT "PK_5a6112b449a9eab8f57a6953378" PRIMARY KEY ("id_ancestor", "id_descendant"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_7c9fe505a03ac0ac2f46661e91" ON "permissions_closure" ("id_ancestor") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_f6966c45c60a0fa083adcbe78b" ON "permissions_closure" ("id_descendant") `,
    );
    await queryRunner.query(
      `ALTER TABLE "user-guides" ADD CONSTRAINT "FK_afadfa9deceb03b5c5cc235a318" FOREIGN KEY ("parentSlug") REFERENCES "user-guides"("slug") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user-guides" ADD CONSTRAINT "FK_44ca5e998f057d503498fe67c0c" FOREIGN KEY ("slug") REFERENCES "user-guide-content"("slug") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permissions" ADD CONSTRAINT "FK_3ddb4a6abeb322e7350ef11acc2" FOREIGN KEY ("parentId") REFERENCES "permissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles_permissions" ADD CONSTRAINT "FK_28bf280551eb9aa82daf1e156d9" FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles_permissions" ADD CONSTRAINT "FK_31cf5c31d0096f706e3ba3b1e82" FOREIGN KEY ("permissionId") REFERENCES "permissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" ADD CONSTRAINT "FK_7f95c7acb4acc961775d03170c9" FOREIGN KEY ("orgCode") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" ADD CONSTRAINT "FK_9863dc6eaba137e458cb589e00e" FOREIGN KEY ("reinforcement_org_id") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" ADD CONSTRAINT "FK_c28e2464cbbcbc656f9bfa1663f" FOREIGN KEY ("type_id") REFERENCES "military_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" ADD CONSTRAINT "FK_d00712933e6aa52d5f49b5d690d" FOREIGN KEY ("position_id") REFERENCES "position_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" ADD CONSTRAINT "FK_69a9cef465bf22e1ae8d9e93767" FOREIGN KEY ("rank_id") REFERENCES "military_ranks"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "organizationGroup" ADD CONSTRAINT "FK_af657895a5264ee14a00cb78b0a" FOREIGN KEY ("createdById") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "organizationGroup" ADD CONSTRAINT "FK_cd41aeb3e38e718271df70788a1" FOREIGN KEY ("updatedById") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "organizations" ADD CONSTRAINT "FK_b9e8b8e8d88eed668c3e6e69d3c" FOREIGN KEY ("parentId") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "organizations" ADD CONSTRAINT "FK_9e7dffd38204e6cc79d39761170" FOREIGN KEY ("orgGroupId") REFERENCES "organizationGroup"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "logs" ADD CONSTRAINT "FK_ce2786a93676c1cba0e5ef55b8a" FOREIGN KEY ("typeId") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "logs" ADD CONSTRAINT "FK_a1196a1956403417fe3a0343390" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_d381e6d02fed1105f165d3084e8" FOREIGN KEY ("manageOrgCode") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_9ee45b55a202061808728f42ad8" FOREIGN KEY ("eQNId") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_957a42fe93ba8b07e2b95a73a5c" FOREIGN KEY ("rankId") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_7d5b477d15c01b6b44f1b4e8cc4" FOREIGN KEY ("positionId") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_f3d6aea8fcca58182b2e80ce979" FOREIGN KEY ("organizationId") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD CONSTRAINT "FK_368e146b785b574f42ae9e53d5e" FOREIGN KEY ("roleId") REFERENCES "roles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftInspections" ADD CONSTRAINT "FK_cc85d96e62ecbdc5451e8783a79" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftInspections" ADD CONSTRAINT "FK_a037adf63ff6060cbc0c5b5ff2a" FOREIGN KEY ("guard_shift_id") REFERENCES "guardShifts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guardShifts" ADD CONSTRAINT "FK_011754c410a53c582ab8c278cc8" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guardPosts" ADD CONSTRAINT "FK_dda7dac029b0fe249bec7adab41" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftStaff" ADD CONSTRAINT "FK_3ba5be405942382558cb50781bf" FOREIGN KEY ("shift_post_id") REFERENCES "shiftPosts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftStaff" ADD CONSTRAINT "FK_bc778d1975b59cbe4568e46db49" FOREIGN KEY ("qneid") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftPosts" ADD CONSTRAINT "FK_f1365691be294e05ce73f65c563" FOREIGN KEY ("guard_shift_id") REFERENCES "guardShifts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftPosts" ADD CONSTRAINT "FK_6868b5ee47eb93f8700cb712e95" FOREIGN KEY ("guard_post_id") REFERENCES "guardPosts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "workingSchedules" ADD CONSTRAINT "FK_ab934f35b453d6ef1ff83fbfe5d" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "dutySchedules" ADD CONSTRAINT "FK_6e85a709f9c5052c4a7a344e9e1" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "detailDutys" ADD CONSTRAINT "FK_88bf70e393e26c4eeee85cbdec9" FOREIGN KEY ("main_id") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "detailDutys" ADD CONSTRAINT "FK_79e12c38e5f275cb12a04559b8e" FOREIGN KEY ("assistant_id") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "detailDutys" ADD CONSTRAINT "FK_a6402993216726b0ca8b1675d83" FOREIGN KEY ("duty_schedule_id") REFERENCES "dutySchedules"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD CONSTRAINT "FK_695f5ceab2fbe28b772ef23a74f" FOREIGN KEY ("detail_duty_now_id") REFERENCES "detailDutys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD CONSTRAINT "FK_6db52086f2363bf60584eee49a4" FOREIGN KEY ("detail_duty_feature_id") REFERENCES "detailDutys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD CONSTRAINT "FK_c03d7e079d4d58cb8f9a41e4f74" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "files" ADD CONSTRAINT "FK_307538019be924095f9211f51f3" FOREIGN KEY ("report_error_id") REFERENCES "report_errors"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "files" ADD CONSTRAINT "FK_a7435dbb7583938d5e7d1376041" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "report_errors" ADD CONSTRAINT "FK_761d5b2b45e5c39f5f2fd0a2223" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "quan_so_truc" ADD CONSTRAINT "FK_0129a556dafc98460ec919eaeb9" FOREIGN KEY ("code") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "quan_so_truc_chi_tiet" ADD CONSTRAINT "FK_766d04eb28414325a608906d3ac" FOREIGN KEY ("idQuanSoTruc") REFERENCES "quan_so_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "identifyCards" ADD CONSTRAINT "FK_adffa4fc997348a504c8e5e7d02" FOREIGN KEY ("document_type_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "identifyCards" ADD CONSTRAINT "FK_6dcc36738c2269a005de2f5c659" FOREIGN KEY ("guest_id") REFERENCES "guests"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guests" ADD CONSTRAINT "FK_6bf73685d29eb922c823a0d5013" FOREIGN KEY ("sex_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" ADD CONSTRAINT "FK_3ec1b0281f252b36b2a74bd7e67" FOREIGN KEY ("meet_who_id") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" ADD CONSTRAINT "FK_141e0366e1d2cc80e52867dfd1c" FOREIGN KEY ("meet_org_code") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" ADD CONSTRAINT "FK_ed32de4efdad4844819b68c2520" FOREIGN KEY ("guest_id") REFERENCES "guests"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" ADD CONSTRAINT "FK_fb8f53d81c5074bc93fed7d26ae" FOREIGN KEY ("guest_type_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" ADD CONSTRAINT "FK_d099e91cb6be067b5810f38f8d3" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" ADD CONSTRAINT "FK_d208e0847d9e67bf51f5ac0098a" FOREIGN KEY ("guest_code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" ADD CONSTRAINT "FK_a3ae3e7b466309a74919dfc07e3" FOREIGN KEY ("purpose_category_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" ADD CONSTRAINT "FK_7a577439ad0b3c5e389ede013bb" FOREIGN KEY ("parent_id") REFERENCES "pre-registered-guest"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pending_guest" ADD CONSTRAINT "FK_293512a8b18156aee9b5af038bf" FOREIGN KEY ("vehicle_type_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pending_guest" ADD CONSTRAINT "FK_2df19032c2d8bb611a7283fea4f" FOREIGN KEY ("vehicle_classification_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pending_guest" ADD CONSTRAINT "FK_c9cdbdd1af1d2cd3c0d6bca1046" FOREIGN KEY ("guard_post_id") REFERENCES "guardPosts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "pending_guest" ADD CONSTRAINT "FK_8a14eb696b0238411e4cdab8be1" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "lich_truc" ADD CONSTRAINT "FK_63ea8f13039740e85aad2906bd1" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cat_lich_truc" ADD CONSTRAINT "FK_4c693bc922703973c9726c94607" FOREIGN KEY ("idNgayTruc") REFERENCES "ngay_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cat_lich_truc" ADD CONSTRAINT "FK_24e567771fe6d9637534fb9966f" FOREIGN KEY ("idLichTruc") REFERENCES "lich_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cat_lich_truc" ADD CONSTRAINT "FK_3dacddf8275cc0b7fe414049480" FOREIGN KEY ("code") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cat_lich_truc" ADD CONSTRAINT "FK_b3841ca32334195395e086c40a5" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" ADD CONSTRAINT "FK_8fa33cede531e97d3c623f692ad" FOREIGN KEY ("idLyDo") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "equipHandovers" ADD CONSTRAINT "FK_e6ce700441beb9568136fe29734" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "equipments" ADD CONSTRAINT "FK_37927340bf23bf15544fab266c9" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "equipments" ADD CONSTRAINT "FK_482c1675c4703236cc8347db231" FOREIGN KEY ("unit_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "attendanceLogs" ADD CONSTRAINT "FK_468f267ab0fc1377705fcf70f24" FOREIGN KEY ("qneid") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "attendanceLogs" ADD CONSTRAINT "FK_a69a6173ba26b5280bbb85f2512" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "absences" ADD CONSTRAINT "FK_ce75c439daf7ff855b8e3655a37" FOREIGN KEY ("absence_type_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_8a7b0dd9674c6370a74586a85e4" FOREIGN KEY ("meet_who_id") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_b253acb07baf74e5e5fd34306d1" FOREIGN KEY ("meet_org_code") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_8446b9203af70cbb90e22aac9ff" FOREIGN KEY ("guest_id") REFERENCES "guests"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_9f260a2024988695aa2677ec1f8" FOREIGN KEY ("guest_type_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_c661f9f450b38c43697a05c9e89" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_eea52fc042e44684f941399f15b" FOREIGN KEY ("guest_code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_993736c0461b7e884b0e2b8cba7" FOREIGN KEY ("vehicle_type_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_214fda90da2322233c205cf6744" FOREIGN KEY ("vehicle_classification_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_0d7c6209dcd83f81f4170ed2905" FOREIGN KEY ("guard_post_id") REFERENCES "guardPosts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_b7b9d316f4ade25667dfb2b03d5" FOREIGN KEY ("status_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_b498af35e68790310fdf1b87f25" FOREIGN KEY ("purpose_category_id") REFERENCES "types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_4a7e1ee4e3344c0f06454a7fc70" FOREIGN KEY ("parent_id") REFERENCES "guestLogs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_7ee12dcdc9f2bc35d74ec160e65" FOREIGN KEY ("detail_duty_id") REFERENCES "detailDutys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_c0342b8979509156404d3e61ae0" FOREIGN KEY ("detail_duty_out_id") REFERENCES "detailDutys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_b8d743285e1dc1cb571f7cb6814" FOREIGN KEY ("guest_id_by") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "configShifts" ADD CONSTRAINT "FK_d4e997a60148ce1dbba041a3349" FOREIGN KEY ("code_org") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "daily_stats" ADD CONSTRAINT "FK_b42e81d81e56c712032f3cfc206" FOREIGN KEY ("org_id") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permissions_closure" ADD CONSTRAINT "FK_7c9fe505a03ac0ac2f46661e918" FOREIGN KEY ("id_ancestor") REFERENCES "permissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "permissions_closure" ADD CONSTRAINT "FK_f6966c45c60a0fa083adcbe78be" FOREIGN KEY ("id_descendant") REFERENCES "permissions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "permissions_closure" DROP CONSTRAINT "FK_f6966c45c60a0fa083adcbe78be"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permissions_closure" DROP CONSTRAINT "FK_7c9fe505a03ac0ac2f46661e918"`,
    );
    await queryRunner.query(
      `ALTER TABLE "daily_stats" DROP CONSTRAINT "FK_b42e81d81e56c712032f3cfc206"`,
    );
    await queryRunner.query(
      `ALTER TABLE "configShifts" DROP CONSTRAINT "FK_d4e997a60148ce1dbba041a3349"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_b8d743285e1dc1cb571f7cb6814"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_c0342b8979509156404d3e61ae0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_7ee12dcdc9f2bc35d74ec160e65"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_4a7e1ee4e3344c0f06454a7fc70"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_b498af35e68790310fdf1b87f25"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_b7b9d316f4ade25667dfb2b03d5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_0d7c6209dcd83f81f4170ed2905"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_214fda90da2322233c205cf6744"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_993736c0461b7e884b0e2b8cba7"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_eea52fc042e44684f941399f15b"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_c661f9f450b38c43697a05c9e89"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_9f260a2024988695aa2677ec1f8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_8446b9203af70cbb90e22aac9ff"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_b253acb07baf74e5e5fd34306d1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_8a7b0dd9674c6370a74586a85e4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "absences" DROP CONSTRAINT "FK_ce75c439daf7ff855b8e3655a37"`,
    );
    await queryRunner.query(
      `ALTER TABLE "attendanceLogs" DROP CONSTRAINT "FK_a69a6173ba26b5280bbb85f2512"`,
    );
    await queryRunner.query(
      `ALTER TABLE "attendanceLogs" DROP CONSTRAINT "FK_468f267ab0fc1377705fcf70f24"`,
    );
    await queryRunner.query(
      `ALTER TABLE "equipments" DROP CONSTRAINT "FK_482c1675c4703236cc8347db231"`,
    );
    await queryRunner.query(
      `ALTER TABLE "equipments" DROP CONSTRAINT "FK_37927340bf23bf15544fab266c9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "equipHandovers" DROP CONSTRAINT "FK_e6ce700441beb9568136fe29734"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" DROP CONSTRAINT "FK_8fa33cede531e97d3c623f692ad"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cat_lich_truc" DROP CONSTRAINT "FK_b3841ca32334195395e086c40a5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cat_lich_truc" DROP CONSTRAINT "FK_3dacddf8275cc0b7fe414049480"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cat_lich_truc" DROP CONSTRAINT "FK_24e567771fe6d9637534fb9966f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "cat_lich_truc" DROP CONSTRAINT "FK_4c693bc922703973c9726c94607"`,
    );
    await queryRunner.query(
      `ALTER TABLE "lich_truc" DROP CONSTRAINT "FK_63ea8f13039740e85aad2906bd1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pending_guest" DROP CONSTRAINT "FK_8a14eb696b0238411e4cdab8be1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pending_guest" DROP CONSTRAINT "FK_c9cdbdd1af1d2cd3c0d6bca1046"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pending_guest" DROP CONSTRAINT "FK_2df19032c2d8bb611a7283fea4f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pending_guest" DROP CONSTRAINT "FK_293512a8b18156aee9b5af038bf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" DROP CONSTRAINT "FK_7a577439ad0b3c5e389ede013bb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" DROP CONSTRAINT "FK_a3ae3e7b466309a74919dfc07e3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" DROP CONSTRAINT "FK_d208e0847d9e67bf51f5ac0098a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" DROP CONSTRAINT "FK_d099e91cb6be067b5810f38f8d3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" DROP CONSTRAINT "FK_fb8f53d81c5074bc93fed7d26ae"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" DROP CONSTRAINT "FK_ed32de4efdad4844819b68c2520"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" DROP CONSTRAINT "FK_141e0366e1d2cc80e52867dfd1c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "pre-registered-guest" DROP CONSTRAINT "FK_3ec1b0281f252b36b2a74bd7e67"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guests" DROP CONSTRAINT "FK_6bf73685d29eb922c823a0d5013"`,
    );
    await queryRunner.query(
      `ALTER TABLE "identifyCards" DROP CONSTRAINT "FK_6dcc36738c2269a005de2f5c659"`,
    );
    await queryRunner.query(
      `ALTER TABLE "identifyCards" DROP CONSTRAINT "FK_adffa4fc997348a504c8e5e7d02"`,
    );
    await queryRunner.query(
      `ALTER TABLE "quan_so_truc_chi_tiet" DROP CONSTRAINT "FK_766d04eb28414325a608906d3ac"`,
    );
    await queryRunner.query(
      `ALTER TABLE "quan_so_truc" DROP CONSTRAINT "FK_0129a556dafc98460ec919eaeb9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "report_errors" DROP CONSTRAINT "FK_761d5b2b45e5c39f5f2fd0a2223"`,
    );
    await queryRunner.query(
      `ALTER TABLE "files" DROP CONSTRAINT "FK_a7435dbb7583938d5e7d1376041"`,
    );
    await queryRunner.query(
      `ALTER TABLE "files" DROP CONSTRAINT "FK_307538019be924095f9211f51f3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP CONSTRAINT "FK_c03d7e079d4d58cb8f9a41e4f74"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP CONSTRAINT "FK_6db52086f2363bf60584eee49a4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP CONSTRAINT "FK_695f5ceab2fbe28b772ef23a74f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "detailDutys" DROP CONSTRAINT "FK_a6402993216726b0ca8b1675d83"`,
    );
    await queryRunner.query(
      `ALTER TABLE "detailDutys" DROP CONSTRAINT "FK_79e12c38e5f275cb12a04559b8e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "detailDutys" DROP CONSTRAINT "FK_88bf70e393e26c4eeee85cbdec9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "dutySchedules" DROP CONSTRAINT "FK_6e85a709f9c5052c4a7a344e9e1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "workingSchedules" DROP CONSTRAINT "FK_ab934f35b453d6ef1ff83fbfe5d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftPosts" DROP CONSTRAINT "FK_6868b5ee47eb93f8700cb712e95"`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftPosts" DROP CONSTRAINT "FK_f1365691be294e05ce73f65c563"`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftStaff" DROP CONSTRAINT "FK_bc778d1975b59cbe4568e46db49"`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftStaff" DROP CONSTRAINT "FK_3ba5be405942382558cb50781bf"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guardPosts" DROP CONSTRAINT "FK_dda7dac029b0fe249bec7adab41"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guardShifts" DROP CONSTRAINT "FK_011754c410a53c582ab8c278cc8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftInspections" DROP CONSTRAINT "FK_a037adf63ff6060cbc0c5b5ff2a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "shiftInspections" DROP CONSTRAINT "FK_cc85d96e62ecbdc5451e8783a79"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_368e146b785b574f42ae9e53d5e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_f3d6aea8fcca58182b2e80ce979"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_7d5b477d15c01b6b44f1b4e8cc4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_957a42fe93ba8b07e2b95a73a5c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_9ee45b55a202061808728f42ad8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" DROP CONSTRAINT "FK_d381e6d02fed1105f165d3084e8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "logs" DROP CONSTRAINT "FK_a1196a1956403417fe3a0343390"`,
    );
    await queryRunner.query(
      `ALTER TABLE "logs" DROP CONSTRAINT "FK_ce2786a93676c1cba0e5ef55b8a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "organizations" DROP CONSTRAINT "FK_9e7dffd38204e6cc79d39761170"`,
    );
    await queryRunner.query(
      `ALTER TABLE "organizations" DROP CONSTRAINT "FK_b9e8b8e8d88eed668c3e6e69d3c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "organizationGroup" DROP CONSTRAINT "FK_cd41aeb3e38e718271df70788a1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "organizationGroup" DROP CONSTRAINT "FK_af657895a5264ee14a00cb78b0a"`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" DROP CONSTRAINT "FK_69a9cef465bf22e1ae8d9e93767"`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" DROP CONSTRAINT "FK_d00712933e6aa52d5f49b5d690d"`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" DROP CONSTRAINT "FK_c28e2464cbbcbc656f9bfa1663f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" DROP CONSTRAINT "FK_9863dc6eaba137e458cb589e00e"`,
    );
    await queryRunner.query(
      `ALTER TABLE "eQN" DROP CONSTRAINT "FK_7f95c7acb4acc961775d03170c9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles_permissions" DROP CONSTRAINT "FK_31cf5c31d0096f706e3ba3b1e82"`,
    );
    await queryRunner.query(
      `ALTER TABLE "roles_permissions" DROP CONSTRAINT "FK_28bf280551eb9aa82daf1e156d9"`,
    );
    await queryRunner.query(
      `ALTER TABLE "permissions" DROP CONSTRAINT "FK_3ddb4a6abeb322e7350ef11acc2"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user-guides" DROP CONSTRAINT "FK_44ca5e998f057d503498fe67c0c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user-guides" DROP CONSTRAINT "FK_afadfa9deceb03b5c5cc235a318"`,
    );
    await queryRunner.query(
      `DROP INDEX "IDX_f6966c45c60a0fa083adcbe78b" ON "permissions_closure"`,
    );
    await queryRunner.query(
      `DROP INDEX "IDX_7c9fe505a03ac0ac2f46661e91" ON "permissions_closure"`,
    );
    await queryRunner.query(`DROP TABLE "permissions_closure"`);
    await queryRunner.query(
      `DROP INDEX "IDX_c2843c10a4fbd93c12e255c5d7" ON "daily_stats"`,
    );
    await queryRunner.query(`DROP TABLE "daily_stats"`);
    await queryRunner.query(
      `DROP INDEX "IDX_673fb4e0bc8a30f59a12af52cf" ON "configShifts"`,
    );
    await queryRunner.query(`DROP TABLE "configShifts"`);
    await queryRunner.query(
      `DROP INDEX "IDX_8b9d9110f57296c02891be69e9" ON "guestLogs"`,
    );
    await queryRunner.query(`DROP TABLE "guestLogs"`);
    await queryRunner.query(
      `DROP INDEX "IDX_c10cbccb565297138b24dedf24" ON "absences"`,
    );
    await queryRunner.query(`DROP TABLE "absences"`);
    await queryRunner.query(
      `DROP INDEX "IDX_1c8b0d2db248800cab41bba7ee" ON "attendanceLogs"`,
    );
    await queryRunner.query(`DROP TABLE "attendanceLogs"`);
    await queryRunner.query(
      `DROP INDEX "IDX_b12b5479ea20a3c5a29a80eefb" ON "equipments"`,
    );
    await queryRunner.query(`DROP TABLE "equipments"`);
    await queryRunner.query(
      `DROP INDEX "IDX_04c92d6720a4fb0a97fc17ee92" ON "equipHandovers"`,
    );
    await queryRunner.query(`DROP TABLE "equipHandovers"`);
    await queryRunner.query(
      `DROP INDEX "IDX_b80d6310f0467242ac95c0b752" ON "cau_hinh_eqn_loai_hinh_truc"`,
    );
    await queryRunner.query(`DROP TABLE "cau_hinh_eqn_loai_hinh_truc"`);
    await queryRunner.query(
      `DROP INDEX "IDX_5aec1e73d1654048eccaa69696" ON "cat_lich_truc"`,
    );
    await queryRunner.query(`DROP TABLE "cat_lich_truc"`);
    await queryRunner.query(
      `DROP INDEX "IDX_e0591022a3ef57611e0a6bb17a" ON "errors"`,
    );
    await queryRunner.query(`DROP TABLE "errors"`);
    await queryRunner.query(
      `DROP INDEX "IDX_20ab7fd0eedd41ae6bfb1ee622" ON "lich_truc"`,
    );
    await queryRunner.query(`DROP TABLE "lich_truc"`);
    await queryRunner.query(
      `DROP INDEX "IDX_49788986d7c7fb77dc0ba27b80" ON "dm_loai_hinh_truc"`,
    );
    await queryRunner.query(`DROP TABLE "dm_loai_hinh_truc"`);
    await queryRunner.query(
      `DROP INDEX "IDX_0e0307500d869d314e458c6dca" ON "loai_hinh_truc_chi_tiet"`,
    );
    await queryRunner.query(`DROP TABLE "loai_hinh_truc_chi_tiet"`);
    await queryRunner.query(`DROP TABLE "order_type_categories"`);
    await queryRunner.query(
      `DROP INDEX "IDX_b69e085c345e5d7fd36265b49b" ON "ngay_truc"`,
    );
    await queryRunner.query(`DROP TABLE "ngay_truc"`);
    await queryRunner.query(
      `DROP INDEX "IDX_5b5f7991e7aa8e876101b1b4d2" ON "pending_guest"`,
    );
    await queryRunner.query(`DROP TABLE "pending_guest"`);
    await queryRunner.query(
      `DROP INDEX "IDX_0a63a673c0d67805cc767f69f6" ON "pre-registered-guest"`,
    );
    await queryRunner.query(`DROP TABLE "pre-registered-guest"`);
    await queryRunner.query(
      `DROP INDEX "IDX_1edb175a16c23b9c95c51d892a" ON "guests"`,
    );
    await queryRunner.query(`DROP TABLE "guests"`);
    await queryRunner.query(
      `DROP INDEX "IDX_6abb550b0a2426ee28cc34746b" ON "identifyCards"`,
    );
    await queryRunner.query(`DROP TABLE "identifyCards"`);
    await queryRunner.query(
      `DROP INDEX "IDX_3ef524f791d88a219a91738290" ON "quan_so_truc_chi_tiet"`,
    );
    await queryRunner.query(`DROP TABLE "quan_so_truc_chi_tiet"`);
    await queryRunner.query(
      `DROP INDEX "IDX_2fe49fc263a7b3ac03f404dadd" ON "quan_so_truc"`,
    );
    await queryRunner.query(`DROP TABLE "quan_so_truc"`);
    await queryRunner.query(
      `DROP INDEX "IDX_769afd2a110476c32f34b84e25" ON "report_errors"`,
    );
    await queryRunner.query(`DROP TABLE "report_errors"`);
    await queryRunner.query(
      `DROP INDEX "IDX_88101aa5202d95b54ec9b35ec1" ON "handle_errors"`,
    );
    await queryRunner.query(`DROP TABLE "handle_errors"`);
    await queryRunner.query(
      `DROP INDEX "IDX_dca2af07a8af68376bb23714df" ON "files"`,
    );
    await queryRunner.query(`DROP TABLE "files"`);
    await queryRunner.query(
      `DROP INDEX "IDX_22d7f49ce8b44dc7bb8c06b7b3" ON "situationSummaries"`,
    );
    await queryRunner.query(`DROP TABLE "situationSummaries"`);
    await queryRunner.query(
      `DROP INDEX "IDX_a97e8ad04d51dd04a2184a0e75" ON "detailDutys"`,
    );
    await queryRunner.query(`DROP TABLE "detailDutys"`);
    await queryRunner.query(
      `DROP INDEX "IDX_4dd5e8953af8e259a5cd586e8d" ON "dutySchedules"`,
    );
    await queryRunner.query(`DROP TABLE "dutySchedules"`);
    await queryRunner.query(
      `DROP INDEX "IDX_c87fd152a288a6e6be144c6465" ON "shiftHandovers"`,
    );
    await queryRunner.query(`DROP TABLE "shiftHandovers"`);
    await queryRunner.query(
      `DROP INDEX "IDX_e1dcc6a62c42b4a7c50688fe0f" ON "workingSchedules"`,
    );
    await queryRunner.query(`DROP TABLE "workingSchedules"`);
    await queryRunner.query(
      `DROP INDEX "IDX_b1a63054a0e8767eb59d92a09d" ON "shiftPosts"`,
    );
    await queryRunner.query(`DROP TABLE "shiftPosts"`);
    await queryRunner.query(
      `DROP INDEX "IDX_56beba5e6ea00c4f448fc3e10c" ON "shiftStaff"`,
    );
    await queryRunner.query(`DROP TABLE "shiftStaff"`);
    await queryRunner.query(
      `DROP INDEX "IDX_ac4fd287bf653006f07bc02e25" ON "guardPosts"`,
    );
    await queryRunner.query(`DROP TABLE "guardPosts"`);
    await queryRunner.query(
      `DROP INDEX "IDX_028b624b79338071e8f067ab7c" ON "guardShifts"`,
    );
    await queryRunner.query(`DROP TABLE "guardShifts"`);
    await queryRunner.query(
      `DROP INDEX "IDX_8675f1b6e332930b1767ec2f79" ON "shiftInspections"`,
    );
    await queryRunner.query(`DROP TABLE "shiftInspections"`);
    await queryRunner.query(
      `DROP INDEX "IDX_450a05c0c4de5b75ac8d34835b" ON "users"`,
    );
    await queryRunner.query(
      `DROP INDEX "IDX_d7b08ee886bf7dc996b38c5d6a" ON "users"`,
    );
    await queryRunner.query(`DROP TABLE "users"`);
    await queryRunner.query(
      `DROP INDEX "IDX_6d8b2172ce6e6ec75dec3613ce" ON "logs"`,
    );
    await queryRunner.query(`DROP TABLE "logs"`);
    await queryRunner.query(
      `DROP INDEX "IDX_6613a1e72563714795d764971c" ON "types"`,
    );
    await queryRunner.query(`DROP TABLE "types"`);
    await queryRunner.query(
      `DROP INDEX "IDX_e3ce0230c4fac41ac62dd583eb" ON "organizations"`,
    );
    await queryRunner.query(`DROP TABLE "organizations"`);
    await queryRunner.query(
      `DROP INDEX "IDX_74f9e67092b539d9ed96def730" ON "organizationGroup"`,
    );
    await queryRunner.query(`DROP TABLE "organizationGroup"`);
    await queryRunner.query(`DROP TABLE "eQN"`);
    await queryRunner.query(`DROP TABLE "military_ranks"`);
    await queryRunner.query(`DROP TABLE "position_categories"`);
    await queryRunner.query(`DROP TABLE "military_types"`);
    await queryRunner.query(
      `DROP INDEX "IDX_beefd8342da401788c55687e22" ON "roles"`,
    );
    await queryRunner.query(`DROP TABLE "roles"`);
    await queryRunner.query(`DROP TABLE "roles_permissions"`);
    await queryRunner.query(
      `DROP INDEX "IDX_68489757328f76f1986dfe08c2" ON "permissions"`,
    );
    await queryRunner.query(`DROP TABLE "permissions"`);
    await queryRunner.query(`DROP TABLE "user-guides"`);
    await queryRunner.query(`DROP TABLE "user-guide-content"`);
  }
}
