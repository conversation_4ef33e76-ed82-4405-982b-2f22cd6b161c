# Use an official Node.js runtime as a parent image
FROM node:18

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . .

# Install the app dependencies from package.json
RUN npm install

# Build App
RUN npm run build

# Expose the app on a specific port (default to 3000)
EXPOSE 50572

# Command to run the app
CMD ["npm", "start"]
