import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import {
  createUserGuide,
  deleteUserGuideBySlug,
  exportUserGuide,
  findTreeUserGuide,
  findUserGuideBySlug,
  updateUserGuide,
} from './user-guide.service';
import { IApiError } from '../types/validation';
import { UserGuide } from '../models';
import jwt from 'jsonwebtoken';
import config from '../config';

export const postUserGuide = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const userGuide = req.body;
    const getObj = await createUserGuide(userGuide);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const getUserGuideBySlug = async (
  req: Request & { params: { slug: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const slug = req.params.slug;
    const getObj = await findUserGuideBySlug(slug);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      const error: IApiError = {
        code: ERROR.DATA_NOT_FOUND,
        message: 'Hướng dẫn không tồn tại',
        statusCode: status.BAD_REQUEST,
        errors: { field: 'slug', message: 'slug không tồn tại' },
      };
      return res.status(status.BAD_REQUEST).json(error);
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const putUserGuide = async (
  req: Request & { params: { slug: string } } & { body: UserGuide },
  res: Response,
  next: NextFunction,
) => {
  try {
    const slug = req.params.slug;
    const userGuide = req.body;
    const getObj = await updateUserGuide(slug, userGuide);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      const error: IApiError = {
        code: ERROR.DATA_NOT_FOUND,
        message: 'Mạng LL không tồn tại',
        statusCode: status.BAD_REQUEST,
        errors: { field: 'id', message: 'Id không tồn tại' },
      };
      return res.status(status.BAD_REQUEST).json(error);
    }

    next(e);
  }
};

export const getTreeUserGuide = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const getObj = await findTreeUserGuide();
    return res.json(getObj);
  } catch (e) {
    next(e);
  }
};

export const deleteUserGuide = async (
  req: Request & { params: { slug: string } } & { body: UserGuide },
  res: Response,
  next: NextFunction,
) => {
  try {
    const slug = req.params.slug;
    const getObj = await deleteUserGuideBySlug(slug);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      const error: IApiError = {
        code: ERROR.DATA_NOT_FOUND,
        message: 'Mạng LL không tồn tại',
        statusCode: status.BAD_REQUEST,
        errors: { field: 'id', message: 'Id không tồn tại' },
      };
      return res.status(status.BAD_REQUEST).json(error);
    } else if (e instanceof Error && e.message === ERROR.DATA_HAS_CHILD) {
      const error: IApiError = {
        code: ERROR.DATA_NOT_FOUND,
        message: 'Thư mục này có thư mục con',
        statusCode: status.BAD_REQUEST,
        errors: { field: 'children', message: 'Thư mục này có thư mục con' },
      };
      return res.status(status.BAD_REQUEST).json(error);
    }

    next(e);
  }
};

export const exportUserGuideDocx = async (
  req: Request & { query: { token?: string }; params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { token } = req.query;
    try {
      jwt.verify(token?.toString() || '', config.jwtSecretFile);
    } catch (_) {
      throw new Error(ERROR.AUTHENTICATION_ERROR);
    }
    const buffer = await exportUserGuide();
    res.setHeader(
      'Content-Disposition',
      'attachment; filename=HDSH_' + Date.now() + '.docx',
    );
    res.send(buffer);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Bảng không có dữ liệu',
        statusCode: status.BAD_REQUEST,
      });
    } else if (e instanceof Error && e.message === ERROR.AUTHENTICATION_ERROR) {
      return res.status(status.UNAUTHORIZED).json({
        code: ERROR.AUTHENTICATION_ERROR,
        message: e.message.toString(),
        statusCode: status.UNAUTHORIZED,
      });
    }
    next(e);
  }
};
