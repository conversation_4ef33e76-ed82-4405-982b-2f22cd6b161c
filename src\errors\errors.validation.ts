import Joi from 'joi';
import { IApiError } from 'src/types/validation';

export const jBodyPostError = Joi.object({
  name: Joi.string().required(),
  content: Joi.string().required(),
  status: Joi.string().required(),
  handleMethod: Joi.string().required(),
});

export const jSlugInParams = Joi.object({
  slug: Joi.string().required(),
});

export const eSlugInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng slug (Path) chưa chính xác',
  errors: { slug: 'slug Định dạng chọn chính xác' },
  statusCode: 400,
};

export const jBodyPutError = Joi.object({
  name: Joi.string().required(),
  content: Joi.string().required(),
  type: Joi.string().required(),
  status: Joi.string().required(),
  handleMethod: Joi.string().required(),
});

export const eQueryErrors: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng chuỗi tìm kiếm chưa chính xác',
  statusCode: 400,
};

export const jQueryErrors = Joi.object({
  search: Joi.string().allow(''),
  name: Joi.string(),
  content: Joi.string(),
  handleMethod: Joi.string(),
  type: Joi.string(),
  status: Joi.string(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});
