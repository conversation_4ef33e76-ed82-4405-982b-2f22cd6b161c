import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { Log } from '../models/log.model';
import { insertLog, find } from './logs.service';
import { IGetLogsQuery } from './logs';
import { ISearchQuery } from '../types/req';
import { ERROR } from '../utils/error';

export const postLog = async (
  req: Request & { body: Log },
  res: Response,
  next: NextFunction,
) => {
  try {
    const body: Log = req.body;
    const postLog = await insertLog(body);
    if (postLog.error)
      return res.status(status.BAD_REQUEST).json(postLog.error);
    return res.json(postLog);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const searchLog = async (
  req: Request & { query: ISearchQuery<IGetLogsQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const links = await find(req.query);
    return res.json(links);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
