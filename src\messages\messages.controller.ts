import { Request, Response, NextFunction } from 'express';
import { sendServiceSMS } from './messages.service';
import { ERROR } from '../utils/error';
import { status } from 'http-status';
import { TypeORMError } from 'typeorm';
export const sendSMS = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { mobile, sms_content } = req.body;
    if (!mobile || !sms_content) {
      return res
        .status(400)
        .json({ success: false, message: 'Missing required fields' });
    }
    await sendServiceSMS({ mobile, sms_content });
    return res.status(201).json({ success: true, message: 'SMS sent' });
  } catch (e) {
    if (e instanceof TypeORMError) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: 'Cannot connect to database sms server',
        statusCode: 400,
      });
    }
    next(e);
  }
};
