import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { GuardShift, ShiftPost, ShiftStaff, User } from '../models';
import { ISearchQuery } from '../types/req';
import { ERROR } from '../utils/error';
import {
  ICreateGuardSchedule,
  IDeleteGuardPostInGuardSchedule,
  ISearchGuardScheduleQuery,
  ISearchGuardShiftQuery,
} from './guard-shifts';
import * as guardShiftsService from './guard-shifts.service';

export const createGuardShifts = async (
  req: Request & { body: GuardShift },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.create(req.body);
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateGuardShifts = async (
  req: Request & { body: GuardShift } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.update(
      Number(req.params.id),
      req.body,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteGuardShifts = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.remove(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getGuardShifts = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.findById(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const searchGuardShifts = async (
  req: Request & { query: ISearchQuery<ISearchGuardShiftQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.search(req.query);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getShiftPosts = async (
  req: Request & { params: { guardShiftId: number; postId: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.getShiftPosts(
      req?.params?.guardShiftId,
      req?.params?.postId,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateShiftPosts = async (
  req: Request & { body: ShiftPost } & {
    params: { guardShiftId: number; postId: number };
  },
  res: Response,
  next: NextFunction,
) => {
  try {
    await guardShiftsService.updateShiftPosts(
      req?.params?.guardShiftId,
      req?.params?.postId,
      { ...req.body },
    );
    return res.json({
      message: 'Cập nhật thành công',
    });
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteShiftPosts = async (
  req: Request & { params: { guardShiftId: number; postId: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.deleteShiftPosts(
      req?.params?.guardShiftId,
      req?.params?.postId,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getShiftStaff = async (
  req: Request & {
    params: { guardShiftId: number; postId: number; qneid: string };
  },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.getShiftStaff(
      req?.params?.guardShiftId,
      req?.params?.postId,
      req?.params?.qneid,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Nhân viên không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateShiftStaff = async (
  req: Request & { body: ShiftStaff } & {
    params: { guardShiftId: number; postId: number; qneid: string };
  },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.updateShiftStaff(
      req?.params?.guardShiftId,
      req?.params?.postId,
      req?.params?.qneid,
      {
        ...req.body,
      },
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Nhân viên không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteShiftStaff = async (
  req: Request & {
    params: { guardShiftId: number; postId: number; qneid: string };
  },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.deleteShiftStaff(
      req?.params?.guardShiftId,
      req?.params?.postId,
      req?.params?.qneid,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Nhân viên không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

// Lấy danh sách lịch gác theo ngày
export const getGuardSchedule = async (
  req: Request & { query: ISearchGuardScheduleQuery },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await guardShiftsService.getGuardSchedule(req.query, user);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Lịch gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'date',
          message: 'Ngày không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const createGuardSchedule = async (
  req: Request & { body: ICreateGuardSchedule },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await guardShiftsService.createGuardSchedule(user, req.body);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const duplicateGuardSchedule = async (
  req: Request & { body: ICreateGuardSchedule },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.duplicateGuardSchedule(req.body);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteGuardPostInGuardSchedule = async (
  req: Request & { params: { guardPostId: number } } & {
    body: IDeleteGuardPostInGuardSchedule;
  },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardShiftsService.deleteGuardPostInGuardSchedule(
      req?.params?.guardPostId,
      req?.body,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Vọng gác trong ngày không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
