import Joi from 'joi';
import { IApiError } from '../types/validation';

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jBodySituationSummary = Joi.object({
  detailDutyNowId: Joi.number().required(),
  detailDutyFeatureId: Joi.number().required(),
  totalPer: Joi.number().required(),
  presentPer: Joi.number().required(),
  absentPer: Joi.string(),
  equipmentTakeOut: Joi.string().max(2000),
  taskMain: Joi.string().max(2000),
  taskContain: Joi.string().max(2000),
  taskSudden: Joi.string().max(2000),
  prosAndCons: Joi.string().max(2000),
  taskContinue: Joi.string().max(2000),
  bookHandover: Joi.string().max(2000),
  nameCommand: Joi.string().max(250),
  nameDutyNow: Joi.string().max(250).required(),
  nameDutyFeature: Joi.string().max(250).required(),
  date: Joi.date().required(),
  codeOrg: Joi.string().max(100).required(),
});

const eBodySituationSummary: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export {
  jIdInParams,
  eIdInParams,
  jBodySituationSummary,
  eBodySituationSummary,
  eSearchQuery,
};
