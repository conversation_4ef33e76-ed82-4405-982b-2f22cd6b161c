import Joi from 'joi';
import { IApiError } from '../types/validation';

export const jIdInParams = Joi.object({
  id: Joi.number().required(),
});
export const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng Id (Path) chưa chính xác',
  errors: { id: 'id Định dạng chọn chính xác' },
  statusCode: 400,
};

export const jBodyPostPermission = Joi.object({
  name: Joi.string().min(1).max(300).required(),
  desc: Joi.string().min(1).max(300),
  parentId: Joi.number().allow(null),
  resource: Joi.string().min(1).max(500),
  status: Joi.number().valid(0, 1),
  displayName: Joi.string().min(1).max(300).required(),
});
export const jBodyPutPermission = Joi.object({
  name: Joi.string().min(1).max(300),
  desc: Joi.string().min(1).max(300),
  parentId: Joi.number().allow(null),
  resource: Joi.string().min(1).max(500),
  status: Joi.number().valid(0, 1),
  displayName: Joi.string().min(1).max(300),
});
export const eBodyPostPermission: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng  chưa chính xác',
  statusCode: 400,
};
export const jParamsSearchPermission = Joi.object({
  limit: Joi.number().min(0),
  page: Joi.number().min(0),
  skip: Joi.number().min(0).required(),
  name: Joi.string().min(1).required(),
  displayName: Joi.string(),
});
export const eParamsSearchPermission: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: '',
  statusCode: 400,
};
export const eMessage: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng chưa chính xác',
  statusCode: 400,
};
export const jIdInUpdateStatus = Joi.array().items(Joi.number());
