import { createClient, RedisClientType } from 'redis';
import logger from '../utils/log';

class RedisService {
  private static instance: RedisService;
  private client: RedisClientType;
  private isConnected: boolean = false;

  private constructor() {
    // Create Redis client
    this.client = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379',
      password: process.env.REDIS_PASSWORD || undefined,
      socket: {
        reconnectStrategy: (retries: any) => {
          if (retries > 5) {
            logger.error('Max redis reconnection attempts reached');
            return new Error('Max reconnection attempts reached');
          }
          return Math.min(retries * 100, 5000); // Reconnect with backoff up to 5 seconds
        },
        connectTimeout: 10000, // 10 seconds connection timeout
      },
    });

    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    this.client.on('connect', () => {
      logger.info('Redis connecting...');
    });

    this.client.on('ready', () => {
      this.isConnected = true;
      logger.info('Redis connected and ready');
    });

    this.client.on('error', (err) => {
      this.isConnected = false;
      logger.error('Redis error:', err);
    });

    this.client.on('end', () => {
      this.isConnected = false;
      logger.info('Redis connection closed');
    });

    this.client.on('reconnecting', () => {
      logger.info('Redis reconnecting...');
    });
  }

  public static getInstance(): RedisService {
    if (!RedisService.instance) {
      RedisService.instance = new RedisService();
    }
    return RedisService.instance;
  }

  public async connect(): Promise<void> {
    if (!this.isConnected) {
      try {
        await this.client.connect();
      } catch (err) {
        logger.error('Failed to connect to Redis:', err);
        throw err;
      }
    }
  }

  public async disconnect(): Promise<void> {
    if (this.isConnected) {
      try {
        await this.client.quit();
        this.isConnected = false;
      } catch (err) {
        logger.error('Failed to disconnect from Redis:', err);
        throw err;
      }
    }
  }

  // Example Redis operations
  public async set(key: string, value: string, ttl?: number): Promise<void> {
    try {
      if (!this.isConnected) await this.connect();

      const options = ttl ? { EX: ttl } : undefined;
      await this.client.set(key, value, options);
    } catch (err) {
      logger.error(`Redis set operation failed for key ${key}:`, err);
      throw err;
    }
  }

  public async get(key: string): Promise<string | null> {
    try {
      if (!this.isConnected) await this.connect();
      return await this.client.get(key);
    } catch (err) {
      logger.error(`Redis get operation failed for key ${key}:`, err);
      return null;
    }
  }

  public async del(key: string): Promise<number> {
    try {
      if (!this.isConnected) await this.connect();
      return await this.client.del(key);
    } catch (err) {
      logger.error(`Redis del operation failed for key ${key}:`, err);
      throw err;
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      if (!this.isConnected) await this.connect();
      await this.client.ping();
      return true;
    } catch (err) {
      logger.error('Redis health check failed:', err);
      return false;
    }
  }
}

// Create and export singleton instance
const redisService = RedisService.getInstance();
export default redisService;
