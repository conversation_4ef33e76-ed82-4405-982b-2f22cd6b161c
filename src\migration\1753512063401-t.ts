import { MigrationInterface, QueryRunner } from "typeorm";

export class T1753512063401 implements MigrationInterface {
    name = 'T1753512063401'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "chucVuLoaiHinhTruc" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_7664624388847d22a6d18da2f86" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_d15ff2fa1e8a805b9538c11fc61" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" bigint NOT NULL IDENTITY(1,1), "maLoaiHinhTruc" uniqueidentifier NOT NULL, "maChucVu" varchar(36) NOT NULL, CONSTRAINT "PK_fcf20d6ce3f693198be43e55773" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_7c868b63615e222e7a7f77d28d" ON "chucVuLoaiHinhTruc" ("fullText") `);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" ADD CONSTRAINT "FK_8b2509728952a07a078556704d6" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" ADD CONSTRAINT "FK_5e9d2220837e6ba9e027faba9c8" FOREIGN KEY ("maChucVu") REFERENCES "military_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" DROP CONSTRAINT "FK_5e9d2220837e6ba9e027faba9c8"`);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" DROP CONSTRAINT "FK_8b2509728952a07a078556704d6"`);
        await queryRunner.query(`DROP INDEX "IDX_7c868b63615e222e7a7f77d28d" ON "chucVuLoaiHinhTruc"`);
        await queryRunner.query(`DROP TABLE "chucVuLoaiHinhTruc"`);
    }

}
