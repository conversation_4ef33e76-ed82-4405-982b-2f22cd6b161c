import Joi from 'joi';
import { IApiError } from 'src/types/validation';

export const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

export const jIdInParamsGuestCardId = Joi.object({
  guestCardId: Joi.string().required(),
});

export const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

export const eIdInParamsGuestCardId: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'guestCardId không hợp lệ',
  errors: { guestCardId: 'guestCardId phải là chuỗi' },
  statusCode: 400,
};

export const jBodyPreRegisteredGuest = Joi.object({
  parentId: Joi.number(),
  meetWhoId: Joi.string().required(),
  guestCardId: Joi.string().required(),
  checkInTime: Joi.string().required(),
  guestCodeOrg: Joi.string(),
  estimatedOutTime: Joi.date(),
  guestTypeId: Joi.number().required(),
  purposeOfVisit: Joi.string(),
  purposeCategoryId: Joi.number().required(),
  groupName: Joi.string(),
  leaderName: Joi.string(),
  leaderPosition: Joi.string(),
  numberOfVisitors: Joi.number(),
  numberOfVehicles: Joi.number(),
  notes: Joi.string().allow(''),
  isLeader: Joi.boolean(),
  guest: Joi.object({
    id: Joi.number(),
    fullName: Joi.string().required(),
    dateOfBirth: Joi.date(),
    phoneNumber: Joi.string().required(),
    guestCodeOrg: Joi.string(),
    guestOrganizationName: Joi.string().required(),
    avatar: Joi.string().allow(null, ''),
    sexId: Joi.number(),
    notes: Joi.string().allow(''),
    office: Joi.string().allow(''),
    occupation: Joi.string(),
    permanentAddress: Joi.string(),
    nationality: Joi.string(),
    identifyCard: Joi.object({
      documentTypeId: Joi.number().required(),
      identificationNumber: Joi.string().required(),
      issueDate: Joi.date(),
      expiryDate: Joi.date(),
      issuingAuthority: Joi.string(),
      notes: Joi.string().allow(''),
      images: Joi.array().items(Joi.string()),
    }).required(),
  }).required(),
});

export const eBodyPreRegisteredGuest: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

export const jSearchPreRegisteredGuestObject = {
  search: Joi.string(),
  limit: Joi.number(),
  page: Joi.number(),
  inDateTo: Joi.date(),
  inDateFrom: Joi.date(),
  estimatedOutTimeTo: Joi.date(),
  estimatedOutTimeFrom: Joi.date(),
  guestName: Joi.string(),
  guestIdentityTypeId: Joi.number(),
  meetWhoId: Joi.string(),
  meetOrgCode: Joi.string(),
  guestTypeIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  purposeCategoryIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  guestCardId: Joi.string(),
  identificationNumber: Joi.string(),
};

export const jSearchPreRegisteredGuest = Joi.object(
  jSearchPreRegisteredGuestObject,
);

export const eSearchPreRegisteredGuestQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export const jDeletePreRegisteredGuest = Joi.object({
  ids: Joi.array().items(Joi.number()).required(),
});

export const eDeletePreRegisteredGuest: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Tham số xóa không hợp lệ',
  statusCode: 400,
};
