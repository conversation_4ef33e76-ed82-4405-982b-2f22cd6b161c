export type ISearchShiftHandoverQuery = {
  orgCode?: string;
  manageOrgCode?: string;
  guardPostId?: number;
  fromDate?: Date;
  toDate?: Date;
  createdAt?: string[];
  fullText?: string;
  shiftCode?: string;
  guardShiftName?: string;
  bookHandover?: string;
  shiftNowName?: string;
  qneidSuperVisor?: string;
  shiftNowId?: number;
  shiftFeatureName?: string;
  guard?: string;
  limit: number;
  page: number;
};
export type ICreateShiftHandover = {
  shiftNowId: number;
  shiftFeatureId: number;
  guardPostId: number;
  qneidSuperVisor: string;
  taskContent: string;
  taskContinue: string;
  bookHandover: string;
  vehicleCountStart: number;
  vehicleEntryCountDuring: number;
  vehicleExitCountDuring: number;
  vehicleCountHandover: number;
  equipmentHandover: string;
};
