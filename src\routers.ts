import express from 'express';
import { routerRole } from './roles';
import { routerLog } from './logs';
import { userRouter } from './users';
import { routerPermission } from './permissions';
import { routerOrganization } from './organizations';
import { userGuideRouter } from './user-guides';
import { reportErrorRouter } from './report-errors';
import { fileRouter } from './files';
import { errorRouter } from './errors';
import { routerType } from './lsTypes';
import { routerSituationSummary } from './situation-summary';
import { routerShiftHandover } from './shift-handovers';
import { routerEquipment } from './equipments';
import { routerGuardPost } from './guard-posts';
import { routerWorkingSchedule } from './working-schedules';
import { routerGuestLog } from './guest-logs';
import { shiftInspectionsRoute } from './shift-inspections';
import { routerEquipHandover } from './equip-handover';
import { routerGuardShifts } from './guard-shifts';
import { routerEQN } from './eqn';
import { routerConfigShift } from './config-shift';
import { routerPreRegisteredGuest } from './pre-registered-guest';
import { routerTimesheet } from './timesheet';
import { routerMessages } from './messages';

import { routerLoaiHinhTruc } from './dm-loai-hinh-truc';
import { routerCauHinheQNLoaiHinhTruc } from './cau-hinh-eqn-loai-hinh-truc';

const router = express.Router();

router.use('/users', userRouter);
router.use('/roles', routerRole);
router.use('/permissions', routerPermission);
router.use('/organizations', routerOrganization);
router.use('/logs', routerLog);
router.use('/user-guides', userGuideRouter);
router.use('/report-errors', reportErrorRouter);
router.use('/errors', errorRouter);
router.use('/files', fileRouter);
router.use('/types', routerType);
router.use('/situation-summary', routerSituationSummary);
router.use('/shift-handovers', routerShiftHandover);
router.use('/equipments', routerEquipment);
router.use('/guard-posts', routerGuardPost);
router.use('/guard-shifts', routerGuardShifts);
router.use('/working-schedules', routerWorkingSchedule);
router.use('/guest-logs', routerGuestLog);
router.use('/shift-inspections', shiftInspectionsRoute);
router.use('/equip-handovers', routerEquipHandover);
router.use('/eqn', routerEQN);
router.use('/config-shifts', routerConfigShift);
router.use('/pre-registered-guests', routerPreRegisteredGuest);
router.use('/timesheet', routerTimesheet);
router.use('/messages', routerMessages);

router.use('/dm-loai-hinh-trucs', routerLoaiHinhTruc);
router.use('/cau-hinh-eqn-loai-hinh-trucs', routerCauHinheQNLoaiHinhTruc);

export default router;
