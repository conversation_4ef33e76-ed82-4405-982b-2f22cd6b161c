import {
  Column,
  <PERSON>tity,
  PrimaryColumn,
  DeleteDateColumn,
  OneToMany,
} from 'typeorm';
import { ChucVuLoaiHinhTruc } from './chucVuLoaiHinhTruc.model';

@Entity('position_categories')
export class PositionCategory {
  @PrimaryColumn({ type: 'varchar', length: 36 })
  id?: string;

  @Column({ type: 'varchar', length: 36, unique: true })
  code?: string;

  @Column({
    name: 'date_created',
    type: 'datetime2',
    default: () => 'GETDATE()',
    nullable: true,
  })
  dateCreated?: Date;

  @Column({
    name: 'date_updated',
    type: 'datetime2',
    default: () => 'GETDATE()',
    nullable: true,
  })
  dateUpdated?: Date;

  @Column({ name: 'is_enable', type: 'bit', default: true, nullable: true })
  isEnable?: boolean;

  @Column({ type: 'nvarchar', length: 255, nullable: true })
  name?: string;

  @Column({ name: 'order_number', type: 'int', nullable: true })
  orderNumber?: number;

  @Column({ name: 'short_name', type: 'nvarchar', length: 50, nullable: true })
  shortName?: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  type?: string;
  @DeleteDateColumn()
  deletedAt?: Date;

  @OneToMany(
    () => ChucVuLoaiHinhTruc,
    (chucVuLoaiHinhTruc) => chucVuLoaiHinhTruc.positionCategory,
  )
  public chucVuLoaiHinhTrucs!: ChucVuLoaiHinhTruc[];
}
