import dotenv from 'dotenv-safe';
import { database } from '../config';
import log from '../utils/log';
dotenv.config({
  allowEmptyValues: true,
});
// import { modelMapping } from './mapping';
import { initSync } from './init';

import { syncDonVi } from './handlers/don-vi-sync';

import fs from 'fs';
import path from 'path';
import { synceQN } from './handlers/eqn-sync';
import { syncPosition } from './handlers/position-sync';
import { syncRank } from './handlers/rank-sync';
import { syncType } from './handlers/type-sync';

async function consumeMessages() {
  try {
    console.log('Đang đọc dữ liệu từ file JSON...');

    // Đường dẫn đến file JSON
    const filePath = path.join(__dirname, '../../data.json');

    // Đọc và parse dữ liệu từ file JSON
    const rawData = fs.readFileSync(filePath, 'utf-8');
    const messages = JSON.parse(rawData);

    console.log(`Đã đọc được ${messages.length} message từ file JSON.`);

    // Xử lý từng message
    for (const message of messages) {
      console.log('Đang xử lý message:', message);
      if (message.MessageType === 'rank') {
        await syncRank(); // Gọi hàm xử lý DonVi
      } else if (message.MessageType === 'type') {
        await syncType(); // Gọi hàm xử lý DonVi
      } else if (message.MessageType === 'position') {
        await syncPosition(); // Gọi hàm xử lý DonVi
      } else if (message.MessageType === 'eQN') {
        await synceQN(); // Gọi hàm xử lý DonVi
      } else if (message.MessageType === 'DonVi') {
        await syncDonVi(); // Gọi hàm xử lý DonVi
      } else {
        console.warn(`Loại message không được hỗ trợ: ${message.MessageType}`);
      }
    }

    console.log('Hoàn thành xử lý tất cả message từ file JSON.');
  } catch (error) {
    console.error('Lỗi khi đọc hoặc xử lý file JSON:', error);
  }
}
database
  .initialize()
  .then(async () => {
    // log.info('Connected to oracle db');
    log.info('kết nối cơ sở dữ liệu to oracle db');

    const logFilePath = path.join(__dirname, 'log_sync.txt');
    if (!fs.existsSync(logFilePath)) {
      log.info(
        'File log_sync.txt không tồn tại. Bắt đầu khởi tạo đồng bộ hóa.',
      );
      // Tạo file log_sync.txt với log thời gian chạy đồng bộ
      const currentDate = new Date();
      const formattedDate = `${currentDate.getDate()}/${currentDate.getMonth() + 1}/${currentDate.getFullYear()} ${currentDate.getHours()}:${currentDate.getMinutes()}:${currentDate.getSeconds()}`;
      fs.writeFileSync(logFilePath, `Đồng bộ hóa lúc ${formattedDate} \n`);
      await initSync();
    } else {
      log.info('File log_sync.txt đã tồn tại. Bỏ qua khởi tạo đồng bộ hóa.');
    }

    consumeMessages();
    //   await initSync();
  })
  .catch((err) => {
    log.error(err);
    return;
  });
