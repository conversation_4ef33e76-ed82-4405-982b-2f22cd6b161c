import Joi from 'joi';
import { IApiError } from '../types/validation';

export const eAddLogBody: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: '<PERSON><PERSON><PERSON> dạng  chưa ch<PERSON>h xác',
  statusCode: 400,
};
export const eQuerySearch: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Kiểm tra lại tham số tìm kiếm',
  statusCode: 400,
};
export const jBodyLogs = Joi.object({
  ip: Joi.string().min(1).max(100).required(),
  content: Joi.string().max(500).required(),
  typeId: Joi.number().required(),
  userId: Joi.number().required(),
});
export const jQuerySearchLog = Joi.object({
  limit: Joi.number().required(),
  page: Joi.number().required(),
  startTime: Joi.date(),
  endTime: Joi.date(),
  typeIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  fullText: Joi.string(),
});
