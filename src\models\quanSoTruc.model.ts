import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';

@Entity()
export class QuanSoTruc extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column()
  thang?: number;

  @Column()
  nam?: number;

  @Column({
    type: 'varchar',
    length: 17,
    nullable: true,
  })
  maDonVi?: string;
  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'code' })
  donVi?: Organization;

  @Column()
  quanSoTruc?: number;

  @Column()
  tongSuatTruc?: number;
}
