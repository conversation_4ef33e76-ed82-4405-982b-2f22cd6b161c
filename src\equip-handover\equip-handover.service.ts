import { database } from '../config';
import { EquipHandover } from '../models';
import { ERROR } from '../utils/error';
import { createLinks } from '../utils/pagination';
import { ISearchEquipHandoverQuery } from './equip-handover';

export const create = async (data: Partial<EquipHandover>) => {
  const repo = database.getRepository(EquipHandover);
  const equipHandover = await repo.save(data);
  return {
    data: equipHandover,
    message: 'Tạo bàn giao trang thiết bị thành công',
  };
};

export const update = async (id: number, data: Partial<EquipHandover>) => {
  const repo = database.getRepository(EquipHandover);
  const equipHandover = await repo.findOne({
    where: { id },
    withDeleted: true,
  });
  if (!equipHandover) throw new Error(ERROR.DATA_NOT_FOUND);
  const updatedEquipHandover = await repo.update(
    equipHandover?.id as number,
    data,
  );
  return {
    data: updatedEquipHandover,
    message: 'Cập nhật bàn giao trang thiết bị thành công',
  };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(EquipHandover);
  const equipHandover = await repo.findOne({
    where: { id },
    withDeleted: true,
  });
  if (!equipHandover) throw new Error(ERROR.DATA_NOT_FOUND);
  return { data: equipHandover };
};

export const getAll = async (query: ISearchEquipHandoverQuery) => {
  const repo = database.getRepository(EquipHandover);
  const qb = repo.createQueryBuilder('equipHandover').withDeleted();

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.leftJoin(
    'situationSummaries',
    'summary',
    'summary.detailDutyNowId = equipHandover.detailDutyNowId',
  )
    .leftJoin(
      'detailDutys',
      'detailNow',
      'summary.detailDutyNowId = detailNow.id',
    )
    .leftJoin('eQN', 'main', 'detailNow.mainId = main.id')
    .leftJoin('eQN', 'assistant', 'detailNow.assistantId = assistant.id');

  if (query.codeOrg) {
    qb.andWhere('equipHandover.codeOrg LIKE :codeOrg', {
      codeOrg: `%${query.codeOrg}%`,
    });
  }

  if (query.detailDutyNowId) {
    qb.andWhere('equipHandover.detailDutyNowId = :detailDutyNowId', {
      detailDutyNowId: query.detailDutyNowId,
    });
  }

  if (query.detailDutyFeatureId) {
    qb.andWhere('equipHandover.detailDutyFeatureId = :detailDutyFeatureId', {
      detailDutyFeatureId: query.detailDutyFeatureId,
    });
  }

  if (query.date) {
    qb.andWhere('CONVERT(DATE, equipHandover.date) = :date', {
      date: query.date,
    });
  }

  if (query.values) {
    qb.andWhere('equipHandover.values LIKE :values', {
      values: `%${query.values}%`,
    });
  }

  if (query.nameDutyNow) {
    qb.andWhere('equipHandover.nameDutyNow LIKE :nameDutyNow', {
      nameDutyNow: `%${query.nameDutyNow}%`,
    });
  }

  if (query.nameDutyFeature) {
    qb.andWhere('equipHandover.nameDutyFeature LIKE :nameDutyFeature', {
      nameDutyFeature: `%${query.nameDutyFeature}%`,
    });
  }

  if (query.fromDate) {
    qb.andWhere('equipHandover.date >= :fromDate', {
      fromDate: query.fromDate,
    });
  }

  if (query.toDate) {
    qb.andWhere('equipHandover.date <= :toDate', { toDate: query.toDate });
  }

  if (query.nameCommand) {
    qb.andWhere('summary.nameCommand LIKE :nameCommand', {
      nameCommand: `%${query.nameCommand}%`,
    });
  }

  if (query.nameChiefDuty) {
    qb.andWhere('main.fullName LIKE :nameChiefDuty', {
      nameChiefDuty: `%${query.nameChiefDuty}%`,
    });
  }

  if (query.nameDeputyDuty) {
    qb.andWhere('assistant.fullName LIKE :nameDeputyDuty', {
      nameDeputyDuty: `%${query.nameDeputyDuty}%`,
    });
  }

  qb.orderBy('equipHandover.createdAt', 'DESC')
    .addOrderBy('equipHandover.date', 'DESC')
    .skip(skip)
    .take(limit);

  const [equipHandovers, total] = await qb.getManyAndCount();

  const links = createLinks(
    '/equip-handover/search?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: equipHandovers,
    },
    links,
  };
};

export const deleteOne = async (id: number) => {
  const repo = database.getRepository(EquipHandover);
  const equipHandover = await repo.findOne({
    where: { id },
    withDeleted: true,
  });
  if (!equipHandover) throw new Error(ERROR.DATA_NOT_FOUND);
  await repo.delete(equipHandover.id as number);
  return {
    data: equipHandover,
    message: 'Xóa bàn giao trang thiết bị thành công',
  };
};

export const exportExcel = async (query: ISearchEquipHandoverQuery) => {
  const data = await getAll(query);
  /**
   * todo: export excel
   */
  return data;
};
