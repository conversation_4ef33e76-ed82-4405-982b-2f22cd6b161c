import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { CauHinhEqnLoaiHinhTruc, User } from '../models';
import { ERROR } from '../utils/error';
import * as service from './cau-hinh-eqn-loai-hinh-truc.service';
export const createCauHinheQNLoaiHinhTruc = async (
  req: Request & { body: CauHinhEqnLoaiHinhTruc },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await service.create(req.body.eqn, user);
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.LOAI_HINH_TRUC_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Mã loại trực không tồn tại',
            statusCode: 400,
          });
        case ERROR.EQN_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Quân nhân không tồn tại',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};
