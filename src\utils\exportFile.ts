import Docxtemplater from 'docxtemplater';
import path from 'path';
import <PERSON>z<PERSON><PERSON> from 'pizzip';
import fs from 'fs-extra';
import config from '../config';
import log from './log';

const TEMP_EXISTED = 'TEMP_EXISTED';
export const exportDoc = async <T>(
  inputName: string,
  outputName: string,
  data: T,
) => {
  const fileTemp = path.resolve(config.dirTempBc, inputName);
  // const pathOutput = path.resolve(config.dirSaveBC, outputName);
  const fileOutput = path.resolve(config.dirSaveBC, outputName);
  log.debug('fileTemp:', fileTemp);
  log.debug('fileOutput:', fileOutput);

  const isFileTempBC = await fs.pathExists(fileTemp);
  log.debug('isPathTempBC:', isFileTempBC, typeof fileTemp);
  if (!isFileTempBC) throw new Error(TEMP_EXISTED);

  // await fs.ensureDir(pathOutput);

  const content = await fs.readFile(fileTemp, 'binary');
  const zip = new PizZip(content);
  const doc = new Docxtemplater(zip, {
    paragraphLoop: true,
    linebreaks: true,
  });

  doc.render(data);
  const buf = doc.getZip().generate({
    type: 'nodebuffer',
    compression: 'DEFLATE',
  });

  await fs.writeFile(fileOutput, buf);
  return {
    fileOutput,
    url: `${config.publicDownloadPath}/${outputName}`,
  };
};

export enum EXPORT_FILE {
  TEMP_EXISTED = 'TEMP_EXISTED',
}
