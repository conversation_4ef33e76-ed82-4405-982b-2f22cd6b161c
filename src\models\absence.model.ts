import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Type } from './type.model';

@Entity('absences')
export class Absence extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'qneid',
    type: 'varchar',
    length: 36,
  })
  qneid?: string;

  @Column({
    name: 'start_date',
    type: 'datetime2',
  })
  startDate?: Date;

  @Column({
    name: 'end_date',
    type: 'datetime2',
  })
  endDate?: Date;

  @Column({
    name: 'absence_type_id',
    type: 'int',
  })
  absenceTypeId?: number;
  @ManyToOne(() => Type)
  @JoinColumn({
    name: 'absence_type_id',
  })
  absenceType?: Type;

  @Column({
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  note?: string;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 100,
  })
  codeOrg?: string;

  @Column({
    name: 'approved_by',
    type: 'nvarchar',
    length: 250,
  })
  approvedBy?: string;

  @Column({
    name: 'approved_at',
    type: 'datetime2',
  })
  approvedAt?: Date;
}
