import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';

@Entity('equipHandovers')
export class EquipHandover extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'detail_duty_now_id',
    type: 'int',
  })
  detailDutyNowId?: number;

  @Column({
    name: 'detail_duty_feature_id',
    type: 'int',
  })
  detailDutyFeatureId?: number;

  @Column({
    type: 'datetime2',
  })
  date?: Date;

  @Column({
    type: 'nvarchar',
    length: 2000,
  })
  values?: string;

  @Column({
    name: 'equipment_situation',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  equipmentSituation?: string;

  @Column({
    name: 'name_duty_now',
    type: 'nvarchar',
    length: 250,
  })
  nameDutyNow?: string;

  @Column({
    name: 'name_duty_feature',
    type: 'nvarchar',
    length: 250,
  })
  nameDutyFeature?: string;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;
}
