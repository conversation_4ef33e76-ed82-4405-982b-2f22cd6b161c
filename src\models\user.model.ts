import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  Index,
  BeforeInsert,
  ManyToOne,
  OneToMany,
  JoinColumn,
} from 'typeorm';
import bcrypt from 'bcryptjs';

import EntityModel from './entity.model';
import { Role } from './role.model';
import { Organization } from './organization.model';
import { Log } from './log.model';
import { Type } from './type.model';
import { eQN } from './eQN.model';

@Entity('users')
export class User extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ type: 'nvarchar', length: 250, nullable: true })
  username?: string;

  @Index()
  @Column({ type: 'nvarchar', length: 250, nullable: true })
  password?: string;

  @Column({ type: 'nvarchar', length: 250, nullable: true, default: null })
  name?: string;

  @Column({ type: 'bit', default: 1 })
  status?: boolean;

  @Column({ type: 'nvarchar', length: 500, nullable: true, default: null })
  contact?: string;

  @Column({ type: 'nvarchar', length: 500, nullable: true, default: null })
  notes?: string;

  @Column({ type: 'nvarchar', length: 100, nullable: false })
  orgCode?: string;

  @Column({ type: 'datetime2', nullable: true, default: null })
  lastLoginTime?: Date;

  @Column({ type: 'nvarchar', length: 300, nullable: true, default: null })
  image?: string;

  @Column({ type: 'datetime2', nullable: true, default: null })
  lastPasswordChangeTime?: Date;

  @Column({ type: 'nvarchar', length: 24, nullable: true, default: null })
  qneid?: string;

  @Column({ type: 'varchar', length: 17, nullable: true, default: null })
  manageOrgCode?: string;

  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'manageOrgCode', referencedColumnName: 'code' })
  manageOrganization?: Organization;

  @Column({ name: 'eQNId', type: 'varchar', length: 36, nullable: true })
  eQNId?: string;

  @ManyToOne(() => eQN)
  @JoinColumn({ name: 'eQNId', referencedColumnName: 'id' })
  eQN?: eQN;

  toJSON() {
    return { ...this, password: undefined };
  }

  @ManyToOne(() => Type, (type) => type.id)
  rank?: Type;

  @Column({ nullable: true })
  public rankId!: number;

  @ManyToOne(() => Type, (position) => position.id)
  position?: Type;

  @Column({ nullable: true })
  public positionId!: number;

  @ManyToOne(() => Organization, (organization) => organization.id)
  organization?: Organization;

  @Column({ nullable: true })
  public organizationId!: string;

  @ManyToOne(() => Role, (role) => role.users)
  role?: Role;

  @Column({ nullable: true })
  public roleId!: number | null;

  @OneToMany(() => Log, (log) => log.user)
  logs?: Log[];

  @BeforeInsert()
  async hashPassword() {
    if (this.password) {
      this.password = await bcrypt.hash(this.password, 12);
    }
  }
  static async comparePasswords(
    candidatePassword: string,
    hashedPassword: string,
  ) {
    return await bcrypt.compare(candidatePassword, hashedPassword);
  }
}
