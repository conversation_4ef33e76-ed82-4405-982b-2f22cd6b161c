import {
  CreateDateColumn,
  UpdateDateColumn,
  BaseEntity,
  Index,
  Column,
  DeleteDateColumn,
} from 'typeorm';

export default abstract class EntityModel extends BaseEntity {
  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;

  @DeleteDateColumn({ default: null })
  deletedAt?: Date;

  @Index()
  @Column({ type: 'nvarchar', length: 4000, nullable: true })
  fullText?: string;
}
