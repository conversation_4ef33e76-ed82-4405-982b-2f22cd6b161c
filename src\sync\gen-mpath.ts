import dotenv from 'dotenv-safe';
dotenv.config({
  allowEmptyValues: true,
});
import { Organization } from '../models';
import { database } from '../config';
import { EntityTarget, ObjectLiteral } from 'typeorm';

const BATCH_SIZE = 1000;

// Tạo interface cho các entity cây
interface TreeEntity extends ObjectLiteral {
  id: string;
  parent: any;
  parent_id: string | null;
  mpath: string | null;
  ThuTuSapXep?: string;
}

/**
 * Hàm rebuild tree chung cho tất cả bảng dữ liệu sử dụng SQL trực tiếp
 * @param entity Entity class
 * @param entityName Tên của entity để hiển thị trong log
 */
async function rebuildTree<T extends TreeEntity>(
  entity: EntityTarget<T>,
  entityName: string,
) {
  console.log(`===== BẮT ĐẦU XỬ LÝ ${entityName} =====`);

  try {
    // Lấy tên bảng thực tế trong database
    const metadata = database.getMetadata(entity);
    const tableName = metadata.tableName;

    // Kiểm tra cấu trúc cột trong cơ sở dữ liệu
    const relationMetadata = metadata.relations.find(
      (rel) => rel.propertyName === 'parent',
    );
    if (relationMetadata) {
      const parentColumnName = relationMetadata.joinColumns[0].databaseName;
      console.log(
        `Tên cột quan hệ cha-con trong ${tableName}: ${parentColumnName}`,
      );
    }

    // Bắt đầu transaction để đảm bảo tính toàn vẹn dữ liệu
    const queryRunner = database.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Đặt lại mpath thành không ảo để đảm bảo TypeORM có thể đọc được sau khi cập nhật
      const mpathColumn = metadata.columns.find(
        (x) => x.databaseName === 'mpath',
      );
      if (mpathColumn) {
        console.log(`mpath Virtual cho ${entityName}:`, mpathColumn.isVirtual);
        mpathColumn.isVirtual = false;
      }

      console.log(
        `1. Đặt tất cả parentId và mpath về NULL cho bảng ${entityName}...`,
      );
      // Bước 1: Reset parentId về NULL
      await queryRunner.query(`
                UPDATE ${tableName}
                SET parentId = NULL,
                mpath = NULL
            `);

      console.log(
        `2. Cập nhật mpath cho các nút gốc (không có IDCapTren, IDCapTren không tồn tại) của ${entityName}...`,
      );
      // Bước 2: Cập nhật mpath cho các nút gốc
      await queryRunner.query(`
                UPDATE node
                SET 
                    mpath = CONCAT(node.id, '.'),
                    parentId = NULL
                FROM ${tableName} node
                LEFT JOIN ${tableName} parent ON node.parentId = parent.id
                WHERE 
                    node.parentId IS NULL 
                    OR node.parentId = ''
                    OR (node.parentId IS NOT NULL AND parent.id IS NULL) /* Cha không tồn tại */
            `);

      // Bước 3: Cập nhật các nút con theo từng cấp
      let level = 1;
      let updated = true; // Biến để kiểm tra xem có bản ghi nào được cập nhật trong mỗi lần lặp
      let totalProcessed = 0; // Đếm tổng số bản ghi đã xử lý

      // Tiếp tục lặp cho đến khi không còn bản ghi nào được cập nhật
      while (updated && level <= 10) {
        // Giới hạn 10 cấp để tránh vòng lặp vô hạn
        console.log(
          `3.${level} Đang xử lý các nút ở cấp ${level} của ${entityName}...`,
        );

        // Đếm số lượng bản ghi cần cập nhật ở cấp này
        const countQuery = `
                    SELECT COUNT(*) as count 
                    FROM ${tableName} AS child
                    JOIN ${tableName} AS parent ON child.parentId = parent.id
                    WHERE child.mpath IS NULL 
                    AND parent.mpath IS NOT NULL
                `;

        const countResult = await queryRunner.query(countQuery);
        const recordsToUpdate = countResult[0].count;

        if (recordsToUpdate === 0) {
          console.log(`Không còn nút nào cần cập nhật ở cấp ${level}`);
          updated = false;
          break;
        }

        console.log(
          `Tìm thấy ${recordsToUpdate} nút cần cập nhật ở cấp ${level}`,
        );

        // Lấy danh sách ID các bản ghi cần cập nhật để xử lý theo lô
        const idsQuery = `
                    SELECT child.id
                    FROM ${tableName} AS child
                    JOIN ${tableName} AS parent ON child.parentId = parent.id
                    WHERE child.mpath IS NULL 
                    AND parent.mpath IS NOT NULL
                `;

        const idsResult = await queryRunner.query(idsQuery);
        const ids = idsResult.map((row: any) => row.id);

        // Xử lý theo lô để tránh vượt quá giới hạn 1000 bản ghi
        let batchCount = 0;
        for (let i = 0; i < ids.length; i += BATCH_SIZE) {
          batchCount++;
          const batchIds = ids.slice(i, i + BATCH_SIZE);

          // Cập nhật theo lô sử dụng JOIN để tránh nhiều truy vấn
          const updateQuery = `
                        UPDATE t
                        SET 
                            mpath = CONCAT(p.mpath, t.id, '.'),
                            parentId = t.parentId
                        FROM ${tableName} t
                        JOIN ${tableName} p ON t.parentId = p.id
                        WHERE t.id IN ('${batchIds.join("','")}')
                        AND p.mpath IS NOT NULL
                    `;

          const updateResult = await queryRunner.query(updateQuery);
          console.log(
            `  + Batch ${batchCount}: Đã cập nhật ${updateResult?.rowsAffected || batchIds.length} nút`,
          );

          totalProcessed += updateResult?.rowsAffected || batchIds.length;
        }

        console.log(
          `Hoàn thành cập nhật cấp ${level}, ${totalProcessed} nút đã xử lý`,
        );
        level++;
      }

      // Kiểm tra nút nào chưa được cập nhật (có thể do cha không tồn tại)
      // const remainingNodesQuery = `
      //           SELECT COUNT(*) as count
      //           FROM ${tableName}
      //           WHERE mpath IS NULL
      //       `;

      // Đếm tổng số nút đã xử lý
      const totalNodesQuery = `
                SELECT COUNT(*) as count
                FROM ${tableName}
            `;

      const totalNodesResult = await queryRunner.query(totalNodesQuery);
      console.log(
        `Tổng cộng ${totalNodesResult[0].count} nút đã được xử lý trong bảng ${entityName}`,
      );

      // Commit transaction khi hoàn thành
      await queryRunner.commitTransaction();
      console.log(`===== HOÀN THÀNH XỬ LÝ ${entityName} =====`);
    } catch (error) {
      // Rollback nếu có lỗi
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      // Giải phóng queryRunner
      await queryRunner.release();
    }
  } catch (error) {
    console.error(`Lỗi khi xử lý ${entityName}:`, error);
  }
}

// Hàm chính để xử lý tất cả các bảng
async function processAllTables(isInitialized: boolean = false) {
  try {
    console.log(`== BẮT ĐẦU XỬ LÝ TẤT CẢ BẢNG ==`);
    console.time('Thời gian xử lý');
    if (!isInitialized) {
      console.log('Kết nối đến database chưa được khởi tạo. Đang khởi tạo...');
      await database.initialize();
    }
    const stats = [];

    // stats.push(await rebuildTree(DanhMucTrangBi, 'DanhMucTrangBi'));
    // stats.push(await rebuildTree(DanhMucVatTu, 'DanhMucVatTu'));
    stats.push(await rebuildTree(Organization, 'DonVi'));
    // stats.push(await rebuildTree(TrangBi, 'TrangBi'));
    if (!isInitialized) {
      console.log(`Đóng kết nối đến database...`);
      await database.close();
    }
  } catch (error) {
    console.error('Lỗi trong quá trình xử lý:', error);
  }
}

// Thực thi nếu script được chạy trực tiếp
if (require.main === module) {
  processAllTables().catch((error) => {
    console.error('Có lỗi xảy ra:', error);
    process.exit(1);
  });
}

export { rebuildTree, processAllTables };
