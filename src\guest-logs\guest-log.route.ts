import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
// import { hasPerm } from '../middlewares/has-perm.middleware';
// import { RolePerms } from '../constants';
import { webhookAuth } from '../middlewares/webhook.middleware';
import {
  createGuestLog,
  deleteGuestLogs,
  deletePendingGuests,
  docxGuestLogs,
  editGuestLog,
  guestByCheckInTime,
  guestByGroupVehicle,
  guestByOrg,
  guestLogDetail,
  guestLogDetailByIdVehicleLog,
  guestLogHistoryInOut,
  guestLogStatsByOrgCode,
  guestLogSummary,
  returnCard,
  searchGuestLogs,
  searchPendingGuestLogs,
  webhookGuestLog,
} from './guest-log.controller';
import {
  eIdInParams,
  eIdVehicleLogInParams,
  eSearchPendingQuery,
  eWebhookGuestLog,
  jBodyGuestLog,
  jDeleteGuestLogs,
  jGuestLogHistoryInOut,
  jGuestLogStatsByOrgCode,
  jIdInParams,
  jIdVehicleLogInParams,
  jSearchGuestLog,
  jSearchGuestLogByVehicle,
  jSearchPendingGuestLog,
  jWebhookGuestLog,
} from './guest-log.validation';

const router = express.Router();

router.post(
  '/webhook',
  webhookAuth,
  createValidator('body', jWebhookGuestLog, eWebhookGuestLog),
  webhookGuestLog,
);

router.get(
  '/pending',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuestLog,
  //   RolePerms.viewSearchGuestLog,
  //   RolePerms.viewGuestInDuty,
  //   RolePerms.viewGuestSummary,
  // ]),
  createValidator('query', jSearchPendingGuestLog, eSearchPendingQuery),
  searchPendingGuestLogs,
);

router.delete(
  '/pending',
  checkAuth,
  // hasPerm([RolePerms.editGuestLog]),
  createValidator('body', jDeleteGuestLogs, eWebhookGuestLog),
  deletePendingGuests,
);

router.put(
  '/:id/return-card',
  checkAuth,
  // hasPerm([RolePerms.editGuestLog]),
  createValidator(eIdInParams.part ?? 'params', jIdInParams, eIdInParams),
  returnCard,
);

router.get(
  '/summary',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuestLog,
  //   RolePerms.viewGuestInDuty,
  //   RolePerms.viewGuestSummary,
  // ]),
  guestLogSummary,
);

router.get(
  '/group/by-vehicle',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuestLog,
  //   RolePerms.viewGuestInDuty,
  //   RolePerms.viewGuestSummary,
  // ]),
  createValidator('query', jSearchGuestLogByVehicle, eSearchPendingQuery),
  guestByGroupVehicle,
);

router.get(
  '/group/by-check-in-time',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuestLog,
  //   RolePerms.viewGuestInDuty,
  //   RolePerms.viewGuestSummary,
  // ]),
  createValidator('query', jSearchGuestLogByVehicle, eSearchPendingQuery),
  guestByCheckInTime,
);

router.get(
  '/group/by-organization',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuestLog,
  //   RolePerms.viewGuestInDuty,
  //   RolePerms.viewGuestSummary,
  // ]),
  createValidator('query', jSearchGuestLogByVehicle, eSearchPendingQuery),
  guestByOrg,
);

router.get(
  '/stats/by-org-code',
  checkAuth,
  createValidator('query', jGuestLogStatsByOrgCode, eSearchPendingQuery),
  guestLogStatsByOrgCode,
);

router.get(
  '/history-in-out',
  checkAuth,
  createValidator('query', jGuestLogHistoryInOut, eSearchPendingQuery),
  guestLogHistoryInOut,
);

router.post(
  '',
  checkAuth,
  // hasPerm([RolePerms.editGuestLog]),
  createValidator('body', jBodyGuestLog, eWebhookGuestLog),
  createGuestLog,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editGuestLog]),
  createValidator(eIdInParams.part ?? 'params', jIdInParams, eIdInParams),
  createValidator('body', jBodyGuestLog, eWebhookGuestLog),
  editGuestLog,
);

router.get(
  '',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuestLog,
  //   RolePerms.viewSearchGuestLog,
  //   RolePerms.viewExportGuestLog,
  //   RolePerms.viewGuestInDuty,
  //   RolePerms.viewLookupGuestInDuty,
  //   RolePerms.viewGuestSummary,
  // ]),
  createValidator('query', jSearchGuestLog, eSearchPendingQuery),
  searchGuestLogs,
);

router.delete(
  '',
  checkAuth,
  // hasPerm([RolePerms.editGuestLog]),
  createValidator('body', jDeleteGuestLogs, eWebhookGuestLog),
  deleteGuestLogs,
);

router.get(
  '/id-vehicle-log/:id',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuestLog,
  //   RolePerms.viewSearchGuestLog,
  //   RolePerms.viewExportGuestLog,
  //   RolePerms.viewGuestInDuty,
  //   RolePerms.viewLookupGuestInDuty,
  //   RolePerms.viewGuestSummary,
  // ]),
  createValidator(
    eIdVehicleLogInParams.part ?? 'params',
    jIdVehicleLogInParams,
    eIdVehicleLogInParams,
  ),
  guestLogDetailByIdVehicleLog,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuestLog,
  //   RolePerms.viewSearchGuestLog,
  //   RolePerms.viewExportGuestLog,
  //   RolePerms.viewGuestInDuty,
  //   RolePerms.viewLookupGuestInDuty,
  //   RolePerms.viewGuestSummary,
  // ]),
  createValidator(eIdInParams.part ?? 'params', jIdInParams, eIdInParams),
  guestLogDetail,
);

router.get(
  '/tkbc/docx',
  checkAuth,
  // hasPerm([RolePerms.viewGuestLog, RolePerms.viewExportGuestLog]),
  createValidator('query', jSearchGuestLog, eSearchPendingQuery),
  docxGuestLogs,
);

export default router;
