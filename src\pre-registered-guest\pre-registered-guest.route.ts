import express from 'express';
import { checkAuth } from '../middlewares/auth.middleware';
import { createValidator } from '../utils/validator';
import {
  createPreRegisteredGuest,
  deletePreRegisteredGuest,
  editPreRegisteredGuest,
  preRegisteredGuestDetail,
  preRegisteredGuestDetailByGuestCardIdActive,
  returnCard,
  searchPreRegisteredGuest,
} from './pre-registered-guest.controller';
import {
  eBodyPreRegisteredGuest,
  eDeletePreRegisteredGuest,
  eIdInParams,
  eIdInParamsGuestCardId,
  eSearchPreRegisteredGuestQuery,
  jBodyPreRegisteredGuest,
  jDeletePreRegisteredGuest,
  jIdInParams,
  jIdInParamsGuestCardId,
  jSearchPreRegisteredGuest,
} from './pre-registered-guest.validation';

const router = express.Router();

router.post(
  '',
  checkAuth,
  // hasPerm([RolePerms.editBusinessGuest]),
  createValidator('body', jBodyPreRegisteredGuest, eBodyPreRegisteredGuest),
  createPreRegisteredGuest,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editBusinessGuest]),
  createValidator(eIdInParams.part ?? 'params', jIdInParams, eIdInParams),
  createValidator('body', jBodyPreRegisteredGuest, eBodyPreRegisteredGuest),
  editPreRegisteredGuest,
);

router.put(
  '/:id/return-card',
  checkAuth,
  // hasPerm([RolePerms.editBusinessGuest]),
  createValidator(eIdInParams.part ?? 'params', jIdInParams, eIdInParams),
  returnCard,
);

router.get(
  '',
  checkAuth,
  // hasPerm([RolePerms.viewBusinessGuest]),
  createValidator(
    'query',
    jSearchPreRegisteredGuest,
    eSearchPreRegisteredGuestQuery,
  ),
  searchPreRegisteredGuest,
);

router.delete(
  '',
  checkAuth,
  // hasPerm([RolePerms.editBusinessGuest]),
  createValidator('body', jDeletePreRegisteredGuest, eDeletePreRegisteredGuest),
  deletePreRegisteredGuest,
);

router.get(
  '/detail-by-guest-card-id-active/:guestCardId',
  checkAuth,
  // hasPerm([RolePerms.viewGuestLog, RolePerms.viewBusinessGuest]),
  createValidator(
    eIdInParams.part ?? 'params',
    jIdInParamsGuestCardId,
    eIdInParamsGuestCardId,
  ),
  preRegisteredGuestDetailByGuestCardIdActive,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewGuestLog, RolePerms.viewBusinessGuest]),
  createValidator(eIdInParams.part ?? 'params', jIdInParams, eIdInParams),
  preRegisteredGuestDetail,
);

export default router;
