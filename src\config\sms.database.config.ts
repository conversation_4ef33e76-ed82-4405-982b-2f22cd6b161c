import dotenv from 'dotenv';
dotenv.config();

export const mssqlConfig = {
  type: 'mssql',
  host: process.env.SMS_DB_HOST,

  port: parseInt(process.env.SMS_DB_PORT!) || 1433,
  username: process.env.SMS_DB_USER,
  password: process.env.SMS_DB_PW,
  database: process.env.SMS_DB,
  options: {
    encrypt: true, // If encryption is used
    trustServerCertificate: true, // Allow self-signed certificates
    enableArithAbort: true, // Other options as needed
  },
};
