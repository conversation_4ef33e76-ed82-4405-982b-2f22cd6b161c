import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { Type } from 'src/models';
import { ERROR } from '../utils/error';
import {
  findTypes,
  updateType,
  findTypeById,
  insertType,
} from './types.service';

export const getTypes = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const types = await findTypes(req.query);
    return res.json(types);
  } catch (e) {
    next(e);
  }
};

export const putType = async (
  req: Request & { body: Type },
  res: Response,
  next: NextFunction,
) => {
  try {
    const type: Type = req.body;
    const id = Number(req.params.id);
    const putType = await updateType(id, type);
    return res.json(putType.res);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }

    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getTypeById = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = req.params.id;
    const type = await findTypeById(id);
    if (type) return res.json(type);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const postType = async (
  req: Request & { body: Type },
  res: Response,
  next: NextFunction,
) => {
  try {
    const type: Type = req.body;
    const result = await insertType(type);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_EXISTED) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Type đã tồn tại',
        statusCode: 400,
        errors: { field: 'name, scope', message: 'Type đã tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
