import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import {
  addUpdateHandleError,
  createReportError,
  deleteReportError,
  editReportError,
  findReportError,
  findReportErrors,
} from './report-errors.service';
import { deleteFiles } from '../files/files.service';
import { FileTypes, RolePerms } from '../constants';
import { User } from '../models';
import { ISearchQuery } from 'src/types/req';
import { IGetReportErrorsQuery } from './report-errors';
import { IApiError } from 'src/types/validation';

export const postReportError = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const report = req.body;
    const user = req.user as User;
    const getObj = await createReportError(report, user);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const deleteReport = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const getObj = await deleteReportError(id, req);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      const error: IApiError = {
        code: ERROR.DATA_NOT_FOUND,
        message: 'Báo cáo lỗi không tồn tại',
        statusCode: status.BAD_REQUEST,
      };
      return res.status(status.BAD_REQUEST).json(error);
    } else if (e instanceof Error && e.message === ERROR.HANDLE_ERROR_EXISTED) {
      const error: IApiError = {
        code: ERROR.HANDLE_ERROR_EXISTED,
        message: 'Báo cáo lỗi đã được tiếp nhận',
        statusCode: status.BAD_REQUEST,
      };
      return res.status(status.BAD_REQUEST).json(error);
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const deleteReportFiles = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { ids } = req.body;
    const isAdmin = req?.permsArr?.includes(RolePerms.editReportError) || false;
    let userId;
    if (!isAdmin) {
      userId = req.user?.id;
    }
    const getObj = await deleteFiles(ids, FileTypes.errors, userId);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const getReportErrors = async (
  req: Request & { query: ISearchQuery<IGetReportErrorsQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = await findReportErrors(req.query, req);
    return res.json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const putReportErrors = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const data = await editReportError(id, req.body, req);
    return res.json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getReportError = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const data = await findReportError(id, req);
    return res.json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const addUpdateHandle = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const data = await addUpdateHandleError(id, req.body, req);
    return res.json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
