import express from 'express';
import { checkAuth } from '../middlewares';
import { eMessage } from '../roles/roles.validation';
import { createValidator } from '../utils/validator';
import {
  deleteUserGuide,
  exportUserGuideDocx,
  getTreeUserGuide,
  getUserGuideBySlug,
  postUserGuide,
  putUserGuide,
} from './user-guide.controller';
import {
  eSlugInParams,
  jBodyPostUserGuide,
  jBodyPutUserGuide,
  jSlugInParams,
} from './user-guide.validation';
// import { hasPerm } from '../middlewares/has-perm.middleware';
// import { RolePerms } from '../constants';

const routerUserGuide = express.Router();

routerUserGuide.post(
  '/',
  checkAuth,
  // // hasPerm([RolePerms.editUserGuide]),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPostUserGuide,
    eMessage,
  ),
  postUserGuide,
);

routerUserGuide.get(
  '/:slug',
  checkAuth,
  createValidator(
    eSlugInParams.part ? eSlugInParams.part : 'params',
    jSlugInParams,
    eSlugInParams,
  ),
  getUserGuideBySlug,
);

routerUserGuide.put(
  '/:slug',
  checkAuth,
  // // hasPerm([RolePerms.editUserGuide]),
  createValidator(
    eSlugInParams.part ? eSlugInParams.part : 'params',
    jSlugInParams,
    eSlugInParams,
  ),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPutUserGuide,
    eMessage,
  ),
  putUserGuide,
);

routerUserGuide.get('/tree/all', checkAuth, getTreeUserGuide);
routerUserGuide.delete(
  '/:slug',
  checkAuth,
  // // hasPerm([RolePerms.editUserGuide]),
  createValidator(
    eSlugInParams.part ? eSlugInParams.part : 'params',
    jSlugInParams,
    eSlugInParams,
  ),
  deleteUserGuide,
);

routerUserGuide.get('/export/docx', exportUserGuideDocx);

export default routerUserGuide;
