import Joi from 'joi';
import { IApiError } from 'src/types/validation';

export const jCreateEquipHandover = Joi.object({
  detailDutyNowId: Joi.number().required(),
  detailDutyFeatureId: Joi.number().required(),
  date: Joi.date().required(),
  values: Joi.string().required(),
  codeOrg: Joi.string().required(),
  equipmentSituation: Joi.string().optional(),
  nameDutyNow: Joi.string().required(),
  nameDutyFeature: Joi.string().required(),
});

export const eCreateEquipHandover: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: '<PERSON><PERSON> liệu không hợp lệ',
  statusCode: 400,
};

export const jUpdateEquipHandover = Joi.object({
  detailDutyNowId: Joi.number().optional(),
  detailDutyFeatureId: Joi.number().optional(),
  date: Joi.date().optional(),
  values: Joi.string().optional(),
  nameDutyNow: Joi.string().optional(),
  nameDutyFeature: Joi.string().optional(),
  codeOrg: Joi.string().required(),
  equipmentSituation: Joi.string().optional(),
});

export const eUpdateEquipHandover: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

export const jSearchEquipHandover = Joi.object({
  page: Joi.number().optional().default(1),
  limit: Joi.number().optional().default(10),
  codeOrg: Joi.string().optional(),
  detailDutyNowId: Joi.number().optional(),
  detailDutyFeatureId: Joi.number().optional(),
  date: Joi.date().optional(),
  values: Joi.string().optional(),
  nameDutyNow: Joi.string().optional(),
  nameDutyFeature: Joi.string().optional(),
  fromDate: Joi.date().optional(),
  toDate: Joi.date().optional(),
  nameCommand: Joi.string().optional(),
  nameChiefDuty: Joi.string().optional(),
  nameDeputyDuty: Joi.string().optional(),
});

export const eSearchEquipHandover: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

export const jIdEquipHandover = Joi.object({
  id: Joi.number().required(),
});

export const eIdEquipHandover: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};
