import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
  Unique,
} from 'typeorm';
import { Role } from './role.model';
import { Permission } from './permissions.model';

@Entity('roles_permissions')
@Unique(['roleId', 'permissionId'])
export class RolePermission {
  @PrimaryGeneratedColumn()
  public id?: number;

  @Column()
  public roleId!: number;

  @Column()
  public permissionId!: number;

  @ManyToOne(() => Role, (role) => role.rolePermissions)
  public role!: Role;

  @ManyToOne(() => Permission, (permission) => permission.rolePermissions)
  public permission!: Permission;
}
