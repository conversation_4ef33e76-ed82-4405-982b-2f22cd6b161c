export type ISearcEQNQuery = {
  start_date?: string;
  end_date?: string;
  name?: string;
  typeId?: number;
  codeOrg?: string;
  limit: number;
  page: number;
};

export type IListEQNQuery = {
  rankId?: string;
  typeId?: string;
  orgCode?: string;
  positionId?: number;
  fullText?: string;
  limit: number;
  page: number;
};

export type IDashboardQuery = {
  codeOrg?: string;
};

export type IDashboard = {
  totalEqnCount: number;
  organization: {
    id: string;
    name: string;
  };
  childOrgs: IDashboardChildOrg[];
};

export type IDashboardChildOrg = {
  organization: {
    id: string;
    name: string;
  };
  totalEqnCountByOrg: number;
};
