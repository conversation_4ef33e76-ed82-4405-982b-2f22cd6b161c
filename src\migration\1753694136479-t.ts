import { MigrationInterface, QueryRunner } from 'typeorm';

export class T1753694136479 implements MigrationInterface {
  name = 'T1753694136479';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chucVuLoaiHinhTruc" DROP CONSTRAINT "FK_5e9d2220837e6ba9e027faba9c8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "chucVuLoaiHinhTruc" ADD CONSTRAINT "FK_5e9d2220837e6ba9e027faba9c8" FOREIGN KEY ("maChucVu") REFERENCES "position_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "chucVuLoaiHinhTruc" DROP CONSTRAINT "FK_5e9d2220837e6ba9e027faba9c8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "chucVuLoaiHinhTruc" ADD CONSTRAINT "FK_5e9d2220837e6ba9e027faba9c8" FOREIGN KEY ("maChucVu") REFERENCES "military_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
