import { database } from '../config';
import { Permission, Role, RolePermission, User } from '../models';
import { ISearchQuery } from '../types/req';
import { IApiError } from '../types/validation';
import { ERROR } from '../utils/error';
import log from '../utils/log';
import { IGetRolesQuery } from './roles';
import { createLinks, DEFAULT_LIMIT } from '../utils/pagination';
import { FindOptionsWhere, In } from 'typeorm';
import { status } from 'http-status';

function createFullText(role: Partial<Role>): string {
  const fields = [role.name, role.desc, role.levelData];
  return fields.filter(Boolean).join(' ');
}
function isValidUppercaseUnderscoreFormat(input: string): boolean {
  const regex = /^[A-Z]+(_[A-Z]+)*$/;
  return regex.test(input);
}
export const insertRole = async (role: Role) => {
  const errors: IApiError['errors'] = [];
  const repoRole = database.getRepository(Role);

  if (isValidUppercaseUnderscoreFormat(role.name || '')) {
    const exitsName = await repoRole
      .createQueryBuilder('roles')
      .select('')
      .where('roles.name= :name', { name: role.name })
      .andWhere('roles.status =1')
      .getRawMany();

    if (exitsName && exitsName.length > 0) {
      errors.push({
        field: 'name',
        value: `exitsName:${role.name}`,
        message: 'Tên Role đã tồn tại.',
      });
    }
  } else {
    errors.push({
      field: 'name',
      value: `exitsName: ${role.name}`,
      message: 'Tên Role không đúng quy tắc đặt.',
    });
  }

  const error = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
    errors,
  };
  if (errors && errors.length > 0) {
    return { error };
  }
  role.fullText = createFullText(role);

  log.debug('role:', role);
  const tempRole = repoRole.create(role);
  const newRole = await repoRole.save(tempRole);

  return { newRole };
};
export const updateRole = async (id: number, body: Partial<Role>) => {
  const errors: IApiError['errors'] = [];
  const repoRole = database.getRepository(Role);

  const existed = await repoRole.findOne({ where: { id } });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  if (body.name && body.name !== existed.name) {
    if (isValidUppercaseUnderscoreFormat(body.name)) {
      const exitsName = await repoRole
        .createQueryBuilder('roles')
        .select('')
        .where('roles.name= :name', { name: body.name })
        .getRawMany();

      if (exitsName && exitsName.length > 0) {
        errors.push({
          field: '',
          value: `exitsName: ${body.name}`,
          message: 'Tên role đã tồn tại.',
        });
      }
    } else {
      errors.push({
        field: 'name',
        value: `exitsName: ${body.name}`,
        message: 'Tên Role không đúng quy tắc đặt.',
      });
    }
  }

  if (errors && errors.length > 0) {
    const error: IApiError = {
      part: 'body',
      code: ERROR.BAD_REQUEST,
      message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
      errors,
      statusCode: status.BAD_REQUEST,
    };
    return { error };
  }
  const role = { ...existed, ...body };
  role.fullText = createFullText(role);
  const updated = await repoRole.save(role);
  return { updated };
};
export const dropRole = async (id: number) => {
  const repoRole = database.getRepository(Role);

  const existed = await repoRole.findOne({
    where: { id },
    relations: { rolePermissions: true },
  });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  existed.status = true;
  const deleted = await repoRole.save(existed);
  return { deleted };
};
export const findById = async (id: number) => {
  const repo = database.getRepository(Role);
  const role = await repo.findOne({
    where: { id, status: true },
    relations: [
      'rolePermissions',
      'users',
      'users.organization',
      'users.position',
      'users.rank',
    ],
  });
  if (!role) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  return role;
};
export const find = async (query: ISearchQuery<IGetRolesQuery>) => {
  const repo = database.getRepository(Role);
  const conditions = repo
    .createQueryBuilder('roles')
    .orderBy('roles.id', 'DESC');

  if (query.search && query.search != '') {
    conditions.andWhere('LOWER(roles.fullText) like LOWER(:search)', {
      search: `%${query.search}%`,
    });
  }

  if (query.name && query.name != '') {
    conditions.andWhere('LOWER(roles.name) like LOWER(:name)', {
      name: `%${query.name}%`,
    });
  }

  if (query.desc && query.desc != '') {
    conditions.andWhere('LOWER(roles.desc) like LOWER(:desc)', {
      desc: `%${query.desc}%`,
    });
  }

  if (query.levelData) {
    if (Array.isArray(query.levelData)) {
      conditions.andWhere(`roles.levelData IN (:...levelData)`, {
        levelData: query.levelData,
      });
    } else {
      conditions.andWhere('roles.levelData = :levelData', {
        levelData: query.levelData,
      });
    }
  }

  const limit: number = query.limit ? Number(query.limit) : DEFAULT_LIMIT;
  const page: number = query.page ? Number(query.page) : 1;

  const totalItems = await conditions.getCount();
  const totalPages = Math.ceil(totalItems / limit);
  const links = createLinks('/roles?', query, page, totalPages);

  const roles = await conditions
    .skip((page - 1) * limit)
    .take(limit)
    .getMany();

  return {
    meta: {
      totalItems,
      itemsPerPage: query.limit,
      totalPages,
      currentPages: query.page,
      items: [...roles],
    },
    links,
  };
};
export const addPermissions2Role = async (
  roleId: number,
  permissionIds: number[],
) => {
  log.debug('addPermissions2Role');
  const repoRole = database.getRepository(Role);
  const repoPermission = database.getRepository(Permission);
  const repoRolePermission = database.getRepository(RolePermission);

  const curRole = await repoRole.findOneBy({ id: roleId, status: true });
  if (!curRole) throw new Error(ERROR.DATA_NOT_FOUND);
  const errors: IApiError['errors'] = [];
  const rolePermissionPromise = await Promise.all(
    permissionIds.map(async (permissionId) => {
      const curPer = await repoPermission.findOneBy({
        id: permissionId,
        status: 1,
      });
      if (!curPer) {
        errors?.push({
          value: `permissionId: ${permissionId}`,
          message: 'permissionId cần thêm không tồn tại',
        });
        return null;
      }
      let rolePer = await repoRolePermission.findOneBy({
        roleId,
        permissionId,
      });
      if (!rolePer) rolePer = new RolePermission();
      else return rolePer;
      rolePer.permissionId = permissionId;
      rolePer.roleId = roleId;

      const tempRolePer = repoRolePermission.create(rolePer);
      const newRolePer = await repoRolePermission.save(tempRolePer);
      return newRolePer;
    }),
  );
  const rolePermissions = rolePermissionPromise.filter((item) => item !== null);
  const role = await repoRole.findOne({
    relations: ['rolePermissions'],
    where: { id: roleId },
  });

  if (errors && errors.length > 0)
    return {
      role,
      added: rolePermissions,
      count: rolePermissions.length,
      error: {
        part: 'body',
        code: ERROR.DATA_ERROR,
        message: 'Check input of names, LL inputs!',
        errors,
      },
    };
  return { role, added: rolePermissions, count: rolePermissions.length };
};
export const removePermissions2Role = async (
  roleId: number,
  permissionsId: number[],
) => {
  const errors: IApiError['errors'] = [];
  const repoRole = database.getRepository(Role);
  const repoRolePermission = database.getRepository(RolePermission);
  const whereRolePer: FindOptionsWhere<RolePermission> = {};
  let idExistedPres: number[] = [];
  const idNotExistedRolePres: number[] = [];

  const curRole = await repoRole.findOneBy({ id: roleId, status: true });
  if (!curRole) throw new Error(ERROR.DATA_NOT_FOUND);

  whereRolePer.roleId = Number(roleId);
  whereRolePer.permissionId = In(permissionsId ?? []);
  const existedRolePres = await repoRolePermission.find({
    where: whereRolePer,
  });

  if (existedRolePres && existedRolePres.length > 0) {
    idExistedPres = existedRolePres
      ? existedRolePres.map((item) => Number(item.permissionId))
      : [];
    await repoRolePermission.remove(existedRolePres);
  }
  const role = await repoRole.findOne({
    relations: { rolePermissions: true },
    where: { id: Number(roleId), status: true },
  });
  permissionsId.map((item) => {
    if (!idExistedPres.includes(Number(item))) {
      idNotExistedRolePres.push(Number(item));
      errors.push({
        field: 'id',
        value: `id: ${item.toString()}`,
        message: 'Không tồn tại permission của role.',
      });
    }
  });

  if (errors && errors.length > 0)
    return {
      role,
      deleted: existedRolePres,
      count: existedRolePres.length,
      error: {
        part: 'body',
        code: ERROR.DATA_ERROR,
        message: 'Check input of names, LL inputs!',
        errors,
      },
    };

  return { role, deleted: existedRolePres, count: existedRolePres.length };
};

export const changeStatusRoles = async (ids: number[]) => {
  const repoRole = database.getRepository(Role);

  const existRoles = await repoRole.find({ where: { id: In(ids) } });

  const existRoleIds = existRoles.map((item) => item.id);

  const notExistRoleIds = ids.filter((item) => !existRoleIds.includes(item));

  const updateSuccess = await repoRole.update(
    { id: In(existRoleIds) },
    { status: false },
  );
  if (updateSuccess.affected === 0) {
    return updateSuccess;
  } else return { updateSuccess, errors: notExistRoleIds };
};

export const addUser2Role = async (id: number, userIds: number[]) => {
  const repoRole = database.getRepository(Role);
  const repoUser = database.getRepository(User);

  const existRoles = await repoRole.find({ where: { id } });
  if (!existRoles) throw new Error(ERROR.DATA_NOT_FOUND);

  const existUsers = await repoUser.find({ where: { id: In(userIds) } });

  const existUserIds = existUsers.map((item) => item.id);

  const notExistUserIds = userIds.filter(
    (item) => !existUserIds.includes(item),
  );

  const updateSuccess = await repoUser.update(
    { id: In(existUserIds) },
    { roleId: id },
  );
  if (updateSuccess.affected === 0) {
    return updateSuccess;
  } else return { updateSuccess, errors: notExistUserIds };
};

export const dropUser2Role = async (id: number, userIds: number[]) => {
  const repoRole = database.getRepository(Role);
  const repoUser = database.getRepository(User);

  const existRoles = await repoRole.find({ where: { id } });
  if (!existRoles) throw new Error(ERROR.DATA_NOT_FOUND);

  const existUsers = await repoUser.find({ where: { id: In(userIds) } });

  const existUserIds = existUsers.map((item) => item.id);

  const notExistUserIds = userIds.filter(
    (item) => !existUserIds.includes(item),
  );

  const updateSuccess = await repoUser.update(
    { id: In(existUserIds) },
    { roleId: null },
  );
  if (updateSuccess.affected === 0) {
    return updateSuccess;
  } else return { updateSuccess, errors: notExistUserIds };
};
