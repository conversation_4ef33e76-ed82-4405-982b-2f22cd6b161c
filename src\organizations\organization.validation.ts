import Joi from 'joi';
import { IApiError } from '../types/validation';

const jIdInParams = Joi.object({
  id: Joi.string().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng mã Code chưa chính xác',
  errors: { code: 'Code Định dạng chọn chính xác' },
  statusCode: 400,
};

const jBodyOrganizations = Joi.object({
  code: Joi.string().required(),
  name: Joi.string().min(1).max(300).required(),
  desc: Joi.string(),
  shortName: Joi.string().required(),
  parentId: Joi.string().min(0),
  orderNumber: Joi.number().required(),
  isEnable: Joi.boolean(),
});

const jBodyPutOrganizations = Joi.object({
  code: Joi.string().required(),
  name: Joi.string().min(1).max(300).required(),
  desc: Joi.string(),
  shortName: Joi.string().required(),
  orderNumber: Joi.number().required(),
  parentId: Joi.string().min(0),
  isEnable: Joi.boolean(),
});

const eAddOrganizationBody: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng  chưa chính xác',
  statusCode: 400,
};

export const jGetTreeQuery = Joi.object({
  depth: Joi.number().min(0),
});
export const eGetTreeQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Có thể thêm tham số depth',
  statusCode: 400,
};

const jSearchAllOrganizations = Joi.object({
  depth: Joi.number().min(0),
  name: Joi.string().min(1).max(300),
  desc: Joi.string(),
  search: Joi.string(),
  status: Joi.boolean(),
  shortName: Joi.string(),
  privateName: Joi.string(),
  parentId: Joi.string(),
  ids: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
  codes: Joi.alternatives().try(Joi.string(), Joi.array().items(Joi.string())),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

const eSearchAllQuery: IApiError = {
  part: 'query',
  code: 'Định dạng chuỗi tìm kiếm chưa chính xác',
  message: '',
  statusCode: 400,
};

const jPutOrganizations = Joi.object({
  code: Joi.string(),
  name: Joi.string().min(1).max(300),
  desc: Joi.string(),
  shortName: Joi.string(),
  address: Joi.string(),
  order: Joi.number(),
  privateName: Joi.string(),
  parentId: Joi.string(),
  orderNumber: Joi.number(),
  isEnable: Joi.boolean(),
});

export {
  jIdInParams,
  eIdInParams,
  jBodyOrganizations,
  eAddOrganizationBody,
  jSearchAllOrganizations,
  eSearchAllQuery,
  jPutOrganizations,
  jBodyPutOrganizations,
};
