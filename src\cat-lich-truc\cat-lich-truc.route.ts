import express from 'express';
import { checkAuth } from '../middlewares/auth.middleware';
import { createCauHinheQNLoaiHinhTruc } from './cat-lich-truc.controller';
import { createValidator } from '../utils/validator';
import {
  eCreate,
  jBodyCauHinheQNLoaiHinhTruc,
} from './cat-lich-truc.validation';
const router = express.Router();
router.post(
  '/',
  checkAuth,
  createValidator('body', jBodyCauHinheQNLoaiHinhTruc, eCreate),
  createCauHinheQNLoaiHinhTruc,
);
export default router;
