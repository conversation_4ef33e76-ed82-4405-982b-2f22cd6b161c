import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Guest } from './guest.model';
import { Organization } from './organization.model';
import { GuardPost } from './guardPost.model';
import { Type } from './type.model';
import { eQN } from './eQN.model';
import { CatLichTruc } from './catLichTruc.model';

@Entity('guestLogs')
export class GuestLog extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'id_vehicle_log',
    type: 'nvarchar',
    length: 50,
    nullable: true,
    default: null,
  })
  idVehicleLog?: string;

  @Column({
    name: 'vehicle_data',
    type: 'nvarchar',
    nullable: true,
    default: null,
  })
  vehicleData?: string;

  // Thông tin người tiếp khách
  @Column({
    name: 'meet_who_id',
    type: 'varchar',
    nullable: true,
    default: null,
    length: 36,
  })
  meetWhoId?: string;

  @ManyToOne(() => eQN)
  @JoinColumn({ name: 'meet_who_id' })
  meetWho?: eQN;

  // Thông tin đơn vị tiếp khách
  @Column({
    name: 'meet_org_code',
    type: 'nvarchar',
    nullable: true,
    default: null,
    length: 17,
  })
  meetOrgCode?: string;

  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'meet_org_code', referencedColumnName: 'code' })
  meetOrganization?: Organization;

  // guest data
  @Column({
    name: 'guest_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  guestId?: number;
  @ManyToOne(() => Guest)
  @JoinColumn({ name: 'guest_id' })
  guest?: Guest;

  @Column({
    name: 'guest_type_id',
    type: 'int',
  })
  guestTypeId?: number; // Loại khách
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'guest_type_id' })
  guestType?: Type; // Loại khách

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    nullable: true,
    default: null,
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization; // Thông tin đơn vị

  @Column({
    name: 'guest_code_org',
    type: 'nvarchar',
    nullable: true,
    default: null,
    length: 17,
  })
  guestCodeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'guest_code_org', referencedColumnName: 'code' })
  guestOrganization?: Organization; // Thông tin đơn vị của khách

  @Column({
    name: 'guest_organization_name',
    type: 'nvarchar',
    length: 250,
    default: null,
    nullable: true,
  })
  guestOrganizationName?: string; // Tên đơn vị của khách

  @Column({
    name: 'is_civil',
    type: 'bit',
    default: false,
  })
  isCivil?: boolean; // dân sự

  @Column({
    name: 'check_in_time',
    type: 'datetime2',
  })
  checkInTime?: Date;

  @Column({
    name: 'check_out_time',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  checkOutTime?: Date;

  @Column({
    name: 'estimate_out_time',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  estimatedOutTime?: Date;

  @Column({
    name: 'default_image',
    type: 'text',
    nullable: true,
    default: null,
  })
  defaultImage?: string; // hình ảnh mặc định

  @Column({
    name: 'vehicle_type_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  vehicleTypeId?: number; // loại xe
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'vehicle_type_id' })
  vehicleType?: Type; // loại xe

  @Column({
    name: 'vehicle_classification_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  vehicleClassificationId?: number; // Phân loại xe
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'vehicle_classification_id' })
  vehicleClassification?: Type; // Phân loại xe

  @Column({
    name: 'etiquette',
    type: 'nvarchar',
    length: 200,
    nullable: true,
    default: null,
  })
  etiquette?: string; // lễ tiết tác phong

  @Column({
    name: 'guard_post_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  guardPostId?: number; // vị trí (Chòi gác nơi xe vào) guardPostId
  @ManyToOne(() => GuardPost)
  @JoinColumn({ name: 'guard_post_id' })
  guardPost?: GuardPost; // vị trí (Chòi gác nơi xe vào)

  @Column({
    name: 'status_id',
    type: 'int',
  })
  statusId?: number; // trạng thái làm việc
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'status_id' })
  status?: Type; // trạng thái làm việc

  @Column({
    name: 'purpose_category_id',
    type: 'int',
  })
  purposeCategoryId?: number;
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'purpose_category_id' })
  purposeCategory?: Type;

  @Column({
    name: 'purpose_of_visit',
    type: 'nvarchar',
    length: 1000,
    nullable: true,
    default: null,
  })
  purposeOfVisit?: string;

  @Column({
    name: 'group_name',
    type: 'nvarchar',
    length: 250,
    nullable: true,
    default: null,
  })
  groupName?: string;

  @Column({
    name: 'leader_name',
    type: 'nvarchar',
    length: 250,
    nullable: true,
    default: null,
  })
  leaderName?: string;

  @Column({
    name: 'leader_position',
    type: 'nvarchar',
    length: 250,
    nullable: true,
    default: null,
  })
  leaderPosition?: string;

  @Column({
    name: 'number_of_visitors',
    type: 'int',
    default: 1,
  })
  numberOfVisitors?: number;

  @Column({
    name: 'number_of_vehicles',
    type: 'int',
    default: 0,
  })
  numberOfVehicles?: number;

  @Column({
    name: 'number_of_visitors_out',
    type: 'int',
    nullable: true,
    default: 0,
  })
  numberOfVisitorsOut?: number;

  @Column({
    name: 'parent_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  parentId?: number;

  @ManyToOne(() => GuestLog)
  @JoinColumn({ name: 'parent_id' })
  parent?: GuestLog;

  @OneToMany(() => GuestLog, (guestLog) => guestLog.parent)
  children?: GuestLog[];

  @Column({
    name: 'duration_inside',
    type: 'int',
    nullable: true,
    default: null,
  })
  durationInside?: number;

  // thông tin thẻ của khách
  @Column({
    name: 'guest_card_id',
    type: 'nvarchar',
    length: 50,
    nullable: true,
    default: null,
  })
  guestCardId?: string;

  @Column({
    name: 'issued_time',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  issuedTime?: Date;

  @Column({
    name: 'return_time',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  returnTime?: Date;

  // Thông tin trực ban
  @Column({
    name: 'detail_duty_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  detailDutyId?: number; // Thông tin về lịch trực ban
  @ManyToOne(() => CatLichTruc)
  @JoinColumn({ name: 'detail_duty_id' })
  detailDuty?: CatLichTruc; // Thông tin về lịch trực ban

  @Column({
    name: 'detail_duty_out_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  detailDutyOutId?: number; // Thông tin về lịch trực ban
  @ManyToOne(() => CatLichTruc)
  @JoinColumn({ name: 'detail_duty_out_id' })
  detailDutyOut?: CatLichTruc; // Thông tin về lịch trực ban

  @Column({
    name: 'guest_id_by',
    type: 'varchar',
    length: 36,
    nullable: true,
    default: null,
  })
  guestIdBy?: string; // người cho phép vào (Cán bộ TBNV)
  @ManyToOne(() => eQN)
  @JoinColumn({ name: 'guest_id_by' })
  guestBy?: eQN; // người cho phép vào (Cán bộ TBNV)

  @Column({
    name: 'notes',
    type: 'nvarchar',
    length: 1000,
    nullable: true,
    default: null,
  })
  notes?: string;

  @Column({
    name: 'is_leader',
    type: 'bit',
    default: false,
  })
  isLeader?: boolean;

  @Column({
    name: 'is_send_message_for_meet_user',
    type: 'bit',
    default: false,
  })
  isSendMessageForMeetUser?: boolean;

  @BeforeInsert()
  beforeCreate() {
    const cols = [
      this.groupName,
      this.etiquette,
      this.leaderName,
      this.leaderPosition,
      this.notes,
      this.vehicleData,
    ];
    this.fullText = cols.join(' ')?.trim();
  }
}
