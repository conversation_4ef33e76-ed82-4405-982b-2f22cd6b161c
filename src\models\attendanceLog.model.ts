import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ATTENDANCE_STATUS } from '../timesheet/constants';
import EntityModel from './entity.model';
import { eQN } from './eQN.model';
import { Organization } from './organization.model';

@Entity('attendanceLogs')
export class AttendanceLog extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'qneid',
    type: 'varchar',
    length: 36,
  })
  qneid?: string;

  @Column({
    name: 'required_start_time',
    type: 'datetime2',
  })
  requiredStartTime?: Date;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;

  @Column({
    name: 'late_duration',
    type: 'int',
  })
  lateDuration?: number;

  @Column({
    name: 'reason_text',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  reasonText?: string;

  @Column({
    name: 'approved_by',
    type: 'nvarchar',
    length: 250,
  })
  approvedBy?: string;

  @Column({
    name: 'approved_at',
    type: 'datetime2',
  })
  approvedAt?: Date;

  @Column({
    name: 'location_check',
    type: 'nvarchar',
    length: 500,
  })
  locationCheck?: string;

  @Column({
    name: 'check_type_id',
    type: 'int',
  })
  checkTypeId?: number;

  @Column({
    name: 'attendance_image',
    type: 'nvarchar',
    length: 'MAX',
    nullable: true,
  })
  attendanceImage?: string;

  @Column({
    name: 'check_in_time',
    type: 'datetime2',
  })
  checkInTime?: Date;

  @Column({
    name: 'check_date',
    type: 'datetime2',
  })
  checkDate?: Date;

  @Column({
    name: 'status',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  status?: ATTENDANCE_STATUS;

  @ManyToOne(() => eQN)
  @JoinColumn({
    name: 'qneid',
    referencedColumnName: 'id',
  })
  eQN?: eQN;

  @ManyToOne(() => Organization, (organization) => organization.id)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;

  // @ManyToOne(() => Type, { createForeignKeyConstraints: false })
  // @JoinColumn({ name: 'check_type_id', referencedColumnName: 'value' })
  // checkInType: Type;

  @Column({
    name: 'biometricData',
    type: 'nvarchar',
    length: 'MAX',
    nullable: true,
  })
  biometricData?: string;
}
