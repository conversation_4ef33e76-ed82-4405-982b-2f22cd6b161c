import { IsNull } from 'typeorm';
import { dirUploadFile, database } from '../config';
import { UserGuide, UserGuideContent } from '../models';
import { ERROR } from '../utils/error';
import fs from 'fs';
import * as cheerio from 'cheerio';
import { join } from 'path';
import { cwd } from 'process';
import { romanNumber } from '../utils/roman-number';

const HTMLtoDOCX = require('@turbodocx/html-to-docx'); // eslint-disable-line @typescript-eslint/no-require-imports

export const createUserGuide = async (data: UserGuide): Promise<UserGuide> => {
  const userGuideRepo = database.getRepository(UserGuide);
  const userGuideContentRepo = database.getRepository(UserGuideContent);
  await userGuideContentRepo.save({ slug: data.slug });
  const userGuide = userGuideRepo.create({
    ...data,
    parentSlug: data.parentSlug,
  });
  return userGuideRepo.save(userGuide);
};

export const updateUserGuide = async (
  slug: string,
  payload: {
    name: string;
    content: string;
    order: number;
    slug: string;
    parentSlug: string;
  },
) => {
  const userGuideRepo = database.getRepository(UserGuide);
  const userGuide = await userGuideRepo.findOne({ where: { slug } });
  if (!userGuide) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  const res = await userGuideRepo.update(slug, {
    order: payload?.order,
    slug: payload.slug,
    parentSlug: payload.parentSlug,
    name: payload.name,
  });

  if (payload.content) {
    const userGuideContentRepo = database.getRepository(UserGuideContent);
    await userGuideContentRepo.update(slug, { content: payload.content });
  }

  return res;
};

export const findUserGuideBySlug = async (slug: string): Promise<UserGuide> => {
  const userGuideRepo = database.getRepository(UserGuide);
  const userGuide = await userGuideRepo.findOne({
    where: { slug },
    relations: ['content'],
  });
  if (!userGuide) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  return userGuide;
};

export const findTreeUserGuide = async () => {
  const userGuideRepo = database.getRepository(UserGuide);
  const userGuides = await userGuideRepo.find({
    where: { parentSlug: IsNull() },
    relations: { children: { children: { children: { children: true } } } },
    order: {
      order: 'ASC',
      children: {
        order: 'ASC',
        children: {
          order: 'ASC',
          children: { order: 'ASC', children: { order: 'ASC' } },
        },
      },
    },
  });
  return userGuides;
};

export const deleteUserGuideBySlug = async (slug: string) => {
  const userGuideRepo = database.getRepository(UserGuide);
  const userGuide = await userGuideRepo.findOne({
    where: { slug },
    relations: { children: true },
  });
  if (!userGuide) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  if (userGuide.children?.length) {
    throw new Error(ERROR.DATA_HAS_CHILD);
  }
  const userGuideContentRepo = database.getRepository(UserGuideContent);
  await userGuideRepo.delete(slug);
  await userGuideContentRepo.delete({ slug });
};

//  // Create default user guide
// const createDefaultUserGuide = async () => {
//   const userGuideRepo = database.getRepository(UserGuide);
//   const userGuideContentRepo = database.getRepository(UserGuideContent);
//   const insertData = async (userGuide: UserGuide) => {
//     await userGuideContentRepo.save({ slug: userGuide.slug });
//     let newUserGuide = userGuideRepo.create({
//       name: userGuide.name,
//       slug: userGuide.slug,
//       order: userGuide.order,
//       parentSlug: userGuide.parentSlug,
//     });
//     newUserGuide = await userGuideRepo.save(newUserGuide);
//     if (userGuide?.children?.length) {
//       await Promise.all(
//         userGuide.children.map(async (child) => {
//           await insertData({
//             ...child,
//             parentSlug: newUserGuide.slug,
//           });
//         }),
//       );
//     }
//   };

//   await Promise.all(
//     UserGuideData?.map(
//       async (userGuide) => await insertData(userGuide as UserGuide),
//     ),
//   );
// };

export const exportUserGuide = async () => {
  const userGuideRepo = database.getRepository(UserGuide);
  const userGuides = await userGuideRepo.find({
    where: { parentSlug: IsNull() },
    relations: {
      content: true,
      children: {
        content: true,
        children: { content: true, children: { content: true } },
      },
    },
    order: {
      order: 'ASC',
      children: {
        order: 'ASC',
        children: { order: 'ASC', children: { order: 'ASC' } },
      },
    },
  });

  let html = `<div><h3 style="text-align:center; text-transform: uppercase">Phụ lục</h3>`;
  const renderMenuChildren = (
    children: UserGuide[],
    index: number,
    chapter: string,
  ) => {
    children.forEach((child, i) => {
      const newChapter = `${chapter}.${i + 1}`;
      html += `<p style="font-size:18.5px">${newChapter} ${child.name} ..............................</p>`;
      if (child.children?.length) {
        renderMenuChildren(child.children, index, newChapter);
      }
    });
  };

  userGuides.forEach((userGuide, i) => {
    html += `<h3 style="text-transform: uppercase">Chương ${romanNumber(i + 1)}: ${userGuide.name} ..............................</h3>`;
    if (userGuide.children?.length) {
      renderMenuChildren(userGuide.children, 1, romanNumber(i + 1));
    }
  });

  html += `</div>`;

  // render content
  const renderContent = (content: UserGuideContent) => {
    html += `<div style="font-size:18.5px">${content?.content || ''}</div>`;
  };

  const renderChildren = (
    children: UserGuide[],
    index: number,
    chapter: string,
  ) => {
    children.forEach((child, i) => {
      const newChapter = `${chapter}.${i + 1}`;
      html += `<h3>${newChapter} ${child.name}</h3>`;
      if (child?.content) {
        renderContent(child.content);
      }
      if (child.children?.length) {
        renderChildren(child.children, index + 1, newChapter);
      }
    });
  };

  userGuides.forEach((userGuide, i) => {
    html += `<h2 style="text-align:center; text-transform: uppercase">Chương ${romanNumber(i + 1)}: ${userGuide.name}</h2>`;
    if (userGuide?.content) {
      renderContent(userGuide.content);
    }
    if (userGuide.children?.length) {
      renderChildren(userGuide.children, 1, romanNumber(i + 1));
    }
  });

  html = `<html>
  <body>
  ${html}
  </body></html>`;

  const doc = cheerio.load(html);
  doc('img').each((_: any, element: any) => {
    const src = doc(element).attr('src');
    doc(element).removeAttr('style');
    if (src) {
      const pathArr = src.split('\\');
      pathArr.shift();
      const path = pathArr.join('/');
      try {
        const buffer = fs.readFileSync(
          join(cwd(), dirUploadFile.dirUpload, 'user-guides', path),
        );
        doc(element).attr(
          'src',
          `data:image/png;base64,${buffer.toString('base64')}`,
        );
      } catch (_) {
        doc(element).remove();
      }
    }
  });

  doc('p').each((_: any, element: any) => {
    doc(element).attr('style', 'font-size:18.5px');
  });

  doc('li').each((_: any, element: any) => {
    doc(element).attr('style', 'font-size:18.5px');
  });

  html = doc.html() || '';
  const buffer = await HTMLtoDOCX(
    html,
    '',
    { footer: true, pageNumber: true },
    '',
  );
  return buffer;
};
