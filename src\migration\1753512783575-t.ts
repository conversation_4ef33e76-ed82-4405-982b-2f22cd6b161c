import { MigrationInterface, QueryRunner } from "typeorm";

export class T1753512783575 implements MigrationInterface {
    name = 'T1753512783575'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "donviLoaiHinhTruc" ("createdAt" datetime2 NOT NULL CONSTRAINT "DF_8e7aeb0fd164c7b6b98cd2116f1" DEFAULT getdate(), "updatedAt" datetime2 NOT NULL CONSTRAINT "DF_d0ce2b25c8d99dbef5c838f5661" DEFAULT getdate(), "deletedAt" datetime2, "fullText" nvarchar(4000), "id" bigint NOT NULL IDENTITY(1,1), "maLoaiHinhTruc" uniqueidentifier NOT NULL, "maDonVi" varchar(36) NOT NULL, "code" uniqueidentifier, CONSTRAINT "PK_f664b7054890a9aa4135b357280" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_7816e0fa22ab752c8476a64096" ON "donviLoaiHinhTruc" ("fullText") `);
        await queryRunner.query(`ALTER TABLE "dm_loai_hinh_truc" DROP COLUMN "maDonVis"`);
        await queryRunner.query(`ALTER TABLE "dm_loai_hinh_truc" DROP COLUMN "chucVus"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD CONSTRAINT "FK_3a0c2f5411f7661d513fb2ea910" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD CONSTRAINT "FK_20db3f19fff11cb028d83af8729" FOREIGN KEY ("code") REFERENCES "organizations"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP CONSTRAINT "FK_20db3f19fff11cb028d83af8729"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP CONSTRAINT "FK_3a0c2f5411f7661d513fb2ea910"`);
        await queryRunner.query(`ALTER TABLE "dm_loai_hinh_truc" ADD "chucVus" ntext NOT NULL`);
        await queryRunner.query(`ALTER TABLE "dm_loai_hinh_truc" ADD "maDonVis" ntext NOT NULL`);
        await queryRunner.query(`DROP INDEX "IDX_7816e0fa22ab752c8476a64096" ON "donviLoaiHinhTruc"`);
        await queryRunner.query(`DROP TABLE "donviLoaiHinhTruc"`);
    }

}
