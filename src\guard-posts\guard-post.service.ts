import { ISearchQuery } from 'src/types/req';
import { database } from '../config';
import { GuardPost, Organization, User } from '../models';
import { ERROR } from '../utils/error';
import { ISearchGuardPostQuery } from './guard-post';
import { createLinks } from '../utils/pagination';

export const create = async (
  data: GuardPost,
  user: User, // eslint-disable-line @typescript-eslint/no-unused-vars
) => {
  const repo = database.getRepository(GuardPost);
  const newGuardPost = repo.create({ ...data, location: '' });
  const saved = await repo.save(newGuardPost);

  return { data: saved };
};

export const update = async (id: number, data: Partial<GuardPost>) => {
  const repo = database.getRepository(GuardPost);
  const organizationRepo = database.getRepository(Organization);

  const org = await organizationRepo.findOne({
    where: { code: data.codeOrg },
    withDeleted: true,
  });
  if (!org) {
    throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
  }

  const existing = await repo.findOne({ where: { id }, withDeleted: true });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.update(id, data);
  return { message: 'Cập nhật thành công' };
};

export const remove = async (id: number) => {
  const repo = database.getRepository(GuardPost);
  const existing = await repo.findOne({ where: { id } });

  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.delete(id);
  return { message: 'Xóa thành công' };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(GuardPost);
  const guardPost = await repo.findOne({ where: { id } });

  if (!guardPost) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  return guardPost;
};

export const search = async (query: ISearchQuery<ISearchGuardPostQuery>) => {
  const repo = database.getRepository(GuardPost);
  const qb = repo.createQueryBuilder('guardPost');

  if (query.name) {
    qb.andWhere('guardPost.name LIKE :name', { name: `%${query.name}%` });
  }

  if (query.codeOrg) {
    qb.andWhere('guardPost.codeOrg = :codeOrg', { codeOrg: query.codeOrg });
  }

  if (query.location) {
    qb.andWhere('guardPost.location LIKE :location', {
      location: `%${query.location}%`,
    });
  }

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit);
  qb.orderBy('guardPost.orderNum', 'ASC');

  const [data, total] = await qb.getManyAndCount();
  const links = createLinks(
    '/guard-post/search?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};

export const getGuardPosts = async (user: User) => {
  const repo = database.getRepository(GuardPost);
  const guardPosts = await repo.find({
    where: { codeOrg: user.manageOrgCode },
  });
  return guardPosts;
};
