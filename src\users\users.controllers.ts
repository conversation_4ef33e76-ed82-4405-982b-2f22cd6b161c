import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ISearchQuery } from '../types/req';
import { User } from '../models';
import { ERROR } from '../utils/error';
import { IGetUsersQuery } from './users';
import * as userService from './users.service';
import { RolePerms } from '../constants';
import tbnvPermissions from './fakePermissions/tbnv.json';
import thttPermissions from './fakePermissions/thtt.json';
import tpPermissions from './fakePermissions/tp.json';
import ppPermissions from './fakePermissions/pp.json';
import uqTpPpPermissions from './fakePermissions/uq_tp_pp.json';
import tbPermissions from './fakePermissions/tb.json';
import tlhcPermissions from './fakePermissions/tlhc.json';
import tlthPermissions from './fakePermissions/tlth.json';
import cbnvPermissions from './fakePermissions/cbnv.json';

export const postUser = async (
  req: Request & { body: User },
  res: Response,
  next: NextFunction,
) => {
  try {
    const body: User = req.body;
    const user = await userService.insertUser(body, req.user, req.ipAddress);
    if (user.error) {
      return res.status(status.BAD_REQUEST).json(user.error);
    }
    return res.json(user.newObj);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const putUser = async (
  req: Request & { body: Partial<User> } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const body: User = req.body;
    const id = Number(req.params.id);
    const isAdmin = req?.permsArr?.includes(RolePerms.editUser) || false;
    if (!isAdmin && req?.user?.id !== id) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: 'Bạn không có quyền sửa thông tin của người khác',
        statusCode: 400,
      });
    }

    const user = await userService.updateUser(
      id,
      body,
      req.user,
      req.ipAddress,
    );

    if (user.error) {
      return res.status(status.BAD_REQUEST).json(user.error);
    }
    return res.json(user.updateObj);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'User không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const getById = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = Number(req.params.id);
    const isAdmin = req?.permsArr?.includes(RolePerms.viewUser) || false;
    if (!isAdmin && req?.user?.id !== id) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: 'Bạn không có quyền xem thông tin của người khác',
        statusCode: 400,
      });
    }
    const user = await userService.findById(id);

    return res.json(user);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'User không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const putChangePassUser = async (
  req: Request & {
    body: { password: string; newPassword: string };
    user?: User;
  } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = Number(req.params.id);
    if (req?.user?.id !== id) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: 'Bạn không có quyền thay đổi mật khẩu của người khác',
        statusCode: 400,
      });
    }
    const user = await userService.updatePassUser(
      id,
      req.body.password,
      req.body.newPassword,
    );

    if (user.error) {
      return res.status(status.BAD_REQUEST).json(user.error);
    }
    return res.json(user.user);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'User không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const deleteUser = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = Number(req.params.id);
    const user = await userService.updateStatusUser([id]);

    return res.json(user);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'User không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const search = async (
  req: Request & { query: ISearchQuery<IGetUsersQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const components = await userService.findAll(req.query);
    return res.json(components);
  } catch (e) {
    next(e);
  }
};
const getIpAddress = (req: Request) => {
  const ipWithCharacter =
    (req.headers['x-forwarded-for'] as string) ||
    req.socket.remoteAddress ||
    '';
  const ipWithoutCharacter = ipWithCharacter.split(',')[0];
  const ip = ipWithoutCharacter.split(':');
  return ip[ip.length - 1];
};
export const login = async (
  req: Request & { body: { username: string; password: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const ipAddress = getIpAddress(req);
    const data = await userService.login(req.body, ipAddress);
    res.json(data);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'User không tồn tại',
        statusCode: 400,
        errors: { field: 'username', message: 'User chưa đăng ký' },
      });
    }

    if (e instanceof Error && e.message === ERROR.AUTHENTICATION_ERROR) {
      return res.status(status.UNAUTHORIZED).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Mật khẩu chưa chính xác',
        statusCode: 400,
        errors: { field: 'password', message: 'Mật khẩu chưa chính xác' },
      });
    }

    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const loginByKeycloak = async (
  req: Request & { body: { code: string; redirectUri: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const ipAddress = getIpAddress(req);
    const data = await userService.keycloakLoginCheck(req.body, ipAddress);
    res.json(data);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'User không tồn tại',
        statusCode: 400,
        errors: { field: 'username', message: 'User chưa đăng ký' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getMyself = async (req: Request, res: Response) => {
  const role = req.user?.role;
  let permissions = [];
  switch (role?.name) {
    case 'TBNV':
      permissions = tbnvPermissions as any[];
      break;
    case 'THTT':
      permissions = thttPermissions as any[];
      break;
    case 'TP':
      permissions = tpPermissions as any[];
      break;
    case 'PP':
      permissions = ppPermissions as any[];
      break;
    case 'UQ_TP_PP':
      permissions = uqTpPpPermissions as any[];
      break;
    case 'TB':
      permissions = tbPermissions as any[];
      break;
    case 'TLHC':
      permissions = tlhcPermissions as any[];
      break;
    case 'TLTH':
      permissions = tlthPermissions as any[];
      break;
    case 'CBNV':
      permissions = cbnvPermissions as any[];
      break;
  }
  return res.json({ user: req.user, permissions });
};

export const updateStatusUsers = async (
  req: Request & { body: number[] },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = await userService.updateStatusUser(req.body);
    return res.json(user);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const logout = async (
  req: Request & { body: { keycloakRFToken: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    await userService.logout(req.body.keycloakRFToken);
    return res.status(status.OK).json({ message: 'Logout successful' });
  } catch (e) {
    console.log('🚀 => logout => e:', e);
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
