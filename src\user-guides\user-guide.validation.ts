import Joi from 'joi';
import { IApiError } from 'src/types/validation';

export const jBodyPostUserGuide = Joi.object({
  name: Joi.string().required(),
  slug: Joi.string().required(),
  order: Joi.number().required(),
  parentSlug: Joi.string().allow(null, ''),
});

export const jSlugInParams = Joi.object({
  slug: Joi.string().required(),
});

export const eSlugInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng slug (Path) chưa chính xác',
  errors: { slug: 'slug Định dạng chọn chính xác' },
  statusCode: 400,
};

export const jBodyPutUserGuide = Joi.object({
  content: Joi.string().allow(null, null),
  slug: Joi.string().min(1),
  parentSlug: Joi.string().allow(null, null),
  order: Joi.number().min(1),
  name: Joi.string().min(1),
});
