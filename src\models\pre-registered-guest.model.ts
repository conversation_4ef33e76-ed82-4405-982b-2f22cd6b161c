import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Type } from './type.model';
import { Organization } from './organization.model';
import { Guest } from './guest.model';
import { eQN } from './eQN.model';

@Entity('pre-registered-guest')
export class PreRegisteredGuest extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  // Thông tin người tiếp khách
  @Column({
    name: 'meet_who_id',
    type: 'varchar',
    nullable: true,
    default: null,
    length: 36,
  })
  meetWhoId?: string;

  @ManyToOne(() => eQN)
  @JoinColumn({ name: 'meet_who_id' })
  meetWho?: eQN;

  // Thông tin đơn vị tiếp khách
  @Column({
    name: 'meet_org_code',
    type: 'nvarchar',
    nullable: true,
    default: null,
    length: 17,
  })
  meetOrgCode?: string;

  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'meet_org_code', referencedColumnName: 'code' })
  meetOrganization?: Organization;

  // guest data
  @Column({
    name: 'guest_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  guestId?: number;
  @ManyToOne(() => Guest)
  @JoinColumn({ name: 'guest_id' })
  guest?: Guest;

  @Column({
    name: 'guest_type_id',
    type: 'int',
  })
  guestTypeId?: number; // Loại khách
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'guest_type_id' })
  guestType?: Type; // Loại khách

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    nullable: true,
    default: null,
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization; // Thông tin đơn vị

  @Column({
    name: 'guest_code_org',
    type: 'nvarchar',
    nullable: true,
    default: null,
    length: 17,
  })
  guestCodeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'guest_code_org', referencedColumnName: 'code' })
  guestOrganization?: Organization; // Thông tin đơn vị của khách

  @Column({
    name: 'guest_organization_name',
    type: 'nvarchar',
    length: 250,
    default: null,
    nullable: true,
  })
  guestOrganizationName?: string; // Tên đơn vị của khách

  @Column({
    name: 'check_in_time',
    type: 'datetime2',
  })
  checkInTime?: Date;

  @Column({
    name: 'estimate_out_time',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  estimatedOutTime?: Date;

  @Column({
    name: 'status',
    type: 'bit',
    default: 0,
  })
  status?: boolean;

  @Column({
    name: 'purpose_category_id',
    type: 'int',
  })
  purposeCategoryId?: number;
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'purpose_category_id' })
  purposeCategory?: Type;

  @Column({
    name: 'purpose_of_visit',
    type: 'nvarchar',
    length: 1000,
    nullable: true,
    default: null,
  })
  purposeOfVisit?: string;

  @Column({
    name: 'group_name',
    type: 'nvarchar',
    length: 250,
    nullable: true,
    default: null,
  })
  groupName?: string;

  @Column({
    name: 'leader_name',
    type: 'nvarchar',
    length: 250,
    nullable: true,
    default: null,
  })
  leaderName?: string;

  @Column({
    name: 'leader_position',
    type: 'nvarchar',
    length: 250,
    nullable: true,
    default: null,
  })
  leaderPosition?: string;

  @Column({
    name: 'number_of_visitors',
    type: 'int',
    default: 1,
  })
  numberOfVisitors?: number;

  @Column({
    name: 'number_of_vehicles',
    type: 'int',
    default: 0,
  })
  numberOfVehicles?: number;

  @Column({
    name: 'parent_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  parentId?: number;

  @ManyToOne(() => PreRegisteredGuest)
  @JoinColumn({ name: 'parent_id' })
  parent?: PreRegisteredGuest;

  @OneToMany(
    () => PreRegisteredGuest,
    (preRegisteredGuest) => preRegisteredGuest.parent,
  )
  children?: PreRegisteredGuest[];

  @Column({
    name: 'guest_card_id',
    type: 'nvarchar',
    length: 50,
  })
  guestCardId?: string;

  @Column({
    name: 'return_time',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  returnTime?: Date;

  @Column({
    name: 'duration_inside',
    type: 'int',
    nullable: true,
    default: null,
  })
  durationInside?: number;

  @Column({
    name: 'notes',
    type: 'nvarchar',
    length: 1000,
    nullable: true,
    default: null,
  })
  notes?: string;

  @Column({
    name: 'is_leader',
    type: 'bit',
    default: false,
  })
  isLeader?: boolean;

  @BeforeInsert()
  beforeCreate() {
    const cols = [
      this.groupName,
      this.leaderName,
      this.leaderPosition,
      this.notes,
    ];
    this.fullText = cols.join(' ')?.trim();
  }
}
