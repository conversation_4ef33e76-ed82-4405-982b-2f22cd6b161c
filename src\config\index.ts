import path from 'path';
import database from './database';
import { config as dotenvConfig } from 'dotenv';

dotenvConfig();

const config = {
  port: process.env.PORT || 3003,
  log_level: process.env.LOG_LEVEL || 'error',
  is_debug: process.env.IS_DEBUG === 'true',
  jwtsecret: process.env.JWT_SECRET || '',
  jwtSecretFile: process.env.JWT_SECRET_FILE || '',
  fileTokenTime: process.env.TOKEN_TIME || '3600',
  token_time: process.env.TOKEN_TIME || '86400',
  site_uri: process.env.SITE_URI || 'localhost:3000',
  dirTempBc: path.resolve(__dirname, '../../static/templates'),
  dirSaveBC: path.resolve(__dirname, '../../static/downloads'),
  dirSavePreviewBC: path.resolve(__dirname, '../../static/preview'),
  publicDownloadPath: 'static/downloads',
  publicPreviewPath: 'static/preview',
  redisHost: process.env.REDIS_HOST || 'localhost',
  redisPort: process.env.REDIS_PORT || 6379,
  redisPassword: process.env.REDIS_PW || '',
  supperAdminRoleName: process.env.SUPER_ADMIN_ROLE_NAME || 'SUPERADMIN',
  webhookKey: process.env.WEBHOOK_KEY || '914e34b1e3dd57defad53bcfd004c281',
  idReaderHost: process.env.ID_READER_HOST,
  cron: {
    syncAttendance: {
      schedule: process.env.CRON_SYNC_ATTENDANCE_SCHEDULE || '0 8 * * 1-6', // Run at 8 AM from Monday to Saturday
    },
  },
  eQN: { apiUrl: process.env.EQN_API_URL, apiKey: process.env.EQN_API_KEY },
  rootCode: process.env.ROOT_CODE || '***********.00.00',
};
const dirUploadFile = {
  serverUpload: path.resolve(__dirname, '../../uploads'),
  dirUpload: 'uploads',
};

export default config;
export { database, dirUploadFile };
