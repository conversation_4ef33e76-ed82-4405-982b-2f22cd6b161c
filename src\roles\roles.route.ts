import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  deletePermission2Role,
  getById,
  postRole,
  putPermission2Role,
  putRole,
  putStatusRole,
  putUsers2Role,
  removeUsers2Role,
  search,
  updateStatusRoles,
} from './roles.controller';
import {
  eBodyPostRole,
  eIdInParams,
  eMessage,
  eRolePermissionsBody,
  jBodyPostRole,
  jBodyPutRole,
  jIdInParams,
  jIdInUpdateStatus,
  jQueryRole,
  jRolePermissionsBody,
} from './roles.validation';

const routerRole = express.Router();
routerRole.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editRole]),
  createValidator(
    eBodyPostRole.part ? eBodyPostRole.part : 'body',
    jBodyPostRole,
    eBodyPostRole,
  ),
  postRole,
);

routerRole.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editRole]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eBodyPostRole.part ? eBodyPostRole.part : 'body',
    jBodyPutRole,
    eBodyPostRole,
  ),
  putRole,
);

routerRole.put(
  '/:id/status',
  checkAuth,
  // hasPerm([RolePerms.editRole]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  putStatusRole,
);

routerRole.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewRole]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  getById,
);

routerRole.get(
  '/',
  checkAuth,
  createValidator(
    eMessage.part ? eMessage.part : 'query',
    jQueryRole,
    eMessage,
  ),
  search,
);

routerRole.delete(
  '/:id/permissions',
  checkAuth,
  // hasPerm([RolePerms.editRole]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eRolePermissionsBody.part ? eRolePermissionsBody.part : 'body',
    jRolePermissionsBody,
    eRolePermissionsBody,
  ),
  deletePermission2Role,
);

routerRole.put(
  '/:id/permissions',
  checkAuth,
  // hasPerm([RolePerms.editRole]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eRolePermissionsBody.part ? eRolePermissionsBody.part : 'body',
    jRolePermissionsBody,
    eRolePermissionsBody,
  ),
  putPermission2Role,
);

routerRole.put(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editRole]),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jIdInUpdateStatus,
    eMessage,
  ),
  updateStatusRoles,
);
routerRole.put(
  '/:id/users',
  checkAuth,
  // hasPerm([RolePerms.editRole]),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jIdInUpdateStatus,
    eMessage,
  ),
  putUsers2Role,
);

routerRole.delete(
  '/:id/users',
  checkAuth,
  // hasPerm([RolePerms.editRole]),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jIdInUpdateStatus,
    eMessage,
  ),
  removeUsers2Role,
);

export default routerRole;
