import { insertLog } from '../logs/logs.service';
import { database } from '../config';
import {
  DMLoaiHinhTruc,
  DonviLoaiHinhTruc,
  Organization,
  User,
} from '../models';
import { ERROR } from '../utils/error';
import { LOG } from '../constants';

export const create = async (
  idLoaiTruc: string,
  data: DonviLoaiHinhTruc,
  user: User,
) => {
  const repo = database.getRepository(DonviLoaiHinhTruc);
  const loaiHinhTrucRepo = database.getRepository(DMLoaiHinhTruc);
  const orgRepo = database.getRepository(Organization);

  const loaiHinhTruc = await loaiHinhTrucRepo.findOne({
    where: { id: idLoaiTruc },
    withDeleted: true,
  });
  if (!loaiHinhTruc) {
    throw new Error(ERROR.LOAI_HINH_TRUC_NOT_FOUND);
  }

  const donVi = await orgRepo.findOne({
    where: { code: data.maDonVi },
    withDeleted: true,
  });
  if (!donVi) {
    throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
  }
  const checkExist = await repo.findOne({
    where: { maDonVi: data.maDonVi, maLoaiHinhTruc: loaiHinhTruc.ma },
    withDeleted: true,
  });
  if (checkExist) {
    throw new Error(ERROR.DATA_EXISTED);
  }

  const newObj = repo.create({ ...data, maLoaiHinhTruc: loaiHinhTruc.ma });
  const saved = await repo.save(newObj);
  await insertLog({
    ...(user && {
      content: `Tạo mới đơn vị trong loại hình lịch trực: ${saved.organization?.shortName} - ${saved.dMLoaiHinhTruc?.ma}`,
    }),
    ip: '127.0.0.1',
    ...(user && { userId: user.id }),
    typeId: LOG.CREATE,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  return { data: saved };
};
