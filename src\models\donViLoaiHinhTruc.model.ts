import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>ToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { DMLoaiHinhTruc } from './dmLoaiHinhTruc.model';
import { Organization } from './organization.model';

@Entity('donviLoaiHinhTruc')
export class DonviLoaiHinhTruc extends EntityModel {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id?: number;

  @Column({
    name: 'maLoaiHinhTruc',
    type: 'varchar',
    length: 36,
  })
  maLoaiHinhTruc?: string;
  @ManyToOne(() => DMLoaiHinhTruc)
  @JoinColumn({ name: 'maLoaiHinhTruc', referencedColumnName: 'ma' })
  dMLoaiHinhTruc?: DMLoaiHinhTruc;

  @Column({
    name: 'maDonVi',
    type: 'varchar',
    length: 17,
  })
  maDonVi?: string;
  @ManyToOne(() => Organization)
  @JoinColumn({ name: 'maDonVi', referencedColumnName: 'code' })
  organization?: Organization;
}
