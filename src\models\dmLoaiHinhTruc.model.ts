import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  <PERSON><PERSON>ty,
  OneToMany,
  PrimaryGeneratedColumn,
  Tree,
  TreeChildren,
  TreeParent,
} from 'typeorm';
import EntityModel from './entity.model';
import { ChucVuLoaiHinhTruc } from './chucVuLoaiHinhTruc.model';
import { DonviLoaiHinhTruc } from './donViLoaiHinhTruc.model';
import { LoaiHinhTrucChiTiet } from './loaiHinhTrucChiTiet.model';

@Entity()
@Tree('materialized-path')
export class DMLoaiHinhTruc extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column({
    type: 'varchar',
    length: 36,
    unique: true,
  })
  ma?: string;

  @Column()
  tenDayDu?: string;

  @Column({
    type: 'int',
    default: 0,
  })
  soLuong?: number;

  @Column({
    type: 'int',
    default: 0,
  })
  stt?: number;

  @TreeChildren()
  children?: DMLoaiHinhTruc[];

  @TreeParent()
  parent?: DMLoaiHinhTruc;

  @Column({ nullable: true })
  public parentId?: string;

  mpath?: string;

  @BeforeInsert()
  @BeforeUpdate()
  updateFullText() {
    this.fullText = [this.ma, this.tenDayDu].filter(Boolean).join(' ').trim();
  }

  @OneToMany(
    () => ChucVuLoaiHinhTruc,
    (chucVuLoaiHinhTruc) => chucVuLoaiHinhTruc.dMLoaiHinhTruc,
  )
  public chucVuLoaiHinhTrucs!: ChucVuLoaiHinhTruc[];

  @OneToMany(
    () => DonviLoaiHinhTruc,
    (donviLoaiHinhTruc) => donviLoaiHinhTruc.dMLoaiHinhTruc,
  )
  public donviLoaiHinhTrucs!: DonviLoaiHinhTruc[];

  @OneToMany(
    () => LoaiHinhTrucChiTiet,
    (loaiHinhTrucChiTiet) => loaiHinhTrucChiTiet.dMLoaiHinhTruc,
  )
  public loaiHinhTrucChiTiets!: LoaiHinhTrucChiTiet[];
}
