export type IPostReportError = {
  content: string;
  type: string;
  time: Date;
  fileIds?: number[];
};

export type IGetReportErrorsQuery = {
  search?: string;
  type?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  page?: number;
  limit?: number;
  order?: ASC | DESC;
  sortBy?: string;
};

export type IPutReportError = {
  content?: string;
  type?: string;
  time?: string;
  status?: string;
  fileIds?: number[];
};

export type IAddUpdateHandleError = {
  dueDateStart: string;
  dueDateEnd: string;
  content: string;
  status: string;
};
