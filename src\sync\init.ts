import { syncAllDonVi } from './handlers/don-vi-sync';
import { syncAlleQN } from './handlers/eqn-sync';
import { syncAllType } from './handlers/type-sync';
import { syncAllRank } from './handlers/rank-sync';
import { syncAllPosition } from './handlers/position-sync';

export const initSync = async () => {
  try {
    await syncAllType();
    await syncAllRank();
    await syncAllPosition();
    await syncAllDonVi();
    await syncAlleQN();

    console.log(`===== BẮT ĐẦU XỬ LÝ TẠO LẠI MPATH TẤT CẢ BẢNG =====`);
    // await processAllTables(true);
  } catch (error) {
    console.log('Error initializing sync:', error);
  }
};
