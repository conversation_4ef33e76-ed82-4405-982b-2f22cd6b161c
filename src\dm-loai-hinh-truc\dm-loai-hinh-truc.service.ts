import { database } from '../config';
import { DMLoaiHinhTruc, User } from '../models';
import { ERROR } from '../utils/error';

export const create = async (
  data: DMLoaiHinhTruc,
  user: User, // eslint-disable-line @typescript-eslint/no-unused-vars
) => {
  const repo = database.getRepository(DMLoaiHinhTruc);

  const checkExist = await repo.findOne({
    where: { ma: data.ma },
    withDeleted: true,
  });
  if (checkExist) {
    throw new Error(ERROR.DATA_EXISTED);
  }
  if (data.parentId) {
    const parentLoaiHinhTruc = await repo.findOne({
      where: { id: data.parentId },
      withDeleted: true,
    });
    if (!parentLoaiHinhTruc) {
      throw new Error(ERROR.DATA_NOT_FOUND);
    } else {
      data.parent = parentLoaiHinhTruc;
    }
  }

  const newObj = repo.create({ ...data });
  const saved = await repo.save(newObj);

  return { data: saved };
};
