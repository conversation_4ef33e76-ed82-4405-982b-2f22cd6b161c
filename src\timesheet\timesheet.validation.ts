import Joi from 'joi';
import { IApiError } from '../types/validation';

export const jTimesheetQuery = Joi.object({
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
  orgCode: Joi.string().optional(),
  eQNId: Joi.string().optional(),
});

export const eTimesheetQuery: IApiError = {
  message: 'Invalid query parameters',
  statusCode: 400,
  code: 'INVALID_QUERY',
  part: 'query',
  errors: {
    startDate: 'Start date must be a valid ISO date',
    endDate: 'End date must be a valid ISO date',
    orgCode: 'Organization code must be a string',
    eQNId: 'EQN ID must be a string',
  },
};

export const jCreateAttendanceLog = Joi.object({
  name: Joi.string().optional(),
  eQNId: Joi.string().required(),
  dateUTC: Joi.string().isoDate().required(),
  checkOutTime: Joi.string().isoDate().optional(),
  status: Joi.string().valid('granted', 'denied').required(),
  deviceId: Joi.string().optional(),
  deviceName: Joi.string().optional(),
  type: Joi.number().optional(),
  biometricData: Joi.string().optional(),
  attendanceImage: Joi.string().optional(),
});

export const eCreateAttendanceLog: IApiError = {
  message: 'Invalid attendance log data',
  statusCode: 400,
  code: 'INVALID_DATA',
  part: 'body',
  errors: {
    eQNId: 'EQN ID is required',
    dateUTC: 'Check-in time must be a valid ISO date',
    status: 'Status must be either granted or denied',
    deviceId: 'Device ID must be a string',
    deviceName: 'Device name must be a string',
    type: 'Type must be a number',
    biometricData: 'Biometric data must be a string',
  },
};

export const jTimesheetDetailQuery = Joi.object({
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
  eQNId: Joi.string().required(),
});

export const eTimesheetDetailQuery: IApiError = {
  message: 'Invalid query parameters',
  statusCode: 400,
  code: 'INVALID_QUERY',
  part: 'query',
  errors: {
    startDate: 'Start date must be a valid ISO date',
    endDate: 'End date must be a valid ISO date',
    eQNId: 'EQN ID must be a string',
  },
};

export const jCreateAttendanceReason = Joi.object({
  eQNId: Joi.string().required(),
  reason: Joi.string().optional(),
  date: Joi.string().isoDate().required(),
});

export const eCreateAttendanceReason: IApiError = {
  message: 'Invalid attendance log data',
  statusCode: 400,
  code: 'INVALID_DATA',
  part: 'body',
  errors: {
    eQNId: 'EQN ID is required',
    reason: 'Reason must be a string',
    date: 'Date must be a valid ISO date',
  },
};

export const jTimesheetListQuery = Joi.object({
  startDate: Joi.string().isoDate().optional(),
  endDate: Joi.string().isoDate().optional(),
  orgCode: Joi.string().optional(),
  status: Joi.string().optional(),
  location: Joi.string().optional(),
  checkTypes: Joi.string().optional(),
  limit: Joi.number().optional(),
  page: Joi.number().optional(),
  keyword: Joi.string().allow('').optional(),
});

export const eTimesheetListQuery: IApiError = {
  message: 'Invalid query parameters',
  statusCode: 400,
  code: 'INVALID_QUERY',
  part: 'query',
  errors: {
    startDate: 'Start date must be a valid ISO date',
    endDate: 'End date must be a valid ISO date',
    orgCode: 'Organization code must be a string',
    limit: 'Limit must be a number',
    page: 'Page must be a number',
  },
};
