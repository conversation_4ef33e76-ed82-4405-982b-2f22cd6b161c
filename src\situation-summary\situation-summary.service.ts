import { ISearchQuery } from 'src/types/req';
import { database } from '../config';
import { SituationSummary, User } from '../models';
import { ERROR } from '../utils/error';
import { ISearchSituationSummaryQuery } from './situation-summary';
import { createLinks } from '../utils/pagination';
import { exportDoc } from '../utils/exportFile';
import dayjs from 'dayjs';
// import { createDocx, createDutyBook } from '../utils/docx';

export const create = async (data: SituationSummary) => {
  const repo = database.getRepository(SituationSummary);
  const newSummary = repo.create(data);
  const saved = await repo.save(newSummary);

  return { data: saved };
};

export const update = async (id: number, data: Partial<SituationSummary>) => {
  const repo = database.getRepository(SituationSummary);

  const existing = await repo.findOne({ where: { id }, withDeleted: true });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  const dataUpdate = {
    ...data,
    date: data.date ? new Date(data.date) : existing.date,
  };

  await repo.update(id, dataUpdate);
  return { message: 'Cập nhật thành công' };
};

export const remove = async (id: number) => {
  const repo = database.getRepository(SituationSummary);
  const existing = await repo.findOne({ where: { id }, withDeleted: true });

  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.delete(id);
  return { message: 'Xóa thành công' };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(SituationSummary);
  const summary = await repo.findOne({ where: { id }, withDeleted: true });

  if (!summary) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  return summary;
};

export const search = async (
  query: ISearchQuery<ISearchSituationSummaryQuery>,
  user: User,
) => {
  const repo = database.getRepository(SituationSummary);
  const qb = repo.createQueryBuilder('summary').withDeleted();

  qb.leftJoin('summary.detailDutyNow', 'detailDutyNow')
    .addSelect(['detailDutyNow.id', 'detailDutyNow.date'])
    .leftJoin('detailDutyNow.main', 'main')
    .addSelect(['main.id', 'main.fullName', 'main.shortName'])
    .leftJoin('detailDutyNow.assistant', 'assistant')
    .addSelect(['assistant.id', 'assistant.fullName', 'assistant.shortName']);

  qb.where('summary.codeOrg = :codeOrg', {
    codeOrg: user.manageOrgCode,
  });

  if (query.codeOrg) {
    qb.andWhere('summary.codeOrg = :codeOrg', { codeOrg: query.codeOrg });
  }

  if (query.id) {
    qb.andWhere('summary.id = :id', { id: query.id });
  }
  if (query.search) {
    qb.andWhere('summary.fullText = :fullText', { fullText: query.search });
  }

  if (query.dutyDate) {
    qb.andWhere('detailDutyNow.date BETWEEN :startDate AND :endDate', {
      startDate: dayjs(query.dutyDate[0]).startOf('day').toISOString(),
      endDate: dayjs(query.dutyDate[1]).endOf('day')?.toISOString(),
    });
  }

  if (query.fromDate) {
    qb.andWhere('summary.date >= :fromDate', { fromDate: query.fromDate });
  }

  if (query.toDate) {
    qb.andWhere('summary.date <= :toDate', { toDate: query.toDate });
  }

  if (query.nameCommand) {
    qb.andWhere('summary.nameCommand LIKE :nameCommand', {
      nameCommand: `%${query.nameCommand}%`,
    });
  }

  if (query.nameDutyNow) {
    qb.andWhere('summary.nameDutyNow LIKE :nameDutyNow', {
      nameDutyNow: `%${query.nameDutyNow}%`,
    });
  }

  if (query.nameDutyFeature) {
    qb.andWhere('summary.nameDutyFeature LIKE :nameDutyFeature', {
      nameDutyFeature: `%${query.nameDutyFeature}%`,
    });
  }

  if (query.nameChiefDuty) {
    qb.andWhere('main.fullName LIKE :nameChiefDuty', {
      nameChiefDuty: `%${query.nameChiefDuty}%`,
    });
  }

  if (query.nameDeputyDuty) {
    qb.andWhere('assistant.fullName LIKE :nameDeputyDuty', {
      nameDeputyDuty: `%${query.nameDeputyDuty}%`,
    });
  }

  if (query.date) {
    qb.andWhere('CONVERT(DATE, summary.date) = :date', { date: query.date });
  }

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit).orderBy('summary.date', 'DESC');

  const [data, total] = await qb.getManyAndCount();

  const links = createLinks(
    '/situation-summary/search?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};

// export const exportDocx = async (id: number) => {
//   const summary = await findById(id);
//   //   return createDocx(summary);
// };

// export const exportBook = async (query: any) => {
//   // const { data } = await search(query);
//   //   return createDutyBook(data);
// };
export const exportDocxById = async (id: number) => {
  const repo = database.getRepository(SituationSummary);
  const summary = await repo.findOne({ where: { id }, withDeleted: true });

  if (!summary) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  const equipmentTakeOutTemp: string = summary.equipmentTakeOut
    ? JSON.parse(summary.equipmentTakeOut)
        .map(
          (item: { name: string; quantity: string }) =>
            `${item.name} - ${item.quantity}`,
        )
        .join(', ')
    : '';

  const absentPerTemp: string = summary.absentPer
    ? JSON.parse(summary.absentPer)
        .map(
          (item: { name: string; reasonLeave: string }) =>
            `${item.name} - ${item.reasonLeave}`,
        )
        .join(', ')
    : '';

  const data = {
    ngayThang: `Ngày ${dayjs(summary.date).format('DD')} tháng ${dayjs(summary.date).format('MM')} năm ${dayjs(summary.date).format('YYYY')}`,
    tongQuanSo: summary.totalPer ?? '',
    coMat: summary.presentPer ?? '',
    vangMat: absentPerTemp ?? '',
    vuKhiLon: equipmentTakeOutTemp ?? '',
    hoatDongChinh: summary.taskMain ?? '',
    tinhHinhThucHien: summary.taskContain ?? '',
    viecDotXuat: summary.taskSudden ?? '',
    uuKhuyetDiem: summary.prosAndCons ?? '',
    tiepTucGiaiQuyet: summary.taskContinue ?? '',
    soSach: summary.bookHandover ?? '',
    trucChiHuy: summary.nameCommand ?? '',
    trucBanNhanPhien: summary.nameDutyFeature ?? '',
    trucBanGiaoPhien: summary.nameDutyNow ?? '',
  };
  const pathOutput = await exportDoc(
    `trich_xuat_khach_tinh_hinh_ngay.docx`,
    `trich_xuat_khach_tinh_hinh_ngay_${Date.now()}.docx`,
    data,
  );
  return pathOutput;
};
