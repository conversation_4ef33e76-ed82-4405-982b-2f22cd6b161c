import { insertLog } from '../logs/logs.service';
import { database } from '../config';
import {
  DMLoaiHinhTruc,
  ChucVuLoaiHinhTruc,
  User,
  PositionCategory,
} from '../models';
import { ERROR } from '../utils/error';
import { LOG } from '../constants';

export const create = async (
  idLoaiTruc: string,
  data: ChucVuLoaiHinhTruc,
  user: User,
) => {
  const repo = database.getRepository(ChucVuLoaiHinhTruc);
  const loaiHinhTrucRepo = database.getRepository(DMLoaiHinhTruc);
  const chucVuRepo = database.getRepository(PositionCategory);

  const loaiHinhTruc = await loaiHinhTrucRepo.findOne({
    where: { id: idLoaiTruc },
    withDeleted: true,
  });
  if (!loaiHinhTruc) {
    throw new Error(ERROR.LOAI_HINH_TRUC_NOT_FOUND);
  }

  const chuVu = await chucVuRepo.findOne({
    where: { code: data.maChucVu },
    withDeleted: true,
  });
  if (!chuVu) {
    throw new Error(ERROR.POSITION_NOT_FOUND);
  }

  const checkExist = await repo.findOne({
    where: { maChucVu: data.maChucVu, maLoaiHinhTruc: loaiHinhTruc.ma },
    withDeleted: true,
  });
  if (checkExist) {
    throw new Error(ERROR.DATA_EXISTED);
  }
  console.log('newObj:', { ...data, maLoaiHinhTruc: loaiHinhTruc.ma });
  const newObj = repo.create({ ...data, maLoaiHinhTruc: loaiHinhTruc.ma });
  const saved = await repo.save(newObj);
  await insertLog({
    ...(user && {
      content: `Tạo mới chức vụ trong loại hình lịch trực: ${saved.positionCategory?.code} - ${saved.dMLoaiHinhTruc?.ma}`,
    }),
    ip: '127.0.0.1',
    ...(user && { userId: user.id }),
    typeId: LOG.CREATE,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  return { data: saved };
};
