import Joi from 'joi';
import { IApiError } from '../types/validation';
export const eSearch: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: '<PERSON><PERSON> liệu không hợp lệ',
  statusCode: 400,
};

export const jIdInParams = Joi.object({
  id: Joi.string().required(),
});

export const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

export const eCreate: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

export const eUpdate: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: '<PERSON><PERSON> liệu không hợp lệ',
  statusCode: 400,
};
export const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export const jBodyLoaiHinhTruc = Joi.object({
  ma: Joi.string().max(250).required(),
  tenDayDu: Joi.string().max(100).required(),
  parentId: Joi.string().allow(null, ''),
  stt: Joi.number().required(),
  soLuong: Joi.number().required(),
});

export const jBodyDonViLoaiHinhTruc = Joi.object({
  maDonVi: Joi.string().max(250).required(),
});
export const jBodyChucVuLoaiHinhTruc = Joi.object({
  maChucVu: Joi.string().max(250).required(),
});
export const jBodyLoaiHinhTrucChiTiet = Joi.object({
  nhiemVu: Joi.string().required(),
  noiDung: Joi.string().allow(null, ''),
  stt: Joi.number().required(),
});
