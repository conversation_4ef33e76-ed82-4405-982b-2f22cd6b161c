import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  getDashboard,
  getListEQN,
  getOrgs,
  getPositions,
  getQNById,
  getRanks,
  seachEQNTreeOrg,
  dataFake,
} from './eqn.controller';
import {
  eDashboard,
  eListQuery,
  eSearchQuery,
  jDashboard,
  jListEQN,
  jSearchEQN,
} from './eqn.validation';

const routerEqn = express.Router();

routerEqn.get(
  '/tree/org',
  checkAuth,
  createValidator('query', jSearchEQN, eSearchQuery),
  seachEQNTreeOrg,
);

routerEqn.get(
  '/list',
  checkAuth,
  // hasPerm([RolePerms.viewEqn]),
  createValidator('query', jListEQN, eListQuery),
  getListEQN,
);

routerEqn.get(
  '/ranks',
  checkAuth,
  // hasPerm([RolePerms.viewEqn]),
  getRanks,
);

routerEqn.get(
  '/positions',
  checkAuth,
  // hasPerm([RolePerms.viewEqn]),
  getPositions,
);

routerEqn.get(
  '/orgs',
  checkAuth,
  // hasPerm([RolePerms.viewEqn]),
  getOrgs,
);

routerEqn.get(
  '/dashboard',
  checkAuth,
  createValidator('query', jDashboard, eDashboard),
  getDashboard,
);

routerEqn.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewEqn]),
  getQNById,
);

routerEqn.post('/data/fake', dataFake);

export default routerEqn;
