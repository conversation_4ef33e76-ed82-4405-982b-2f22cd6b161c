export const romanNumber = (number: number): string => {
  let num = number;
  const roman = [
    'I',
    'IV',
    'V',
    'IX',
    'X',
    'XL',
    'L',
    'XC',
    'C',
    'CD',
    'D',
    'CM',
    'M',
  ];
  const decimal = [1, 4, 5, 9, 10, 40, 50, 90, 100, 400, 500, 900, 1000];
  let result = '';
  for (let i = 12; i >= 0; i--) {
    while (num >= decimal[i]) {
      num -= decimal[i];
      result += roman[i];
    }
  }
  return result;
};
