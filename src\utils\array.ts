/* eslint-disable @typescript-eslint/no-explicit-any */

export const enumStrValues2Array = (inputEnum: any) => {
  const keys = Object.keys(inputEnum);
  const values = keys.map((key) => inputEnum[key]);
  return values;
};

export const enumIntValues2Array = (inputEnum: any) => {
  const keys = Object.keys(inputEnum);
  const values = keys.filter((key) => !isNaN(Number(key)));
  const res = values.map((value) => Number(value));
  return res;
};

export const enumStringValues2Array = (inputEnum: any) => {
  const values = Object.values(inputEnum);
  const res = values.map((value) => String(value));
  return res;
};

export const checkArrayQuery = (data: any) => {
  return Array.isArray(data) ? data : [data];
};
