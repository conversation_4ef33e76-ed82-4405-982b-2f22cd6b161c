import Joi from 'joi';
import { IApiError } from '../types/validation';

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jBodyShiftHandover = Joi.object({
  shiftNowId: Joi.number().optional(),
  shiftFeatureId: Joi.number().optional(),
  guardPostId: Joi.number().optional(),
  qneidSuperVisor: Joi.string().max(250).optional(),
  taskContent: Joi.string().max(250).optional(),
  taskContinue: Joi.string().max(250).optional(),
  bookHandover: Joi.string().max(250).optional(),
  vehicleCountStart: Joi.number().optional(),
  vehicleEntryCountDuring: Joi.number().optional(),
  vehicleExitCountDuring: Joi.number().optional(),
  vehicleCountHandover: Joi.number().optional(),
  equipmentHandover: Joi.string().max(250).optional(),
});

const eBodyShiftHandover: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const jSearchShiftHandover = Joi.object({
  orgCode: Joi.string().optional(),
  manageOrgCode: Joi.string().optional(),
  guardPostId: Joi.number().optional(),
  fromDate: Joi.date().optional(),
  toDate: Joi.date().optional(),
  shiftNowId: Joi.number().optional(),
  qneidSuperVisor: Joi.string().optional(),
  shiftCode: Joi.string().optional(),
  bookHandover: Joi.string().optional(),
  createdAt: Joi.array().optional(),
  fullText: Joi.string().optional(),
  guardShiftName: Joi.string().optional(),
  shiftNowName: Joi.string().optional(),
  shiftFeatureName: Joi.string().optional(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export {
  jIdInParams,
  eIdInParams,
  jBodyShiftHandover,
  eBodyShiftHandover,
  jSearchShiftHandover,
  eSearchQuery,
};
