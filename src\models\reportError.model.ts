import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import EntityModel from './entity.model';
import { File } from './file.model';
import { HandleError } from './handleError.model';
import { User } from './user.model';

@Entity('report_errors')
export class ReportError extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ name: 'user_id' })
  userId?: number;
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: User;

  @Column({ type: 'nvarchar', length: 2000, nullable: true })
  content?: string;

  @Column({ type: 'nvarchar', length: 250, nullable: true })
  type?: string;

  @Column({ type: 'datetime2', nullable: true })
  time?: Date;

  @Column({ type: 'nvarchar', length: 50 })
  status?: string; // new, reject , handling, resolve

  @OneToMany(() => File, (file) => file.reportError)
  files?: File[];

  @OneToOne(() => HandleError, (handleError) => handleError.reportError, {
    createForeignKeyConstraints: false,
  })
  handleError?: HandleError;
}
