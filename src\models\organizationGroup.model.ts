import {
  BeforeInse<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { User } from './user.model';

@Entity('organizationGroup')
export class OrganizationGroup extends EntityModel {
  @PrimaryColumn({ type: 'varchar', length: 36 })
  id?: string;

  @Column()
  name?: string;

  @Column({ default: null, nullable: true })
  shortName?: string;

  @Column({
    type: 'smallint',
    default: 0,
  })
  order?: number;

  @Column({
    type: 'simple-array',
    nullable: true,
    default: null,
  })
  Parameters?: string[];

  @Column({
    type: 'number',
    nullable: true,
    default: null,
  })
  createdById?: number;
  @ManyToOne(() => User)
  @JoinColumn({ name: 'createdById' })
  createdBy?: User;

  @Column({
    type: 'number',
    nullable: true,
    default: null,
  })
  updatedById?: number;
  @ManyToOne(() => User)
  @JoinC<PERSON>umn({ name: 'updatedById' })
  updatedBy?: User;

  @BeforeInsert()
  async beforeInsert() {
    this.updateFullText();
  }

  updateFullText() {
    const attributes = [this.name, this.shortName, this.Parameters]?.filter(
      (_) => _,
    );
    this.fullText = attributes?.join(' ');
  }
}
