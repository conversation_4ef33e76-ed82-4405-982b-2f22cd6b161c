import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { GuardShift } from './guardShift.model';
import { GuardPost } from './guardPost.model';
import { ShiftStaff } from './shiftStaff.model';

@Entity('shiftPosts')
export class ShiftPost extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'guard_shift_id',
    type: 'int',
    default: null,
  })
  guardShiftId?: number;
  @ManyToOne(() => GuardShift, (guardShift) => guardShift.shiftPosts)
  @JoinColumn({ name: 'guard_shift_id' })
  guardShift?: GuardShift;

  @Column({
    name: 'guard_post_id',
    type: 'int',
  })
  guardPostId?: number;
  @ManyToOne(() => GuardPost)
  @JoinColumn({ name: 'guard_post_id' })
  guardPost?: GuardPost;

  @OneToMany(() => ShiftStaff, (shiftStaff) => shiftStaff.shiftPost)
  shiftStaffs?: ShiftStaff[];
}
