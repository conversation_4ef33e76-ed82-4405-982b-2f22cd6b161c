import sharp from 'sharp';
import log from './log';

/**
 * Resize ảnh base64 xuống kích thước cụ thể
 * @param base64Image Chuỗi base64 của ảnh (có thể bao gồm hoặc không bao gồm phần data:image/xxx;base64,)
 * @param width Chiều rộng mong muốn (mặc định: 200)
 * @param height Chiều cao mong muốn (mặc định: 200)
 * @param options Các tùy chọn resize (mặc định: fit: 'cover', position: 'center')
 * @returns Chuỗi base64 của ảnh đã được resize
 */
export const resizeBase64Image = async (
  base64Image: string,
  width: number = 200,
  height: number = 200,
  options: sharp.ResizeOptions = {
    fit: 'cover',
    position: 'center',
  },
): Promise<string> => {
  try {
    // Kiểm tra xem chuỗi base64 có chứa phần header không
    const base64Data = base64Image.includes('base64,')
      ? base64Image.split('base64,')[1]
      : base64Image;

    // Lấy định dạng ảnh từ chuỗi base64 (nếu có)
    let imageFormat = 'jpeg'; // Mặc định là jpeg
    if (base64Image.includes('data:image/')) {
      const formatMatch = base64Image.match(/data:image\/([a-zA-Z]+);base64/);
      if (formatMatch && formatMatch[1]) {
        imageFormat = formatMatch[1].toLowerCase();
        // Đảm bảo định dạng được hỗ trợ bởi sharp
        if (
          !['jpeg', 'jpg', 'png', 'webp', 'gif', 'avif'].includes(imageFormat)
        ) {
          imageFormat = 'jpeg';
        }
      }
    }

    // Chuyển đổi base64 thành buffer
    const buffer = Buffer.from(base64Data, 'base64');

    // Resize ảnh
    const resizedImageBuffer = await sharp(buffer)
      .resize(width, height, options)
      .toFormat(imageFormat as keyof sharp.FormatEnum)
      .toBuffer();

    // Chuyển đổi buffer thành base64
    const resizedBase64 = resizedImageBuffer.toString('base64');

    // Trả về chuỗi base64 với header phù hợp
    return `data:image/${imageFormat};base64,${resizedBase64}`;
  } catch (error) {
    log.error('Error resizing base64 image:', error);
    // Trả về ảnh gốc nếu có lỗi
    return base64Image;
  }
};
