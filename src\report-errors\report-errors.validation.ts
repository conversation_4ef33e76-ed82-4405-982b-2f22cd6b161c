import Joi from 'joi';
import { IApiError } from 'src/types/validation';

export const jBodyPostReportError = Joi.object({
  content: Joi.string().required(),
  type: Joi.string().required(),
  time: Joi.date().required(),
  fileIds: Joi.array().items(Joi.number()),
});

export const jSlugInParams = Joi.object({
  slug: Joi.string().required(),
});

export const eSlugInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng slug (Path) chưa chính xác',
  errors: { slug: 'slug Định dạng chọn chính xác' },
  statusCode: 400,
};

export const jBodyPutReportError = Joi.object({
  content: Joi.string().required(),
  type: Joi.string().required(),
  time: Joi.date().required(),
  fileIds: Joi.array().items(Joi.number()),
});

export const eQueryReportErrors: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng chuỗi tìm kiếm chưa chính xác',
  statusCode: 400,
};

export const jQueryReportErrors = Joi.object({
  search: Joi.string().allow(''),
  type: Joi.string(),
  startDate: Joi.string(),
  endDate: Joi.string(),
  status: Joi.string(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

export const jBodyAddUpdateHandleError = Joi.object({
  dueDateStart: Joi.string().required(),
  dueDateEnd: Joi.string().required(),
  content: Joi.string().required(),
  status: Joi.string().required(),
});
