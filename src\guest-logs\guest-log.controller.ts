import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import * as guestLogService from './guest-log.service';
import { ISearchQuery } from '../types/req';
import {
  ICreateGuestLogBody,
  IGuestLogGroup,
  ISearchGuestLogQuery,
  IWebhookGuestLogBody,
  IGuestLogStatsByOrgCodeQuery,
  IGuestLogHistoryInOutQuery,
} from './guest-log';
import { ISearchPendingGuestLogQuery } from './guest-log';
import { User } from '../models';

export const webhookGuestLog = async (
  req: Request & { body: IWebhookGuestLogBody },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guestLogService.processWebhook(req.body);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.GUEST_LOG_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.GUEST_LOG_NOT_FOUND,
            message: 'Không tìm thấy thông tin nhật ký khách',
            statusCode: 400,
          });

        case ERROR.GUARD_POST_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.GUARD_POST_NOT_FOUND,
            message: 'Không tìm thấy thông trạm gác',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const searchPendingGuestLogs = async (
  req: Request & { query: ISearchQuery<ISearchPendingGuestLogQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.searchPendingGuests(
      req.query,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const deletePendingGuests = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.deletePendingGuests(
      req.body?.ids,
      user as User,
      req.ip,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.PENDING_GUEST_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.PENDING_GUEST_NOT_FOUND,
            message: 'Không tồn tại khách chờ',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const createGuestLog = async (
  req: Request & { body: ICreateGuestLogBody },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.createGuestLog(
      req.body,
      user as User,
      req.ip,
    );
    if (result.error) {
      return res.status(status.BAD_REQUEST).json(result.error);
    }
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const editGuestLog = async (
  req: Request & { body: ICreateGuestLogBody } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.editGuestLog(
      req?.params?.id,
      req.body,
      user as User,
      req.ip,
    );
    if (result.error) {
      return res.status(status.BAD_REQUEST).json(result.error);
    }
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const returnCard = async (
  req: Request & { body: ICreateGuestLogBody } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.returnCard(
      req?.params?.id,
      user as User,
      req.ip,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const searchGuestLogs = async (
  req: Request & { query: ISearchQuery<ISearchGuestLogQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.searchGuestLogs(
      req.query,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const deleteGuestLogs = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.deleteGuestLogs(
      req.body?.ids,
      user as User,
      req.ip,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.GUEST_LOG_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.GUEST_LOG_NOT_FOUND,
            message: 'Có 1 số dữ liệu bạn không thể xóa',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const guestLogDetailByIdVehicleLog = async (
  req: Request & { params: { id: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    // const user = req.user;
    const result = await guestLogService.getGuestLogByIdVehicleLog(
      req.params.id,
      // user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.GUEST_LOG_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.GUEST_LOG_NOT_FOUND,
            message: 'Không tìm thấy thông tin nhật ký khách',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const guestLogDetail = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.getGuestLog(
      req.params.id,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.GUEST_LOG_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.GUEST_LOG_NOT_FOUND,
            message: 'Không tìm thấy thông tin nhật ký khách',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const guestLogSummary = async (
  req: Request & { query: IGuestLogGroup },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.summary(req?.query, user as User);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const guestByGroupVehicle = async (
  req: Request & { query: IGuestLogGroup },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.groupVehicle(req.query, user as User);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const guestByCheckInTime = async (
  req: Request & { query: IGuestLogGroup },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.groupByCheckInTime(
      req.query,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const guestByOrg = async (
  req: Request & { query: IGuestLogGroup },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.groupByOrg(req.query, user as User);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const guestLogStatsByOrgCode = async (
  req: Request & { query: IGuestLogStatsByOrgCodeQuery },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.getGuestLogStatsByOrgCode(
      req.query,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const guestLogHistoryInOut = async (
  req: Request & { query: IGuestLogHistoryInOutQuery },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.getGuestLogHistoryInOut(
      req.query,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const docxGuestLogs = async (
  req: Request & { query: ISearchQuery<ISearchGuestLogQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user;
    const result = await guestLogService.exportDocxGuestLogs(
      req.query,
      user as User,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};
