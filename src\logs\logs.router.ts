import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import { searchLog } from './logs.controller';
import { eQuerySearch, jQuerySearchLog } from './logs.validation';
const routerLog = express.Router();

// router.post(
//   '',
//   createValidator(
//     eAddLogBody.part ? eAddLogBody.part : 'body',
//     jBodyLogs,
//     eAddLogBody,
//   ),
//   postLog,
// );

routerLog.get(
  '',
  checkAuth,
  // hasPerm([RolePerms.viewLogs]),
  createValidator(
    eQuerySearch.part ? eQuerySearch.part : 'query',
    jQuerySearchLog,
    eQuerySearch,
  ),
  searchLog,
);
export default routerLog;
