import { Router } from 'express';
import { checkAuth } from '../middlewares/auth.middleware';
import { createValidator } from '../utils/validator';
import {
  createShiftInspection,
  deleteShiftInspection,
  getShiftInspection,
  getShiftInspections,
  getUser,
  updateShiftInspection,
} from './shift-inspections.controller';
import {
  eCreateShiftInspection,
  eIdInParams,
  eSearchShiftInspection,
  eUpdateShiftInspection,
  jCreateShiftInspection,
  jIdInParams,
  jSearchShiftInspection,
  jUpdateShiftInspection,
} from './shift-inspections.validation';

export const shiftInspectionsRoute = Router();

shiftInspectionsRoute.get(
  '/',
  checkAuth,
  // hasPerm([RolePerms.viewShiftInspection, RolePerms.viewSituationSummary]),
  createValidator('query', jSearchShiftInspection, eSearchShiftInspection),
  getShiftInspections,
);

shiftInspectionsRoute.get(
  '/user',
  checkAuth,
  // hasPerm([RolePerms.viewShiftInspection]),
  getUser,
);

shiftInspectionsRoute.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewShiftInspection]),
  createValidator('params', jIdInParams, eIdInParams),
  getShiftInspection,
);

shiftInspectionsRoute.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editShiftInspection]),
  createValidator('body', jCreateShiftInspection, eCreateShiftInspection),
  createShiftInspection,
);

shiftInspectionsRoute.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editShiftInspection]),
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jUpdateShiftInspection, eUpdateShiftInspection),
  updateShiftInspection,
);

shiftInspectionsRoute.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editShiftInspection]),
  createValidator('params', jIdInParams, eIdInParams),
  deleteShiftInspection,
);
