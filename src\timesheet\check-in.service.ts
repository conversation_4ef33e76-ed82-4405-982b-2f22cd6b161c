import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isBetween from 'dayjs/plugin/isBetween';
import { WorkingSchedule } from 'src/models';
import { ATTENDANCE_STATUS, TIME_ZONE } from './constants';
import {
  IOrder,
  IGroupOptions,
  IFormattedOrgStats,
} from './interfaces/order.interface';

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetween);

// get season working schedule time
export function getCurrentSeason(
  checkDate: dayjs.Dayjs,
  workingSchedules: WorkingSchedule[],
) {
  const currentYear = checkDate.year();
  for (const schedule of workingSchedules) {
    const startDate = dayjs.tz(
      `${currentYear}-${schedule.effectiveFrom}`,
      'YYYY-MM-DD',
      TIME_ZONE,
    );
    let endDate = dayjs.tz(
      `${currentYear}-${schedule.effectiveTo}`,
      'YYYY-MM-DD',
      TIME_ZONE,
    );

    // if season is winter
    if (schedule.yearSpan) {
      endDate = endDate.add(1, 'year');
      const seasonStart = startDate;
      const seasonEnd = endDate;
      if (checkDate.isBetween(seasonStart, seasonEnd, null, '[]')) {
        return schedule;
      }
    } else {
      if (checkDate.isBetween(startDate, endDate, null, '[]')) {
        return schedule;
      }
    }
  }
  return null;
}

// function check time-check is valid
export function isCheckInValid(
  checkInTime: string | Date | dayjs.Dayjs,
  workingSchedules: WorkingSchedule[],
): {
  status: ATTENDANCE_STATUS;
  minutesDiff: number;
  season: string;
  requiredCheckInTime: dayjs.Dayjs;
} {
  const checkIn = dayjs.tz(checkInTime, TIME_ZONE).tz(TIME_ZONE);
  const currentSeason = getCurrentSeason(checkIn, workingSchedules);

  if (!currentSeason) throw new Error('Invalid config working time');

  const [requiredHour, requiredMinute] = (currentSeason.startTime || '')
    .split(':')
    .map(Number);

  // Create the required check-in time in the same timezone
  const requiredCheckInTime = dayjs(checkIn)
    .hour(requiredHour)
    .minute(requiredMinute)
    .second(0)
    .millisecond(0)
    .tz(TIME_ZONE);

  // Calculate difference in minutes
  const minutesDiff = checkIn.diff(requiredCheckInTime, 'minute');

  let status = ATTENDANCE_STATUS.KHONG_CHAM;

  if (minutesDiff <= 0) {
    status = ATTENDANCE_STATUS.DUNG_GIO; // Early arrival is considered on time
  } else {
    status = ATTENDANCE_STATUS.DI_MUON;
  }

  return {
    status,
    minutesDiff,
    season: currentSeason?.season || '',
    requiredCheckInTime,
  };
}

export function groupOrdersEQNByOrganization(
  data: IOrder[],
  options: IGroupOptions = {},
): IFormattedOrgStats[] {
  const { startDate, endDate } = options;

  const stats: IFormattedOrgStats = {
    organization: {
      id: '',
      name: '',
    },
    totalOrders: 0,
    statusSummary: {},
    dailyStats: [],
  };

  data.forEach((order) => {
    const orderDate = dayjs(order.date_created).tz(TIME_ZONE);
    const dateStr = orderDate.format('YYYY-MM-DD');

    // Filter by date if date range provided
    if (startDate && dayjs(startDate).tz(TIME_ZONE).isAfter(orderDate)) return;
    if (endDate && dayjs(endDate).tz(TIME_ZONE).isBefore(orderDate)) return;

    // Update total orders
    stats.totalOrders++;

    // Update status summary
    const status = order.status;
    stats.statusSummary[status] = (stats.statusSummary[status] || 0) + 1;

    // Find or create daily stat
    let dailyStat = stats.dailyStats.find((d) => d.date === dateStr);
    if (!dailyStat) {
      dailyStat = {
        date: dateStr,
        total: 0,
        statuses: {},
      };
      stats.dailyStats.push(dailyStat);
    }

    dailyStat.total++;
    dailyStat.statuses[status] = (dailyStat.statuses[status] || 0) + 1;
  });

  // Sort dailyStats by date
  stats.dailyStats.sort(
    (a, b) => dayjs(a.date).valueOf() - dayjs(b.date).valueOf(),
  );

  return [stats];
}
