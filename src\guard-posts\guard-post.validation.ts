import Joi from 'joi';
import { IApiError } from '../types/validation';

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jBodyGuardPost = Joi.object({
  name: Joi.string().max(250).required(),
  orderNum: Joi.number().required(),
  location: Joi.string().max(500),
  codeOrg: Joi.string().max(100).required(),
});

const eBodyGuardPost: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const jSearchGuardPost = Joi.object({
  name: Joi.string(),
  codeOrg: Joi.string(),
  location: Joi.string(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export {
  jIdInParams,
  eIdInParams,
  jBodyGuardPost,
  eBodyGuardPost,
  jSearchGuardPost,
  eSearchQuery,
};
