import bodyParser from 'body-parser';
import cors from 'cors';
import express from 'express';
import http from 'http';
import router from './routers';
import { initSocket } from './socket/socket.service';
import morgan_log from './utils/morgan-log';
import { removePreviewFileCron } from './cronjob/removePreviewFile.job';

const app = express();
const server = http.createServer(app);

initSocket(server);
// initRedis(); // dùng khi quyết làm cache

app.use(cors());
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use(morgan_log);

removePreviewFileCron();

app.use('/static/user-guides', express.static('uploads/user-guides'));
app.use('/static/images', express.static('uploads/images'));
app.use('/static/downloads', express.static('static/downloads'));

app.use('/api', router);

export default server;
