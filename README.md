## Deploy product

1. Download docker image Node:18

```bash

docker pull node:18

```

2. Apply product env

   Change .env file content by product environment

3. Build the docker image

```bash

  docker build -t product-image .

```

4.  Save the Docker image to a tar file for transfer:

```bash

  docker save -o product-image.tar product-image

```

5.  Upload image to server

6.  Load the image on server

```bash

  docker load -i product-image.tar

```

Verify the image was loaded:

```bash

 docker images

```

7. Run product

```bash

docker run -d --name backend --restart=always -p 3001:3001 product-image

```
