import _ from 'lodash';
import { ITree, ITreeNode } from '../types/tree';
import { getTree, getTreeNoLimitNoSkip, sortASC } from '../utils/tree';
import { database } from '../config';
import { Permission, User } from '../models';
import { ISearchQuery } from '../types/req';
import { ERROR } from '../utils/error';
import log from '../utils/log';
import { createLinks, DEFAULT_LIMIT } from '../utils/pagination';
import { ISearchPermissionQuery } from './permissions';
import { IApiError } from '../types/validation';
import { insertLog } from '../logs/logs.service';
import { LOG } from '../constants';
import { In } from 'typeorm';

function createFullText(permission: Partial<Permission>): string {
  const fields = [
    permission.name,
    permission.desc,
    permission.resource,
    permission.displayName,
  ];
  return fields.filter(Boolean).join(' ');
}

function isValidName(resourceName: string): boolean {
  const featurePattern = /^[A-Z][A-Z0-9]*(-[A-Z0-9]+)*$/;
  const parts = resourceName.split('_');
  if (parts.length !== 2) {
    const featurePattern = /^[A-Z][A-Z0-9]*(-[A-Z0-9]+)*$/;
    return featurePattern.test(resourceName);
  }
  const [action, feature] = parts;
  const isActionValid = featurePattern.test(action);
  const isFeatureValid = featurePattern.test(feature);
  return isActionValid && isFeatureValid;
}

function isValidResource(resourceName: string): boolean {
  const featurePattern = /^[a-z][a-z0-9]*(-[a-z0-9]+)*s$/;
  const parts = resourceName.split('_');
  if (parts.length !== 2) {
    const featurePattern = /^[a-zA-Z0-9]+(-[a-zA-Z0-9]+)*s$/;
    return featurePattern.test(resourceName);
  }
  const [action, feature] = parts;
  const isActionValid = /^[a-z][a-z0-9]*$/.test(action);
  const isFeatureValid = featurePattern.test(feature);
  return isActionValid && isFeatureValid;
}
function filterActivePermissions(permissions: Permission[]): Permission[] {
  const filterPermission = (permission: Permission): Permission | null => {
    const activeChildren = permission.children?.filter(
      (child) => child.status === 1,
    );
    if (permission.status !== 1) {
      return null;
    }

    return {
      ...permission,
      children: activeChildren
        ?.map(filterPermission)
        .filter(Boolean) as Permission[],
    } as Permission;
  };

  return permissions.map(filterPermission).filter(Boolean) as Permission[];
}
export const insertPermission = async (
  permission: Permission,
  user?: User,
  ip?: string,
) => {
  const errors: IApiError['errors'] = [];
  const repoPermission = database.getRepository(Permission);

  if (permission.parentId && permission.parentId !== null) {
    const existed = await repoPermission.findOne({
      where: { id: permission.parentId, status: 1 },
      withDeleted: true,
    });
    if (!existed) {
      errors.push({
        value: `parentId: ${permission.parentId}`,
        message: 'parentId không tồn tại.',
      });
    } else {
      permission.parent = existed;
    }
  }

  if (!isValidName(permission?.name || '')) {
    errors.push({
      field: 'name',
      value: `name: ${permission.name}`,
      message: 'Tên name không đúng cấu trúc.',
    });
  } else {
    const conditionExitsName = repoPermission
      .createQueryBuilder('permissions')
      .withDeleted()
      .select('')
      .where('LOWER(permissions.name) = LOWER(:name)', {
        name: permission.name,
      });
    const exitsName = await conditionExitsName.getMany();
    if (exitsName && exitsName.length > 0) {
      errors.push({
        field: 'name',
        value: `name ${permission.name}`,
        message: 'Tên permission đã tồn tại.',
      });
    }
  }

  if (!isValidResource(permission?.resource || '')) {
    errors.push({
      field: 'resource',
      value: `resource: ${permission.name}`,
      message: 'resource chưa đúng cấu trúc',
    });
  } else {
    const conditionExitsResource = repoPermission
      .createQueryBuilder('permissions')
      .withDeleted()
      .select('')
      .where('LOWER(permissions.resource) = LOWER(:resource)', {
        resource: permission.resource,
      });
    const exitsResource = await conditionExitsResource.getMany();
    if (exitsResource && exitsResource.length > 0) {
      errors.push({
        field: 'resource',
        value: `resource ${permission.name}`,
        message: 'Tên resource đã tồn tại.',
      });
    }
  }

  const error = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
    errors,
  };
  if (errors && errors.length > 0) {
    return { error };
  }
  permission.name = permission?.name?.toUpperCase();
  permission.resource = permission?.resource?.toLowerCase();
  permission.fullText = createFullText(permission);

  const tempPermission = repoPermission.create(permission);
  const newPermission = await repoPermission.save(tempPermission);

  await insertLog({
    ...(user && { content: `Created new permission: ${permission.name}` }),
    ...(ip && { ip }),
    ...(user && { userId: user.id }),
    typeId: LOG.CREATE,
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  return { newPermission };
};

export const updatePermission = async (
  id: number,
  body: Partial<Permission>,
  user?: User,
  ip?: string,
) => {
  const errors: IApiError['errors'] = [];
  const repoPermission = database.getRepository(Permission);

  const existed = await repoPermission.findOne({
    where: { id, status: 1 },
    withDeleted: true,
  });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  if (body.parentId) {
    const existedParent = await repoPermission.findOne({
      where: { id: body.parentId },
      withDeleted: true,
    });
    if (!existedParent) {
      errors.push({
        value: `parentId: ${body.parentId}`,
        message: 'parentId không tồn tại.',
      });
    } else {
      existed.parent = existedParent;
    }
  }

  if (body.name && body.name !== existed.name) {
    if (!isValidName(body.name)) {
      errors.push({
        field: 'name',
        value: `name: ${body.name}`,
        message: 'Tên name không đúng cấu trúc.',
      });
    } else {
      const conditionExitsName = repoPermission
        .createQueryBuilder('permissions')
        .withDeleted()
        .select('')
        .where('LOWER(permissions.name) = LOWER(:name)', { name: body.name });
      const exitsName = await conditionExitsName.getMany();
      if (exitsName && exitsName.length > 0) {
        errors.push({
          field: 'name',
          value: `name ${body.name}`,
          message: 'Tên permission đã tồn tại.',
        });
      }
    }
  }

  if (body.resource && body.resource !== existed.resource) {
    if (!isValidResource(body.resource)) {
      errors.push({
        field: 'resource',
        value: `resource ${body.resource}`,
        message: 'resource chưa đúng cấu trúc',
      });
    } else {
      const conditionExitsResource = repoPermission
        .createQueryBuilder('permissions')
        .withDeleted()
        .select('')
        .where('LOWER(permissions.resource) = LOWER(:resource)', {
          resource: body.resource,
        });
      const exitsResource = await conditionExitsResource.getMany();
      if (exitsResource && exitsResource.length > 0) {
        errors.push({
          field: 'resource',
          value: `resource ${body.name}`,
          message: 'Tên resource đã tồn tại.',
        });
      }
    }
  }

  const error = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
    errors,
  };
  if (errors && errors.length > 0) {
    return { error };
  }
  const permission = { ...existed, ...body };
  if (body.name) body.name = body.name.toUpperCase();
  if (body.resource) body.resource = body.resource.toLowerCase();
  permission.fullText = createFullText(permission);

  const updated = await repoPermission.save(permission);

  const log =
    user && ip
      ? {
          content: `Update permission: ${permission.name}; Updated fields: ${Object.entries(
            permission,
          )
            .filter(
              ([key, value]) =>
                key !== 'fullText' &&
                value !== existed[key as keyof Permission],
            )
            .map(
              ([key, value]) =>
                `${key}: ${existed[key as keyof Permission]} => ${value}`,
            )
            .join('; ')}`,
          ip,
          userId: user.id,
          typeId: LOG.UPDATE,
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      : undefined;

  if (log) await insertLog(log);
  return { updated };
};

export const deletePermission = async (id: number) => {
  const repoPermission = database.getRepository(Permission);

  const existed = await repoPermission.findOne({ where: { id, status: 1 } });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  existed.status = 1;
  const deleted = await repoPermission.save(existed);
  return { deleted };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(Permission);
  const role = await repo.findOne({
    where: { id, status: 1 },
    relations: ['parent'],
  });
  if (!role) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  return role;
};

type IPermission = {
  id?: number;
  name?: string;
  desc?: string;
  hasChildren?: boolean;
  level?: number;
  children?: IPermission[];
};
const reCreatePermission = async (permission: Permission) => {
  const repotree = database.getTreeRepository(Permission);
  const repo = database.getRepository(Permission);

  const hasChildren = await repo.find({
    where: { parentId: permission.id, status: 1 },
  });

  const level = (await repotree.countAncestors(permission)) - 1;
  if (permission.children && permission.children.length > 0) {
    const children: IPermission[] = [];
    for (let i = 0; i < permission.children.length; i++) {
      const element = permission.children[i];
      if (Number(element.status) === 1) {
        const newPermission = await reCreatePermission(element);
        if (newPermission) children.push(newPermission);
      }
    }
    return {
      ...permission,
      hasChildren: hasChildren.length > 0 ? true : false,
      level,
      children,
    };
  } else {
    if (Number(permission.status) === 1) {
      return {
        ...permission,
        hasChildren: hasChildren.length > 0 ? true : false,
        level,
        children: [],
      };
    }
  }
};
const sortASCPermission = (node: IPermission) => {
  if (node?.children?.length && node?.children?.length > 0) {
    for (let i = 0; i < node.children.length; i++) {
      node.children.sort((one, two) =>
        Number(one.name) > Number(two.name) ? 1 : -1,
      );
      if (node?.children?.[i]?.children?.length)
        sortASCPermission(node.children[i]);
    }
    return node;
  }
};
export const findTreeById = async (id: number, depth?: number) => {
  log.debug('findTreeById:', id);
  const repo = database.getTreeRepository(Permission);
  const existed = await repo.findOne({ where: { id, status: 1 } });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  const curTree = depth
    ? await repo.findDescendantsTree(existed, { depth })
    : await repo.findDescendantsTree(existed);

  const temp = await reCreatePermission(curTree);
  log.debug('temp:', temp);
  if (temp) {
    const res = sortASCPermission(temp);
    return res;
  }
};

export const findAll = async (query: ISearchQuery<ISearchPermissionQuery>) => {
  async function createNode(permission: Permission) {
    const repotree = database.getTreeRepository(Permission);
    const repo = database.getRepository(Permission);

    const hasChildren = await repo.find({
      where: { parentId: permission.id, status: 1 },
    });
    const level = (await repotree.countAncestors(permission)) - 1;

    if (permission.children && permission.children.length > 0) {
      const children: ITreeNode[] = [];
      for (let i = 0; i < permission.children.length; i++) {
        const element = permission.children[i];
        if (Number(element.status) === 1) {
          console.log('status:', element.status); //eslint-disable-line
          const newTemplate = await createNode(element);
          if (newTemplate) children.push(newTemplate);
        }
      }
      return {
        id: permission.id,
        name: permission.name,
        hasChildren: hasChildren.length > 0 ? true : false,
        level,
        children,
      };
    } else {
      if (permission.status) {
        return {
          id: permission.id,
          name: permission.name,
          hasChildren: hasChildren.length > 0 ? true : false,
          level,
          children: [],
        };
      }
    }
  }
  //=======================================================================//
  const limit: number = query.limit ? Number(query.limit) : DEFAULT_LIMIT;
  const page: number = query.page ? Number(query.page) : 1;
  //const depth: number = query.depth ? Number(query.depth) : DEPTH;

  const repo = database.getRepository(Permission);
  const repoTree = database.getTreeRepository(Permission);

  const treeResult: ITreeNode[] = [];

  const conditions = repo
    .createQueryBuilder('permissions')
    .andWhere('permissions.parentId is null')
    .andWhere('permissions.status = 1')
    .orderBy('permissions.id', 'DESC');

  const totalItems = await conditions.getCount();
  const totalPages = Math.ceil(totalItems / limit);

  const links = createLinks('/permissions?', query, page, totalPages);

  const permissions = await conditions
    .skip((page - 1) * limit)
    .take(limit)
    .getMany();

  if (permissions && permissions.length > 0) {
    await Promise.all(
      permissions.map(async (parentPermission) => {
        const childrenTreeWithLimitedDepth =
          await repoTree.findDescendantsTree(parentPermission);
        const childNodes: ITreeNode[] = [];
        if (
          childrenTreeWithLimitedDepth &&
          childrenTreeWithLimitedDepth.children
        ) {
          await Promise.all(
            childrenTreeWithLimitedDepth.children.map(async (item) => {
              console.log('statussssss:', item.status); //eslint-disable-line
              if (Number(item.status) === 1) {
                const node = await createNode(item);
                if (node) childNodes.push(node);
              }
            }),
          );
          sortASC(childNodes);
        }
        treeResult.push({
          id: parentPermission.id,
          name: parentPermission.name,
          hasChildren:
            (await repoTree.countDescendants(parentPermission)) > 1
              ? true
              : false,
          level: 0,
          children: childNodes,
          isEnable: false,
        });
      }),
    );
  }

  treeResult.sort((one, two) => {
    const nameA = one?.name ?? '';
    const nameB = two?.name ?? '';
    return nameA > nameB ? 1 : -1;
  });

  return {
    meta: {
      totalItems,
      itemsPerPage: limit,
      totalPages,
      currentPages: page,
      items: treeResult.length > 0 ? treeResult : [],
    },
    links,
  };
};

export const searchPermissions = async (
  query: ISearchQuery<ISearchPermissionQuery>,
) => {
  const repoTreePermissions = database.getTreeRepository(Permission);
  const repoPermissions = database.getTreeRepository(Permission);
  const flatPermissions: ITreeNode[] = [];
  const dataPermissions: Permission[] = [];
  const limit: number = query.limit ? Number(query.limit) : DEFAULT_LIMIT;
  let tree: ITree = {
    length: 0,
    node: [],
    countUseLeaf: 0,
    countCurrentLeaf: 0,
  };

  const conditions = repoPermissions
    .createQueryBuilder('permissions')
    .andWhere('LOWER(permissions.name) like LOWER(:name)', {
      name: `%${query.name}%`,
    })
    .orderBy('permissions.id', 'DESC');
  const permissions = await conditions.getMany();

  if (permissions && permissions.length > 0) {
    await Promise.all(
      permissions.map(async (childPermission) => {
        const ancestors =
          await repoTreePermissions.findAncestors(childPermission);

        if (ancestors && ancestors.length > 0) {
          await Promise.all(
            ancestors.map(async (item) => {
              const index = _.findIndex(
                dataPermissions,
                (d: ITreeNode) => d.id === item.id,
              );
              if (index === -1) {
                dataPermissions.push(item);
                const numDescendants =
                  await repoTreePermissions.countDescendants(item);
                const numAncestors =
                  await repoTreePermissions.countAncestors(item);

                flatPermissions.push({
                  id: item.id,
                  parentId: item.parentId,
                  name: item.name,
                  hasChildren: numDescendants > 1 ? true : false,
                  level: numAncestors > 1 ? numAncestors - 1 : 0,
                  children: [],
                });
              }
            }),
          );
        } ///
      }),
    );
  }

  tree = getTree(
    [...flatPermissions],
    Number(query.skip ? query.skip : 0),
    limit,
  );

  const totalItems = tree.length;
  if (tree.node) sortASC(tree.node);

  return {
    meta: {
      totalItems,
      itemsCurrent: tree.countCurrentLeaf ? tree.countCurrentLeaf : 0,
      countUseLeaf: tree.countUseLeaf ? tree.countUseLeaf : 0,
      countRemainLeaf: tree.countRemainLeaf ? tree.countRemainLeaf : 0,
      itemsPerPage: 0,
      totalPages: 0,
      currentPages: 0,
      items: tree.node ? tree.node : [],
    },
    links: {},
  };
};
export const findAncestorsById = async (id: number) => {
  const flatPermissions: ITreeNode[] = [];
  const repoPermission = database.getRepository(Permission);
  const repoTree = database.getTreeRepository(Permission);

  const deviceTemplate = await repoPermission.findOne({ where: [{ id }] });
  if (!deviceTemplate) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  const templates = await repoTree.findAncestors(deviceTemplate);

  templates.map((item) => {
    const numDescendants = templates.filter(
      (ele) => ele.parentId == item.id,
    ).length;
    flatPermissions.push({
      id: item.id,
      parentId: item.parentId,
      name: item.name,
      hasChildren: numDescendants > 0 ? true : false,
      level: 0,
      children: [],
    });
  });
  const tree = getTreeNoLimitNoSkip(flatPermissions);
  return tree[0];
};

export const findTreeAll = async () => {
  const permissionRepo = database.getTreeRepository(Permission);
  const permissions = await permissionRepo.findTrees({});
  return filterActivePermissions(permissions);
};

export const changeStatusPermission = async (ids: number[]) => {
  const repoPermission = database.getRepository(Permission);

  const existPermissions = await repoPermission.find({
    where: { id: In(ids) },
  });

  const existPermissionIds = existPermissions.map((item) => item.id);

  const notExistPermissionIds = ids.filter(
    (item) => !existPermissionIds.includes(item),
  );

  const updateSuccess = await repoPermission.update(
    { id: In(existPermissionIds) },
    { status: 0 },
  );
  if (updateSuccess.affected === 0) {
    return updateSuccess;
  } else return { updateSuccess, errors: notExistPermissionIds };
};

export const findGroupTree = async () => {
  const permissionTreeRepository = database.getTreeRepository(Permission);
  const permissionRepository = database.getRepository(Permission);

  const rootPermissionNodes = await permissionRepository
    .createQueryBuilder('permission')
    .where('permission.parentId IS NULL')
    .getMany();

  const treePromises = rootPermissionNodes.map(async (rootNode) => {
    const permissions =
      await permissionTreeRepository.findDescendants(rootNode);
    return {
      ...rootNode,
      children: [...permissions].filter((item) => item.id !== rootNode.id),
    };
  });

  return Promise.all(treePromises);
};
