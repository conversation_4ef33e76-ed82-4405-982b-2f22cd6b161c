import { ISearchQuery } from '../types/req';
import { database } from '../config';
import {
  GuestLog,
  GuardPost,
  User,
  Organization,
  Type,
  Guest,
  IdentifyCard,
  CatLichTruc,
  eQN,
} from '../models';
import { ERROR } from '../utils/error';
import { InOutStatus, LOG, TYPE_SCOPES, WorkingStatus } from '../constants';
import {
  ICreateGuestLogBody,
  IGuestLogGroup,
  ISearchGuestLogQuery,
  IWebhookGuestLogBody,
  IGuestLogStatsByOrgCodeQuery,
  IGuestLogStatsByOrgCodeResult,
  IGuestLogHistoryInOutQuery,
} from './guest-log';
import { ISearchPendingGuestLogQuery } from './guest-log';
import { createLinks } from '../utils/pagination';
import { io } from '../socket/socket.service';
import { PendingGuest } from '../models/pendingGuest.model';
import { insertLog } from '../logs/logs.service';
import { In, Like, SelectQueryBuilder } from 'typeorm';
import { getAllChildOrganizations } from '../organizations/organization.service';
import { IApiError } from '../types/validation';
import { checkArrayQuery } from '../utils/array';
import { parseJson } from '../utils/json';
import dayjs from 'dayjs';
import { exportDoc } from '../utils/exportFile';

export const processWebhook = async (data: IWebhookGuestLogBody) => {
  const guestLogRepo = database.getRepository(GuestLog);
  const pendingGuestRepo = database.getRepository(PendingGuest);
  const guardPostRepo = database.getRepository(GuardPost);
  const typeRepo = database.getRepository(Type);

  const guardPost = await guardPostRepo.findOne({
    where: { id: data.guardPostId },
    withDeleted: true,
  });

  if (!guardPost) {
    throw new Error(ERROR.GUARD_POST_NOT_FOUND);
  }

  if (data.status === InOutStatus.IN) {
    let vehicleType: Type | null = null;
    let vehicleClassification: Type | null = null;

    if (data.vehicleTypeValue) {
      vehicleType = await typeRepo.findOne({
        where: {
          value: data.vehicleTypeValue,
          scope: TYPE_SCOPES.VEHICLE_TYPES,
        },
        withDeleted: true,
      });
      if (!vehicleType) {
        throw new Error(ERROR.VEHICLE_TYPE_NOT_FOUND);
      }
    }
    if (data.vehicleClassificationValue) {
      vehicleClassification = await typeRepo.findOne({
        where: {
          value: data.vehicleClassificationValue,
          scope: TYPE_SCOPES.VEHICLE_CLASSES,
        },
        withDeleted: true,
      });
      if (!vehicleClassification) {
        throw new Error(ERROR.VEHICLE_CLASSIFICATION_NOT_FOUND);
      }
    }

    if (data.codeOrg) {
      const guestOrg = await Organization.findOne({
        where: { code: data.codeOrg },
        withDeleted: true,
      });
      if (!guestOrg) {
        throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
      }
    }

    const pendingGuestData = pendingGuestRepo.create({
      idVehicleLog: data.idVehicleLog,
      defaultImage: data.defaultImage,
      guestCardId: data.guestCardId,
      vehicleType: vehicleType || null,
      vehicleClassification: vehicleClassification || null,
      etiquette: data.etiquette,
      guardPostId: data.guardPostId,
      vehicleData: JSON.stringify({
        licensePlate: data.licensePlate,
        commodity: data.commodity,
      }),
      codeOrg: data.codeOrg,
    } as PendingGuest);

    const newPendingGuest = await pendingGuestRepo.save(pendingGuestData);

    io?.to(`org:${data.codeOrg}`).emit('new-guest-log', {
      idVehicleLog: data.idVehicleLog,
      pendingGuestId: newPendingGuest.id,
      guardPostId: data.guardPostId,
    });
    const saved = await pendingGuestRepo.findOne({
      where: {
        id: newPendingGuest.id,
      },
      withDeleted: true,
    });
    await insertLog({
      typeId: LOG.VEHICLE_IN,
      userId: 20,
      ip: 'localhost',
      content: `Gửi tín hiệu thông báo sự kiện phát hiện phương tiện vào khỏi đơn vị: 
      biển số xe =${saved ? saved.vehicleData : ''}, Thời gian: ${dayjs(new Date()).format('HH:mm:ss DD/MM/YYYY')}`,
    });
    await insertLog({
      typeId: LOG.CREATE,
      userId: 20,
      ip: 'localhost',
      content: `Nhận tín hiệu đã tiếp nhận xử lý sự kiện: 
      id =${saved ? saved.id : ''}, Thời gian: ${dayjs(new Date()).format('HH:mm:ss DD/MM/YYYY')}`,
    });
  } else if (data.status === InOutStatus.OUT) {
    const guestLog = await guestLogRepo.findOne({
      where: {
        idVehicleLog: data.idVehicleLog,
        statusId: WorkingStatus.ENTERED,
        codeOrg: data.codeOrg,
      },
      withDeleted: true,
    });

    if (!guestLog) {
      throw new Error(ERROR.GUEST_LOG_NOT_FOUND);
    }

    guestLog.statusId = WorkingStatus.GO_OUT;
    guestLog.checkOutTime = new Date();
    await guestLogRepo.save(guestLog);

    await insertLog({
      typeId: LOG.VEHICLE_OUT,
      userId: 20,
      ip: 'localhost',
      content: `Gửi tín hiệu thông báo sự kiện phát hiện phương tiện ra khỏi đơn vị: Biến số xe=${guestLog.vehicleData}, Thời gian: ${dayjs(guestLog.checkOutTime).format('HH:mm:ss DD/MM/YYYY')}`,
    });
    await insertLog({
      typeId: LOG.UPDATE,
      userId: 20,
      ip: 'localhost',
      content: `Nhận tín hiệu đã tiếp nhận xử lý sự kiện: 
      id =${guestLog ? guestLog.id : ''}, Thời gian: ${dayjs(new Date()).format('HH:mm:ss DD/MM/YYYY')}`,
    });
    io?.to(`org:${data.codeOrg}`).emit('update-guest-log', {
      idVehicleLog: data.idVehicleLog,
      guestLogId: guestLog,
      guardPostId: data.guardPostId,
    });
  }

  return { message: 'Webhook processed successfully' };
};

export const createPendingGuestSearchQuery = async (
  qb: SelectQueryBuilder<PendingGuest>,
  query: ISearchQuery<ISearchPendingGuestLogQuery>,
  user: User,
) => {
  qb.where('pending_guest.code_org like :codeOrg', {
    codeOrg: user.manageOrgCode,
  });

  if (query?.guestCardId) {
    qb.andWhere('pending_guest.guest_card_id like :guestCardId', {
      guestCardId: `%${query.guestCardId}%`,
    });
  }

  if (query?.search) {
    qb.andWhere('pending_guest.full_text like :search', {
      search: `%${query.search}%`,
    });
  }

  if (query?.vehicleClassificationIds) {
    qb.andWhere(
      'pending_guest.vehicle_classification_id IN (:...vehicleClassificationIds)',
      {
        vehicleClassificationIds: checkArrayQuery(
          query.vehicleClassificationIds,
        ),
      },
    );
  }

  if (query?.createdAt) {
    qb.andWhere('pending_guest.createdAt BETWEEN :fromDate AND :toDate', {
      fromDate: new Date(query.createdAt[0]),
      toDate: new Date(query.createdAt[1]),
    });
  }
  if (query?.vehicleTypeIds) {
    qb.andWhere('pending_guest.vehicle_type_id IN (:...vehicleTypeIds)', {
      vehicleTypeIds: checkArrayQuery(query.vehicleTypeIds),
    });
  }

  if (query?.guardPostIds) {
    qb.andWhere('pending_guest.guard_post_id IN (:...guardPostIds)', {
      guardPostIds: checkArrayQuery(query.guardPostIds),
    });
  }

  if (query?.inDateFrom) {
    qb.andWhere('pending_guest.createdAt >= :fromDate', {
      fromDate: new Date(query.inDateFrom),
    });
  }

  if (query?.inDateTo) {
    qb.andWhere('pending_guest.createdAt <= :toDate', {
      toDate: new Date(query.inDateTo),
    });
  }
};

export const searchPendingGuests = async (
  query: ISearchQuery<ISearchPendingGuestLogQuery>,
  user: User,
) => {
  const pendingGuestRepo = database.getRepository(PendingGuest);

  const qb = pendingGuestRepo
    .createQueryBuilder('pending_guest')
    .withDeleted()
    .leftJoinAndSelect('pending_guest.guardPost', 'guardPost')
    .leftJoinAndSelect('pending_guest.vehicleType', 'vehicleType');

  createPendingGuestSearchQuery(qb, query, user);

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit).orderBy('pending_guest.createdAt', 'DESC');

  const [data, total] = await qb.getManyAndCount();

  data.forEach((item) => {
    item.vehicleData = item.vehicleData ? JSON.parse(item.vehicleData) : {};
  });
  const links = createLinks(
    '/guest-logs/pending?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};

export const deletePendingGuests = async (
  ids: number[],
  user: User,
  ip?: string,
) => {
  const repo = database.getRepository(PendingGuest);
  const idArr = Array.isArray(ids) ? ids : [ids];
  const pendingGuests = await repo.find({
    where: {
      id: In(idArr),
      guardPost: {
        codeOrg: user.manageOrgCode,
      },
    },
    withDeleted: true,
  });

  if (pendingGuests.length !== idArr.length || pendingGuests.length === 0) {
    throw new Error(ERROR.PENDING_GUEST_NOT_FOUND);
  }

  await repo.delete(idArr);

  await insertLog({
    typeId: LOG.DELETE,
    userId: user.id,
    content: 'Xóa các bản ghi của khách chờ: ' + ids?.join(', '),
    ip,
  });
  return { message: 'Deleted successfully' };
};

const verifyGuestLogData = async (data: ICreateGuestLogBody) => {
  const organizationRepo = database.getRepository(Organization);
  const typeRepo = database.getRepository(Type);
  const guestLogRepo = database.getRepository(GuestLog);
  const guestRepo = database.getRepository(Guest);
  const identifyCardRepo = database.getRepository(IdentifyCard);
  const eQNRepo = database.getRepository(eQN);

  const errors: IApiError['errors'] = [];

  // kiểm tra mục đích làm việc
  if (data.purposeCategoryId) {
    const purposeCategory = await typeRepo.findOne({
      where: {
        id: data.purposeCategoryId,
        scope: TYPE_SCOPES.PURPOSE_CATEGORIES,
      },
      withDeleted: true,
    });
    if (!purposeCategory) {
      errors.push({
        field: 'purposeCategoryId',
        value: `purposeCategoryId:${data.purposeCategoryId}`,
        message: 'Không tìm thấy loại mục đích làm việc.',
      });
    }
  }

  // Kiểm tra xem có tồn tại bản ghi cha không khi là khách đoàn
  if (data?.parentId) {
    const parentGuestLog = await guestLogRepo.findOne({
      where: { id: data.parentId },
      withDeleted: true,
    });
    if (!parentGuestLog) {
      errors.push({
        field: 'parentId',
        value: `parentId:${data.parentId}`,
        message: 'Không tìm thấy thông tin đoàn khách.',
      });
    } else if (parentGuestLog.purposeCategoryId) {
      data.purposeCategoryId = parentGuestLog.purposeCategoryId;
    }
  }

  // Kiểm tra người tiếp khách
  const meetWho = await eQNRepo.findOne({
    where: { id: data.meetWhoId },
    withDeleted: true,
  });

  data.meetOrgCode = meetWho?.orgCode || '';

  if (!meetWho) {
    errors.push({
      field: 'meetWhoId',
      value: `meetWhoId:${data.meetWhoId}`,
      message: 'Không tìm thấy người tiếp khách.',
    });
  }

  // kiểm tra đơn vị của khách
  if (data?.guestCodeOrg) {
    const guestOrg = await organizationRepo.findOne({
      where: { code: data.guestCodeOrg },
      withDeleted: true,
    });
    if (!guestOrg) {
      errors.push({
        field: 'guestCodeOrg',
        value: `guestCodeOrg:${data.guestCodeOrg}`,
        message: 'Không tìm thấy đơn vị của khách.',
      });
    }
  }

  // kiểm tra loại khách
  const guestType = await typeRepo.findOne({
    where: { id: data.guestTypeId, scope: TYPE_SCOPES.GUEST_TYPES },
    withDeleted: true,
  });

  if (!guestType) {
    errors.push({
      field: 'guestTypeId',
      value: `guestTypeId:${data.guestTypeId}`,
      message: 'Không tìm loại khách.',
    });
  }

  const { identifyCard, ...guest } = data.guest;
  let guestData: Guest | null;

  if (guest?.id) {
    guestData = await guestRepo.findOne({
      where: { id: guest.id },
    });
    if (!guestData) {
      errors.push({
        field: 'guestId',
        value: `guestId:${data.guest.id}`,
        message: 'Không tìm thấy thông tin khách.',
      });
    }
    await guestRepo.save({ ...guestData, ...guest });
  } else {
    //TODO: sau này check thêm điều kiện sđt hoặc gì để kiểm tra xem có phải khách mới hay không
    const newGuest = guestRepo.create(guest);
    guestData = await guestRepo.save(newGuest);
  }

  const identifyDocumentType = await typeRepo.findOne({
    where: {
      id: data?.guest?.identifyCard?.documentTypeId || 0,
      scope: TYPE_SCOPES.IDENTIFY_DOCUMENT_TYPES,
    },
    withDeleted: true,
  });

  if (!identifyDocumentType) {
    errors.push({
      field: 'documentTypeId',
      value: `documentTypeId:${data?.guest?.identifyCard?.documentTypeId}`,
      message: 'Không tìm loại giấy tờ tùy thân.',
    });
  }

  const identifyCardData = await identifyCardRepo.findOne({
    where: {
      documentTypeId: data?.guest?.identifyCard?.documentTypeId,
      guestId: guestData?.id,
    },
    withDeleted: true,
  });

  if (!identifyCardData) {
    const newIdentify = identifyCardRepo.create({
      ...identifyCard,
      guestId: guestData?.id,
    });
    await identifyCardRepo.save(newIdentify);
  } else {
    await identifyCardRepo.update(identifyCardData?.id as number, identifyCard);
  }
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  data.guest.id = guestData?.id as any;

  return errors;
};

export const createGuestLog = async (
  data: ICreateGuestLogBody,
  user: User,
  ip?: string,
) => {
  const guestLogRepo = database.getRepository(GuestLog);
  const pendingGuestRepo = database.getRepository(PendingGuest);
  const catLichTrucRepo = database.getRepository(CatLichTruc);

  const errors = await verifyGuestLogData(data);

  // kiểm tra ca trực ban nội vụ
  const findEQN = await database.getRepository(eQN).findOne({
    where: {
      id: data?.guestIdBy,
    },
  });

  const catLichTruc = await catLichTrucRepo.findOne({
    where: [
      {
        // mainId: findEQN?.id,
        // date: Between(
        //   dayjs().startOf('day').toDate(),
        //   dayjs().endOf('day').toDate(),
        // ),
        // dutySchedule: {
        //   codeOrg: user.manageOrgCode,
        // },
      },
      {
        // assistantId: findEQN?.id,
        // date: Between(
        //   dayjs().startOf('day').toDate(),
        //   dayjs().endOf('day').toDate(),
        // ),
        // dutySchedule: {
        //   codeOrg: user.manageOrgCode,
        // },
      },
    ],
    relations: ['dutySchedule'],
    withDeleted: true,
  });

  if (!catLichTruc) {
    errors.push({
      field: 'guestIdBy',
      value: `guestIdBy:${data.guestIdBy}`,
      message: 'Không tìm thấy thông tin ca trực của người trực ban nội vụ.',
    });
  }

  const pendingGuest = await pendingGuestRepo.findOne({
    where: { id: data.pendingGuestId },
    withDeleted: true,
  });

  if (data?.pendingGuestId) {
    if (!pendingGuest) {
      errors.push({
        field: 'pendingGuestId',
        value: `pendingGuestId:${data.pendingGuestId}`,
        message: 'Không tìm thấy thông tin khách chờ.',
      });
    }
  }

  if (errors && errors.length > 0) {
    const error = {
      part: 'body',
      code: ERROR.BAD_REQUEST,
      message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
      errors,
    };

    return { error };
  }

  const { guest, ...guestLogData } = data;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { createdAt, updatedAt, id, ...pendingGuestData } = pendingGuest || {};

  let newGuestLog = guestLogRepo.create({
    ...guestLogData,
    ...pendingGuestData,
    checkInTime: new Date(),
    guestId: guest.id,
    statusId: WorkingStatus.ENTERED,
    codeOrg: user.manageOrgCode,
    guestOrganizationName: guest.guestOrganizationName,
    // catLichTrucId: catLichTruc?.id || 0,
  });

  newGuestLog = await guestLogRepo.save(newGuestLog);

  // Xóa thông tin khách chờ
  if (pendingGuest) {
    await pendingGuestRepo.delete(pendingGuest?.id as number);
  }

  await insertLog({
    typeId: LOG.CREATE,
    userId: user.id,
    content: 'Tạo mới bản ghi guest log: ' + newGuestLog.id,
    ip,
  });
  if (data?.isSendMessageForMeetUser) {
    await insertLog({
      typeId: LOG.CREATE,
      userId: user.id,
      content: `Gửi tin nhắn tới người đón khách: ${findEQN?.fullName} có số điện thoại: ${findEQN?.phoneNumber}`,
      ip,
    });
  }

  return { message: 'Created successfully', data: newGuestLog };
};

export const editGuestLog = async (
  id: number,
  data: ICreateGuestLogBody,
  user: User,
  ip?: string,
) => {
  const guestLogRepo = database.getRepository(GuestLog);

  const guestLog = await guestLogRepo.findOne({
    where: { id, codeOrg: user.manageOrgCode },
    withDeleted: true,
  });

  if (!guestLog) {
    throw new Error(ERROR.GUEST_LOG_NOT_FOUND);
  }

  const errors = await verifyGuestLogData(data);

  if (errors && errors.length > 0) {
    const error = {
      part: 'body',
      code: ERROR.BAD_REQUEST,
      message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
      errors,
    };

    return { error };
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { guest, ...guestLogData } = data;

  await guestLogRepo.save({ ...guestLog, ...guestLogData });

  await insertLog({
    typeId: LOG.UPDATE,
    userId: user.id,
    content: 'Cập nhật bản ghi guest log: ' + guestLog.id,
    ip,
  });

  return { message: 'Created successfully' };
};

export const returnCard = async (id: number, user: User, ip?: string) => {
  const guestLogRepo = database.getRepository(GuestLog);
  const catLichTrucRepo = database.getRepository(CatLichTruc);
  const guestLog = await guestLogRepo.findOne({
    where: { id, codeOrg: user.manageOrgCode },
    relations: ['children'],
  });

  const catLichTruc = await catLichTrucRepo.findOne({
    where: {
      // date: Between(
      //   dayjs().startOf('day').toDate(),
      //   dayjs().endOf('day').toDate(),
      // ),
      // dutySchedule: {
      //   codeOrg: user.manageOrgCode,
      // },
    },
    withDeleted: true,
  });

  if (!guestLog) {
    throw new Error(ERROR.GUEST_LOG_NOT_FOUND);
  }

  const updateData = {
    statusId: WorkingStatus.GO_OUT,
    returnTime: new Date(),
    checkOutTime: new Date(),
    durationInside: dayjs(new Date()).diff(guestLog.checkInTime, 'minute'),
    catLichTrucOutId: catLichTruc?.id || 0,
  };

  await guestLogRepo.update(guestLog.id as number, updateData);

  if (guestLog?.children && guestLog?.children?.length > 0) {
    const childrenIds = guestLog.children.map((child) => child.id);
    await guestLogRepo.update(childrenIds as number[], updateData);
  }

  await insertLog({
    typeId: LOG.UPDATE,
    userId: user.id,
    content: 'Khách trả thẻ: ' + guestLog.id,
    ip,
  });

  return { message: 'Returned successfully' };
};

// tạo query
const createGuestLogSearchQuery = (
  qb: SelectQueryBuilder<GuestLog>,
  query: ISearchQuery<ISearchGuestLogQuery>,
  user: User,
  catLichTruc?: CatLichTruc | null,
) => {
  qb.where('guestLogs.parent_id IS NULL AND guestLogs.code_org LIKE :codeOrg', {
    codeOrg: user.manageOrgCode,
  });

  if (query?.guestCardId) {
    qb.andWhere('guestLogs.guest_card_id LIKE :guestCardId', {
      guestCardId: `%${query.guestCardId}%`,
    });
  }

  if (query?.identificationNumber) {
    qb.andWhere(
      'identifyCards.identification_number LIKE :identificationNumber',
      {
        identificationNumber: `%${query.identificationNumber}%`,
      },
    );
  }

  if (query.inDateFrom) {
    qb.andWhere('guestLogs.check_in_time >= :fromDate', {
      fromDate: new Date(query.inDateFrom),
    });
  }

  if (query.inDateTo) {
    qb.andWhere('guestLogs.check_in_time <= :toDate', {
      toDate: new Date(query.inDateTo),
    });
  }

  if (query.outDateFrom) {
    qb.andWhere('guestLogs.check_out_time >= :fromDate', {
      fromDate: new Date(query.outDateFrom),
    });
  }

  if (query.outDateTo) {
    qb.andWhere('guestLogs.check_out_time <= :toDate', {
      toDate: new Date(query.outDateTo),
    });
  }

  if (query?.isInDuty == 'true') {
    qb.andWhere(
      '(guestLogs.detail_duty_id = :catLichTrucId OR guestLogs.detail_duty_out_id = :catLichTrucId)',
      {
        catLichTrucId: catLichTruc?.id || 0,
      },
    );
  }

  if (query.vehicleClassificationIds) {
    qb.andWhere(
      'guestLogs.vehicle_classification_id IN (:...vehicleClassificationIds)',
      {
        vehicleClassificationIds: checkArrayQuery(
          query.vehicleClassificationIds,
        ),
      },
    );
  }

  if (query?.purposeCategoryIds) {
    qb.andWhere('guestLogs.purpose_category_id IN (:...purposeCategoryIds)', {
      purposeCategoryIds: checkArrayQuery(query.purposeCategoryIds),
    });
  }

  if (query?.statusIds) {
    qb.andWhere('guestLogs.status_id IN (:...statusIds)', {
      statusIds: checkArrayQuery(query.statusIds),
    });
  }

  if (query?.vehicleTypeIds) {
    qb.andWhere('guestLogs.vehicle_type_id IN (:...vehicleTypeIds)', {
      vehicleTypeIds: checkArrayQuery(query.vehicleTypeIds),
    });
  }

  if (query?.search) {
    qb.andWhere('guestLogs.fullText LIKE :search', {
      search: `%${query.search}%`,
    });
  }

  if (query.guestName) {
    qb.andWhere('guest.fullName like :guestName', {
      guestName: `%${query.guestName}%`,
    });
  }

  if (query.guestIdentityTypeId) {
    qb.andWhere('identifyCards.document_type_id like :documentType', {
      documentType: query.guestIdentityTypeId,
    });
  }

  if (query.meetWhoId) {
    qb.andWhere('guestLogs.meet_who_id like :meetWhoId', {
      meetWhoId: query.meetWhoId,
    });
  }

  if (query.meetOrgCode) {
    qb.andWhere('guestLogs.meet_org_code like :meetOrgCode', {
      meetOrgCode: query.meetOrgCode,
    });
  }

  if (query.guestTypeIds) {
    qb.andWhere('guestLogs.guest_type_id IN (:...guestTypeIds)', {
      guestTypeIds: checkArrayQuery(query.guestTypeIds),
    });
  }
};

export const searchGuestLogs = async (
  query: ISearchQuery<ISearchGuestLogQuery>,
  user: User,
) => {
  const guestLogs = database.getRepository(GuestLog);

  const qb = guestLogs
    .createQueryBuilder('guestLogs')
    .withDeleted()
    .leftJoinAndSelect('guestLogs.guest', 'guest')
    .leftJoinAndSelect('guestLogs.guardPost', 'guardPost')
    .leftJoinAndSelect('guestLogs.purposeCategory', 'purposeCategory')
    .leftJoinAndSelect('guestLogs.status', 'status')
    .leftJoinAndSelect('guestLogs.vehicleType', 'vehicleType')
    .leftJoinAndSelect('guestLogs.guestType', 'guestType')
    .leftJoinAndSelect('guestLogs.meetWho', 'meetWho')
    .leftJoinAndSelect('guestLogs.meetOrganization', 'meetOrganization')
    .leftJoinAndSelect('guestLogs.catLichTruc', 'catLichTruc')
    .leftJoinAndSelect('catLichTruc.main', 'main')
    .leftJoinAndSelect('catLichTruc.assistant', 'assistant')
    .leftJoinAndSelect('guestLogs.guestBy', 'guestBy')
    .leftJoinAndSelect('guest.identifyCards', 'identifyCards')
    .leftJoinAndSelect('identifyCards.documentType', 'documentType');

  let catLichTruc: CatLichTruc | null = null;
  if (query?.isInDuty == 'true') {
    const catLichTrucRepo = database.getRepository(CatLichTruc);
    catLichTruc = await catLichTrucRepo.findOne({
      where: {
        // date: Between(
        //   dayjs().startOf('day').toDate(),
        //   dayjs().endOf('day').toDate(),
        // ),
        // dutySchedule: {
        //   codeOrg: user.manageOrgCode,
        // },
      },
    });
  }

  createGuestLogSearchQuery(qb, query, user, catLichTruc);

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit).orderBy('guestLogs.createdAt', 'DESC');

  const [data, total] = await qb.getManyAndCount();
  data.forEach((item) => {
    item.vehicleData = item.vehicleData ? JSON.parse(item.vehicleData) : {};
  });
  const links = createLinks(
    '/guest-logs?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};

export const getGuestLogByIdVehicleLog = async (id: string) => {
  const guestLogs = database.getRepository(GuestLog);

  let guestLog = await guestLogs
    .createQueryBuilder('guestLog')
    .withDeleted()
    .leftJoinAndSelect('guestLog.guardPost', 'guardPost')
    .leftJoinAndSelect('guestLog.purposeCategory', 'purposeCategory')
    .leftJoinAndSelect('guestLog.status', 'status')
    .leftJoinAndSelect('guestLog.vehicleType', 'vehicleType')
    .leftJoinAndSelect('guestLog.guestType', 'guestType')
    .leftJoinAndSelect('guestLog.meetWho', 'meetWho')
    .leftJoinAndSelect('guestLog.meetOrganization', 'meetOrganization')
    .leftJoinAndSelect('guestLog.catLichTruc', 'catLichTruc')
    .leftJoinAndSelect('catLichTruc.main', 'main')
    .leftJoinAndSelect('catLichTruc.assistant', 'assistant')
    .leftJoinAndSelect('guestLog.guestBy', 'guestBy')
    .leftJoinAndSelect('guestLog.guest', 'guest')
    .leftJoinAndSelect('guest.identifyCards', 'identifyCards')
    .leftJoinAndSelect('identifyCards.documentType', 'documentType')
    .leftJoinAndSelect('guestLog.children', 'children')
    .leftJoinAndSelect('children.guest', 'childrenGuest')
    .leftJoinAndSelect('childrenGuest.identifyCards', 'childrenIdentifyCards')
    .leftJoinAndSelect(
      'childrenIdentifyCards.documentType',
      'childrenDocumentType',
    )
    .leftJoinAndSelect('children.status', 'childrenStatus')
    .where('guestLog.idVehicleLog = :id', { id })
    .getOne();

  if (!guestLog) {
    throw new Error(ERROR.GUEST_LOG_NOT_FOUND);
  }

  if (guestLog?.parentId) {
    guestLog = await guestLogs
      .createQueryBuilder('guestLog')
      .withDeleted()
      .leftJoinAndSelect('guestLog.guardPost', 'guardPost')
      .leftJoinAndSelect('guestLog.purposeCategory', 'purposeCategory')
      .leftJoinAndSelect('guestLog.status', 'status')
      .leftJoinAndSelect('guestLog.vehicleType', 'vehicleType')
      .leftJoinAndSelect('guestLog.guestType', 'guestType')
      .leftJoinAndSelect('guestLog.meetWho', 'meetWho')
      .leftJoinAndSelect('guestLog.meetOrganization', 'meetOrganization')
      .leftJoinAndSelect('guestLog.catLichTruc', 'catLichTruc')
      .leftJoinAndSelect('catLichTruc.main', 'main')
      .leftJoinAndSelect('catLichTruc.assistant', 'assistant')
      .leftJoinAndSelect('guestLog.guestBy', 'guestBy')
      .leftJoinAndSelect('guestLog.guest', 'guest')
      .leftJoinAndSelect('guest.identifyCards', 'identifyCards')
      .leftJoinAndSelect('identifyCards.documentType', 'documentType')
      .leftJoinAndSelect('guestLog.children', 'children')
      .leftJoinAndSelect('children.guest', 'childrenGuest')
      .leftJoinAndSelect('childrenGuest.identifyCards', 'childrenIdentifyCards')
      .leftJoinAndSelect(
        'childrenIdentifyCards.documentType',
        'childrenDocumentType',
      )
      .leftJoinAndSelect('children.status', 'childrenStatus')
      .where('guestLog.id = :id', { id: guestLog.parentId })
      .getOne();
  }

  if (guestLog) {
    guestLog.vehicleData = parseJson(guestLog?.vehicleData) || undefined;
    guestLog.children?.forEach((child) => {
      child.vehicleData = parseJson(child?.vehicleData) || undefined;
    });
  }

  return guestLog;
};

export const getGuestLog = async (id: number, user: User) => {
  const guestLogs = database.getRepository(GuestLog);

  const guestLog = await guestLogs
    .createQueryBuilder('guestLog')
    .withDeleted()
    .leftJoinAndSelect('guestLog.guardPost', 'guardPost')
    .leftJoinAndSelect('guestLog.purposeCategory', 'purposeCategory')
    .leftJoinAndSelect('guestLog.status', 'status')
    .leftJoinAndSelect('guestLog.vehicleType', 'vehicleType')
    .leftJoinAndSelect('guestLog.guestType', 'guestType')
    .leftJoinAndSelect('guestLog.meetWho', 'meetWho')
    .leftJoinAndSelect('guestLog.meetOrganization', 'meetOrganization')
    .leftJoinAndSelect('guestLog.catLichTruc', 'catLichTruc')
    .leftJoinAndSelect('catLichTruc.main', 'main')
    .leftJoinAndSelect('catLichTruc.assistant', 'assistant')
    .leftJoinAndSelect('guestLog.guestBy', 'guestBy')
    .leftJoinAndSelect('guestLog.guest', 'guest')
    .leftJoinAndSelect('guest.identifyCards', 'identifyCards')
    .leftJoinAndSelect('identifyCards.documentType', 'documentType')
    .leftJoinAndSelect('guestLog.children', 'children')
    .leftJoinAndSelect('children.guest', 'childrenGuest')
    .leftJoinAndSelect('childrenGuest.identifyCards', 'childrenIdentifyCards')
    .leftJoinAndSelect(
      'childrenIdentifyCards.documentType',
      'childrenDocumentType',
    )
    .leftJoinAndSelect('children.status', 'childrenStatus')
    .where('guestLog.id = :id', { id })
    .andWhere('guestLog.codeOrg = :codeOrg', { codeOrg: user.manageOrgCode })
    .getOne();

  if (!guestLog) {
    throw new Error(ERROR.GUEST_LOG_NOT_FOUND);
  }

  guestLog.vehicleData = parseJson(guestLog?.vehicleData) || undefined;

  guestLog.children?.forEach((child) => {
    child.vehicleData = parseJson(child?.vehicleData) || undefined;
  });

  return guestLog;
};

export const deleteGuestLogs = async (
  ids: number[],
  user: User,
  ip?: string,
) => {
  const repo = database.getRepository(GuestLog);
  const idArr = Array.isArray(ids) ? ids : [ids];
  const guestLogs = await repo.find({
    where: {
      id: In(idArr),
      codeOrg: user.manageOrgCode,
      statusId: WorkingStatus.ENTERED,
    },
    relations: ['children'],
    withDeleted: true,
  });

  if (guestLogs.length !== idArr.length || guestLogs.length === 0) {
    throw new Error(ERROR.GUEST_LOG_NOT_FOUND);
  }

  const childrenIds =
    guestLogs.flatMap(
      (item) => item?.children?.map((child) => child.id) || [],
    ) || [];

  await repo.delete([...(childrenIds as number[]), ...idArr]);

  await insertLog({
    typeId: LOG.DELETE,
    userId: user.id,
    content: 'Xóa các bản ghi của guest log: ' + ids?.join(', '),
    ip,
  });
  return { message: 'Deleted successfully' };
};

export const summary = async (
  query: ISearchGuestLogQuery & ISearchPendingGuestLogQuery,
  user: User,
) => {
  const guestLogRepo = database.getRepository(GuestLog);
  const pendingGuestRepo = database.getRepository(PendingGuest);
  const typeRepo = database.getRepository(Type);
  const catLichTrucRepo = database.getRepository(CatLichTruc);
  const catLichTruc = await catLichTrucRepo.findOne({
    where: {
      // date: Between(
      //   dayjs().startOf('day').toDate(),
      //   dayjs().endOf('day').toDate(),
      // ),
      // dutySchedule: {
      //   codeOrg: user.manageOrgCode,
      // },
    },
    withDeleted: true,
  });

  const guestType = await typeRepo.find({
    where: { scope: TYPE_SCOPES.GUEST_TYPES },
    withDeleted: true,
  });

  const qbPending = pendingGuestRepo
    .createQueryBuilder('pending_guest')
    .withDeleted();
  createPendingGuestSearchQuery(qbPending, query, user);
  const pendingGuest = await qbPending.getCount();

  const qbEnteredGuest = guestLogRepo
    .createQueryBuilder('guestLogs')
    .withDeleted();
  const enteredQuery = {
    ...query,
    statusIds: query?.statusIds || [
      WorkingStatus.ENTERED,
      WorkingStatus.GO_OUT,
    ],
    isInDuty: 'true',
  };
  createGuestLogSearchQuery(qbEnteredGuest, enteredQuery, user, catLichTruc);
  const enteredGuest = await qbEnteredGuest.getCount();

  const qbOutGuest = guestLogRepo.createQueryBuilder('guestLogs').withDeleted();
  const outQuery = {
    ...query,
    statusIds: [WorkingStatus.GO_OUT],
    isInDuty: 'true',
  };
  createGuestLogSearchQuery(qbOutGuest, outQuery, user, catLichTruc);
  const outGuest = await qbOutGuest.getCount();

  const guestTypesSummary = await Promise.all(
    guestType.map(async (type) => {
      const qb = guestLogRepo.createQueryBuilder('guestLogs').withDeleted();
      query.guestTypeIds = [type.id as number];
      query.isInDuty = 'true';
      createGuestLogSearchQuery(qb, query, user, catLichTruc);
      const count = await qb.getCount();
      return { ...type, count };
    }),
  );

  return {
    tabSummary: {
      pendingGuest,
      enteredGuest,
      outGuest,
    },
    guestTypesSummary,
  };
};

export const groupVehicle = async (query: IGuestLogGroup, user: User) => {
  const typeRepo = database.getRepository(Type);
  const guestLogRepo = database.getRepository(GuestLog);
  const pendingGuestRepo = database.getRepository(PendingGuest);

  const vehicleTypes = await typeRepo.find({
    where: { scope: TYPE_SCOPES.VEHICLE_TYPES },
    withDeleted: true,
  });

  let catLichTruc: CatLichTruc | null = null;
  if (query?.isInDuty == 'true') {
    const catLichTrucRepo = database.getRepository(CatLichTruc);
    catLichTruc = await catLichTrucRepo.findOne({
      where: {
        // date: Between(
        //   dayjs().startOf('day').toDate(),
        //   dayjs().endOf('day').toDate(),
        // ),
        // dutySchedule: {
        //   codeOrg: user.manageOrgCode,
        // },
      },
      withDeleted: true,
    });
  }

  const vehicleTypeSummary = await Promise.all(
    vehicleTypes?.map(async (type) => {
      let count = 0;
      if (query.isPendingGuest != 'true') {
        const qb = guestLogRepo
          .createQueryBuilder('guestLogs')
          .withDeleted()
          .leftJoinAndSelect('guestLogs.guest', 'guest')
          .leftJoinAndSelect('guest.identifyCards', 'identifyCards');
        query.vehicleTypeIds = [type.id as number];
        createGuestLogSearchQuery(qb, query, user, catLichTruc);
        count = await qb.getCount();
      } else {
        const qb = pendingGuestRepo.createQueryBuilder('pending_guest');
        query.vehicleTypeIds = [type.id as number];
        createPendingGuestSearchQuery(qb, query, user);
        count = await qb.getCount();
      }
      return { name: type.name, count, id: type.id };
    }),
  );

  return vehicleTypeSummary?.filter((item) => item.count > 0);
};

export const groupByCheckInTime = async (
  query: { isPendingGuest: string } & ISearchQuery<ISearchGuestLogQuery> &
    ISearchQuery<ISearchPendingGuestLogQuery>,
  user: User,
) => {
  const guestLogRepo = database.getRepository(GuestLog);
  const pendingGuestRepo = database.getRepository(PendingGuest);

  let catLichTruc: CatLichTruc | null = null;
  if (query?.isInDuty == 'true') {
    const catLichTrucRepo = database.getRepository(CatLichTruc);
    catLichTruc = await catLichTrucRepo.findOne({
      where: {
        // date: Between(
        //   dayjs().startOf('day').toDate(),
        //   dayjs().endOf('day').toDate(),
        // ),
        // dutySchedule: {
        //   codeOrg: user.manageOrgCode,
        // },
      },
      withDeleted: true,
    });
  }

  const times = Array.from({ length: 24 }, (_, i) => {
    const start = `${String(i).padStart(2, '0')}:00:00`;
    const end =
      i === 23
        ? `${String(i).padStart(2, '0')}:59:59`
        : `${String(i + 1).padStart(2, '0')}:00:00`;
    return {
      title: `${String(i).padStart(2, '0')}:00 - ${String(i + 1).padStart(2, '0')}:00`,
      start,
      end,
    };
  });

  const summary = await Promise.all(
    times?.map(async (time) => {
      let count = 0;

      if (query.isPendingGuest != 'true') {
        const qb = guestLogRepo
          .createQueryBuilder('guestLogs')
          .withDeleted()
          .leftJoinAndSelect('guestLogs.guest', 'guest')
          .leftJoinAndSelect('guest.identifyCards', 'identifyCards');
        createGuestLogSearchQuery(qb, query, user, catLichTruc);
        qb.andWhere(
          'CAST(guestLogs.check_in_time as TIME) BETWEEN :start AND :end',
          {
            start: time.start,
            end: time.end,
          },
        );
        count = await qb.getCount();
      } else {
        const qb = pendingGuestRepo
          .createQueryBuilder('pending_guest')
          .withDeleted();
        createPendingGuestSearchQuery(qb, query, user);
        qb.andWhere(
          'CAST(pending_guest.createdAt as TIME) BETWEEN :start AND :end',
          {
            start: time.start,
            end: time.end,
          },
        );
        count = await qb.getCount();
      }

      return { ...time, count };
    }),
  );

  return summary?.filter((item) => item.count > 0);
};

export const groupByOrg = async (query: IGuestLogGroup, user: User) => {
  const guestLogRepo = database.getRepository(GuestLog);
  const pendingGuestRepo = database.getRepository(PendingGuest);
  const organizationRepo = database.getRepository(Organization);

  let catLichTruc: CatLichTruc | null = null;
  if (query?.isInDuty == 'true') {
    const catLichTrucRepo = database.getRepository(CatLichTruc);
    catLichTruc = await catLichTrucRepo.findOne({
      where: {
        // date: Between(
        //   dayjs().startOf('day').toDate(),
        //   dayjs().endOf('day').toDate(),
        // ),
        // dutySchedule: {
        //   codeOrg: user.manageOrgCode,
        // },
      },
      withDeleted: true,
    });
  }

  const codePrefixIndex = user?.manageOrgCode?.indexOf('00.');
  const codePrefix = user?.manageOrgCode?.slice(0, codePrefixIndex);

  const organizations = await organizationRepo.find({
    where: { code: Like(`${codePrefix}%`) },
    withDeleted: true,
  });

  const res = await Promise.all(
    organizations?.map(async (organization) => {
      let count = 0;
      if (query.isPendingGuest != 'true') {
        const qb = guestLogRepo
          .createQueryBuilder('guestLogs')
          .withDeleted()
          .leftJoinAndSelect('guestLogs.guest', 'guest')
          .leftJoinAndSelect('guest.identifyCards', 'identifyCards');
        query.meetOrgCode = organization?.code as string;
        createGuestLogSearchQuery(qb, query, user, catLichTruc);
        count = await qb.getCount();
      } else {
        const qb = pendingGuestRepo.createQueryBuilder('pending_guest');
        query.meetOrgCode = organization?.code as string;
        createPendingGuestSearchQuery(qb, query, user);
        count = await qb.getCount();
      }

      return { name: organization.name, code: organization.code, count };
    }),
  );

  return res?.filter((item) => item.count > 0);
};

export const getGuestLogStatsByOrgCode = async (
  query: IGuestLogStatsByOrgCodeQuery,
  user: User,
) => {
  const startDate = query.startDate ? new Date(query.startDate) : new Date();
  const endDate = query.endDate ? new Date(query.endDate) : new Date();

  const guestLogRepo = database.getRepository(GuestLog);

  const allOrgWithChildren = await getAllChildOrganizations(
    query.orgCode,
    user,
  );

  if (!allOrgWithChildren || allOrgWithChildren.length === 0) {
    return [];
  }

  const results: IGuestLogStatsByOrgCodeResult[] = await Promise.all(
    allOrgWithChildren.map(async (org) => {
      const baseQuery = guestLogRepo
        .createQueryBuilder('guestLogs')
        .withDeleted()
        .where(`guestLogs.meet_org_code = :orgCode`, { orgCode: org.code })
        .andWhere(
          '((guestLogs.check_in_time BETWEEN :startDate AND :endDate) OR ' +
            '(guestLogs.check_out_time BETWEEN :startDate AND :endDate))',
          {
            startDate: dayjs(startDate).startOf('date').toDate(),
            endDate: dayjs(endDate).endOf('date').toDate(),
          },
        );

      const totalGuests = await baseQuery.getCount();

      const guestsOutQuery = guestLogRepo
        .createQueryBuilder('guestLogs')
        .where(`guestLogs.meet_org_code = :orgCode`, { orgCode: org.code })
        .andWhere('guestLogs.status_id = :statusId', {
          statusId: WorkingStatus.GO_OUT,
        })
        .andWhere(
          '((guestLogs.check_in_time BETWEEN :startDate AND :endDate) OR ' +
            '(guestLogs.check_out_time BETWEEN :startDate AND :endDate))',
          {
            startDate: dayjs(startDate).startOf('date').toDate(),
            endDate: dayjs(endDate).endOf('date').toDate(),
          },
        );

      const guestsOut = await guestsOutQuery.getCount();
      const guestsRemaining = totalGuests - guestsOut;

      return {
        orgId: org.id,
        orgCode: org.code,
        orgName: org.name,
        totalGuests,
        guestsOut,
        guestsRemaining,
      };
    }),
  );

  return results.filter((result) => Number(result?.totalGuests) > 0);
};

export const getGuestLogHistoryInOut = async (
  query: IGuestLogHistoryInOutQuery,
  user: User,
) => {
  const guestLogRepo = database.getRepository(GuestLog);

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  const startDate = query.startDate ? new Date(query.startDate) : new Date();
  const endDate = query.endDate ? new Date(query.endDate) : new Date();

  const organizations = await getAllChildOrganizations(query.orgCode, user);

  if (!organizations || organizations.length === 0) {
    return {
      data: [],
      meta: {
        totalItems: 0,
        itemsPerPage: limit,
        totalPages: 0,
        currentPage: page,
      },
      links: createLinks('/guest-logs/history-in-out?', 0, page, limit),
    };
  }

  const orgCodes = organizations.map((org) => `'${org.code}'`).join(',');

  const queryBuilder = guestLogRepo
    .createQueryBuilder('guestLogs')
    .withDeleted()
    .leftJoinAndSelect('guestLogs.organization', 'organization')
    .leftJoinAndSelect('guestLogs.meetOrganization', 'meetOrganization')
    .leftJoinAndSelect('guestLogs.guestType', 'guestType')
    .leftJoinAndSelect('guestLogs.guest', 'guest')
    .leftJoinAndSelect('guest.identifyCards', 'identifyCards')
    .leftJoinAndSelect('identifyCards.documentType', 'documentType')
    .leftJoinAndSelect('guestLogs.status', 'status')
    .leftJoinAndSelect('guestLogs.vehicleType', 'vehicleType')
    .leftJoinAndSelect('guestLogs.meetWho', 'meetWho')
    .leftJoinAndSelect('guestLogs.purposeCategory', 'purposeCategory')
    .where(`guestLogs.meetOrgCode IN (${orgCodes})`)
    .andWhere(
      '((guestLogs.checkInTime BETWEEN :startDate AND :endDate) OR ' +
        '(guestLogs.checkOutTime BETWEEN :startDate AND :endDate))',
      {
        startDate: dayjs(startDate).startOf('date').toDate(),
        endDate: dayjs(endDate).endOf('date').toDate(),
      },
    )
    .orderBy('guestLogs.checkOutTime', 'DESC')
    .addOrderBy('guestLogs.checkInTime', 'DESC')
    .skip(skip)
    .take(limit);

  const [data, totalItems] = await queryBuilder.getManyAndCount();

  data.forEach((item) => {
    item.vehicleData = item.vehicleData ? JSON.parse(item.vehicleData) : {};
  });

  const totalPages = Math.ceil(totalItems / limit);

  return {
    meta: {
      totalItems,
      itemsPerPage: limit,
      totalPages,
      currentPage: page,
      items: data,
    },
    links: createLinks('/guest-logs/history-in-out?', totalPages, page, limit),
  };
};
interface VehicleData {
  licensePlate: string;
}
export const exportDocxGuestLogs = async (
  query: ISearchQuery<ISearchGuestLogQuery>,
  user: User,
) => {
  const guestLogs = database.getRepository(GuestLog);

  const qb = guestLogs
    .createQueryBuilder('guestLogs')
    .withDeleted()
    .leftJoinAndSelect('guestLogs.guest', 'guest')
    .leftJoinAndSelect('guestLogs.guardPost', 'guardPost')
    .leftJoinAndSelect('guestLogs.purposeCategory', 'purposeCategory')
    .leftJoinAndSelect('guestLogs.status', 'status')
    .leftJoinAndSelect('guestLogs.vehicleType', 'vehicleType')
    .leftJoinAndSelect('guestLogs.guestType', 'guestType')
    .leftJoinAndSelect('guestLogs.meetWho', 'meetWho')
    .leftJoinAndSelect('guestLogs.meetOrganization', 'meetOrganization')
    .leftJoinAndSelect('guestLogs.catLichTruc', 'catLichTruc')
    .leftJoinAndSelect('guestLogs.guestBy', 'guestBy')
    .leftJoinAndSelect('guest.identifyCards', 'identifyCards')
    .leftJoinAndSelect('identifyCards.documentType', 'documentType');

  let catLichTruc: CatLichTruc | null = null;
  if (query?.isInDuty == 'true') {
    const catLichTrucRepo = database.getRepository(CatLichTruc);
    catLichTruc = await catLichTrucRepo.findOne({
      where: {
        // date: Between(
        //   dayjs().startOf('day').toDate(),
        //   dayjs().endOf('day').toDate(),
        // ),
        // dutySchedule: {
        //   codeOrg: user.manageOrgCode,
        // },
      },
      withDeleted: true,
    });
  }

  createGuestLogSearchQuery(qb, query, user, catLichTruc);

  qb.orderBy('guestLogs.createdAt', 'ASC');

  const [data] = await qb.getManyAndCount();
  const dataExport = data.map((item, index) => {
    const dataVehicle = item.vehicleData
      ? (JSON.parse(item.vehicleData) as VehicleData)
      : { licensePlate: '' };
    return {
      stt: index + 1,
      tenKhach: item.guest?.fullName,
      coQuan: item.guestOrganizationName ?? '',
      cccd: item.guest?.identifyCards?.[0]?.identificationNumber ?? '',
      thoiGianVao: item.checkInTime
        ? dayjs(item.checkInTime).format('HH:mm DD-MM-YYYY')
        : '',
      thoiGianRa: item.checkOutTime
        ? dayjs(item.checkOutTime).format('HH:mm DD-MM-YYYY')
        : '',
      nguoiLienHe: item.meetWho?.fullName ?? '',
      donViLienHe: item.meetOrganization?.name ?? '',
      mucDichLamViec: item.purposeCategory?.name ?? '',
      bienSoXe: dataVehicle ? dataVehicle.licensePlate : '',
      phuongTien: item.vehicleType?.name ? item.vehicleType?.name : '',
      phanLoai: item.guestType?.name ?? '',
    };
  });
  const pathOutput = await exportDoc(
    `trich_xuat_khach.docx`,
    `trich_xuat_khach_${Date.now()}.docx`,
    { data: dataExport },
  );
  return pathOutput;
};
