import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ShiftHandover } from '../models';
import { ISearchQuery } from '../types/req';
import { ERROR } from '../utils/error';
import {
  ICreateShiftHandover,
  ISearchShiftHandoverQuery,
} from './shift-handover';
import * as shiftHandoverService from './shift-handover.service';

export const createShiftHandover = async (
  req: Request & { body: ICreateShiftHandover },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await shiftHandoverService.create(req.body);
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateShiftHandover = async (
  req: Request & { body: ShiftHandover } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await shiftHandoverService.update(
      Number(req.params.id),
      req.body,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
        return res.status(status.BAD_REQUEST).json({
          code: ERROR.DATA_NOT_FOUND,
          message: 'Ca gác không tồn tại',
          statusCode: 400,
          errors: {
            field: 'id',
            message: 'Id không tồn tại',
          },
        });
      } else
        return res.status(status.BAD_REQUEST).json({
          part: 'body',
          code: ERROR.BAD_REQUEST,
          message: e.message.toString(),
          statusCode: 400,
        });
    }
    next(e);
  }
};

export const deleteShiftHandover = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await shiftHandoverService.remove(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getShiftHandover = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await shiftHandoverService.findById(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const searchShiftHandovers = async (
  req: Request & { query: ISearchQuery<ISearchShiftHandoverQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await shiftHandoverService.search(req.query);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
