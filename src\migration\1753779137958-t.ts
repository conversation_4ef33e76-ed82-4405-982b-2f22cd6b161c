import { MigrationInterface, QueryRunner } from "typeorm";

export class T1753779137958 implements MigrationInterface {
    name = 'T1753779137958'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "position_categories" DROP CONSTRAINT "UQ_be2519c18fa146b184225dd18cb"`);
        await queryRunner.query(`ALTER TABLE "position_categories" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "position_categories" ADD "code" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "position_categories" ADD CONSTRAINT "UQ_be2519c18fa146b184225dd18cb" UNIQUE ("code")`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "position_categories" DROP CONSTRAINT "UQ_be2519c18fa146b184225dd18cb"`);
        await queryRunner.query(`ALTER TABLE "position_categories" DROP COLUMN "code"`);
        await queryRunner.query(`ALTER TABLE "position_categories" ADD "code" varchar(36)`);
        await queryRunner.query(`ALTER TABLE "position_categories" ADD CONSTRAINT "UQ_be2519c18fa146b184225dd18cb" UNIQUE ("code")`);
    }

}
