import _ from 'lodash';
import { ITreeNode } from 'src/types/tree';

const getCountLeaf = (node: ITreeNode, countleaf: ITreeNode[]) => {
  if (node.children && node.children.length && node.children.length > 0) {
    for (let k = 0; k < node.children.length; k++) {
      const isLeaf = Boolean(node.children[k].hasChildren);

      if (isLeaf === false) {
        countleaf.push(node.children[k]);
      } else {
        getCountLeaf(node.children[k], countleaf);
      }
    }
    return countleaf;
  }
};
export const sortASC = (node: ITreeNode[]) => {
  if (node.length > 0) {
    // for (let i = 0; i < node.length; i++) {
    //   if (node[i] && node[i].children) {
    //     node[i].children.sort((one, two) =>
    //       one.orderBy && two.orderBy && one.orderBy > two.orderBy ? 1 : -1,
    //     );
    //   }
    //   if (node[i] && node[i].children) sortASC(node[i].children);
    // }
  }
};
export const getTree = (data: ITreeNode[], skip: number, limit: number) => {
  if (!data || data.length == 0) return [];
  const count = _.countBy(data, (o) => o.hasChildren === false).true;
  const treeAll: ITreeNode[] = [];
  const tree: ITreeNode[] = [];
  const rootIds: number[] = [];
  let item = data[0];
  let primaryKey = Number(item.id);
  const treeObjs = [];
  const tempChildren = [];
  let parentId = 0;
  let parent: ITreeNode = {
    id: 0,
    name: '',
    hasChildren: false,
    level: 0,
    children: [],
    orderBy: 1,
  };
  const len = data.length;
  let i = 0;
  const countleaf: ITreeNode[] = [];
  let countUseLeaf = 0;

  while (i < len) {
    item = data[i++];
    primaryKey = Number(item.id);

    if (tempChildren[primaryKey]) {
      item.children = tempChildren[primaryKey];
      delete tempChildren[primaryKey];
    }

    treeObjs[primaryKey] = item;
    parentId = Number(item.parentId);

    if (parentId) {
      parent = treeObjs[parentId];

      if (!parent) {
        const siblings = tempChildren[parentId];
        if (siblings) {
          siblings.push(item);
        } else {
          tempChildren[parentId] = [item];
        }
      } else if (parent.children) {
        parent.children.push(item);
      } else {
        parent.children = [item];
      }
    } else {
      rootIds.push(primaryKey);
    }
  }
  for (let i = 0; i < rootIds.length; i++) {
    treeAll.push(treeObjs[rootIds[i]]);
  }
  treeAll.sort((one, two) => ((one.name ?? '') > (two.name ?? '') ? 1 : -1));

  if (skip >= treeAll.length) skip = treeAll.length;
  for (let i = 0; i < skip; i++) {
    const leafs = getCountLeaf(treeAll[i], []);
    countUseLeaf = countUseLeaf + Number(leafs?.length);
  }

  for (let i = skip; i < treeAll.length; i++) {
    getCountLeaf(treeAll[i], countleaf);
    tree.push(treeAll[i]);
    if (countleaf.length >= limit) {
      break;
    }
  }

  const treeResult = {
    node: tree,
    length: count,
    countCurrentLeaf: countleaf.length,
    countRemainLeaf: count - countUseLeaf - countleaf.length,
    countUseLeaf: countUseLeaf === 0 ? countleaf.length : countUseLeaf,
  };
  return treeResult;
};
export const getTreeNoLimitNoSkip = (data: ITreeNode[]) => {
  console.log('dataa:', data);
  if (!data || data.length == 0) return [];
  const treeAll: ITreeNode[] = [];
  const rootIds: number[] = [];
  let item = data[0];
  let primaryKey = Number(item.id);
  const treeObjs = [];
  const tempChildren = [];
  let parentId = 0;
  let parent: ITreeNode = {
    id: 0,
    name: '',
    hasChildren: false,
    level: 0,
    children: [],
    orderBy: 1,
  };
  const len = data.length;
  let i = 0;

  while (i < len) {
    item = data[i++];
    primaryKey = Number(item.id);

    if (tempChildren[primaryKey]) {
      item.children = tempChildren[primaryKey];
      delete tempChildren[primaryKey];
    }

    treeObjs[primaryKey] = item;
    parentId = Number(item.parentId);

    if (parentId) {
      parent = treeObjs[parentId];

      if (!parent) {
        const siblings = tempChildren[parentId];
        if (siblings) {
          siblings.push(item);
        } else {
          tempChildren[parentId] = [item];
        }
      } else if (parent.children) {
        parent.children.push(item);
      } else {
        parent.children = [item];
      }
    } else {
      rootIds.push(primaryKey);
    }
  }
  for (let i = 0; i < rootIds.length; i++) {
    treeAll.push(treeObjs[rootIds[i]]);
  }
  treeAll.sort((one, two) => ((one.name ?? '') > (two.name ?? '') ? 1 : -1));

  return treeAll;
};
export const treePagination = (
  data: ITreeNode[],
  page: number,
  limit: number,
) => {
  const from = (page - 1) * limit;
  let to = from + limit;

  if (!data || data.length == 0) return [];
  const treeAll: ITreeNode[] = [];
  const tree: ITreeNode[] = [];
  const rootIds: number[] = [];
  let item = data[0];
  let primaryKey = Number(item.id);
  const treeObjs = [];
  const tempChildren = [];
  let parentId = 0;
  let parent: ITreeNode = {
    id: 0,
    name: '',
    hasChildren: false,
    level: 0,
    children: [],
    orderBy: 1,
  };
  const len = data.length;
  let i = 0;

  while (i < len) {
    item = data[i++];
    primaryKey = Number(item.id);

    if (tempChildren[primaryKey]) {
      item.children = tempChildren[primaryKey];
      delete tempChildren[primaryKey];
    }

    treeObjs[primaryKey] = item;
    parentId = Number(item.parentId);

    if (parentId) {
      parent = treeObjs[parentId];

      if (!parent) {
        const siblings = tempChildren[parentId];
        if (siblings) {
          siblings.push(item);
        } else {
          tempChildren[parentId] = [item];
        }
      } else if (parent.children) {
        parent.children.push(item);
      } else {
        parent.children = [item];
      }
    } else {
      rootIds.push(primaryKey);
    }
  }
  for (let i = 0; i < rootIds.length; i++) {
    treeAll.push(treeObjs[rootIds[i]]);
  }
  treeAll.sort((one, two) => ((one.name ?? '') > (two.name ?? '') ? 1 : -1));

  if (to >= treeAll.length) to = treeAll.length;

  for (let i = from; i < to; i++) {
    tree.push(treeAll[i]);
  }

  const treeResult = { tree, length: treeAll.length };
  return treeResult;
};
