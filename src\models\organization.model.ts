import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  Tree,
  TreeChildren,
  TreeParent,
} from 'typeorm';

import EntityModel from './entity.model';
import { User } from './user.model';
import { eQN } from './eQN.model';
import { OrganizationGroup } from './organizationGroup.model';
import { DonviLoaiHinhTruc } from './donViLoaiHinhTruc.model';

@Entity('organizations')
@Tree('materialized-path')
export class Organization extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column({ type: 'nvarchar', length: 17, unique: true })
  code?: string;

  @Column({ type: 'nvarchar', length: 100, nullable: false })
  name?: string;

  @Column({ type: 'nvarchar', length: 200, nullable: true })
  desc?: string;

  @Column({ type: 'bit', nullable: false, default: 1 })
  isEnable?: boolean;

  @Column({ type: 'bit', nullable: false, default: 0 })
  hasChild?: boolean;

  @Column({ type: 'nvarchar', length: 50, nullable: false })
  shortName?: string;

  @Column({ type: 'smallint', default: 0, nullable: true })
  orderNumber?: number;

  @Column({ type: 'tinyint', nullable: true })
  depth?: number;

  @Column({ type: 'nvarchar', length: 1000, nullable: true })
  path?: string;

  @TreeChildren()
  children?: Organization[];

  @TreeParent()
  parent?: Organization;

  @Column({ nullable: true })
  public parentId?: string;

  @OneToMany(() => User, (user) => user.organization)
  users?: User[];

  @OneToMany(() => eQN, (enq) => enq.organization)
  @JoinColumn({ name: 'code', foreignKeyConstraintName: 'orgCode' })
  eQns?: eQN[];

  // @Column({ nullable: true, type: 'nvarchar', length: 4000, name: 'mpath' })
  mpath?: string;

  @Column({
    nullable: true,
    type: 'nvarchar',
    length: 4000,
    name: 'mpathFullName',
  })
  mPathFullName?: string;

  @Column({
    type: 'varchar',
    length: 36,
    nullable: true,
  })
  orgGroupId?: string;
  @ManyToOne(() => OrganizationGroup)
  @JoinColumn({ name: 'orgGroupId' })
  organizationGroup?: OrganizationGroup;

  @OneToMany(
    () => DonviLoaiHinhTruc,
    (donviLoaiHinhTruc) => donviLoaiHinhTruc.organization,
  )
  donviLoaiHinhTrucs?: DonviLoaiHinhTruc[];
}
