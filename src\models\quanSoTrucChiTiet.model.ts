import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { QuanSoTruc } from './quanSoTruc.model';

@Entity()
export class QuanSoTrucChiTiet extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column()
  soLuong?: number;

  @Column()
  ngay?: number;

  @Column({ default: false })
  isTangCuong?: boolean;

  @Column({
    type: 'uuid',
  })
  idQuanSoTruc?: string;
  @ManyToOne(() => QuanSoTruc)
  @JoinColumn({ name: 'idQuanSoTruc' })
  quanSoTruc?: QuanSoTruc;
}
