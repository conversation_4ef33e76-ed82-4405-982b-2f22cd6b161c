import express from 'express';
import { createValidator } from '../utils/validator';
import { getTypes, putType, getTypeById, postType } from './types.controller';
import {
  eBodySearchTypes,
  ePostBody,
  jPostBody,
  eBodyPutTypes,
  eIdInParams,
  jBodyPutTypes,
  jBodySearchTypes,
  jIdInParams,
} from './types.validation';

const routerType = express.Router();

routerType.get(
  '/',
  createValidator(
    eBodySearchTypes.part ? eBodySearchTypes.part : 'query',
    jBodySearchTypes,
    eBodySearchTypes,
  ),
  getTypes,
);

routerType.put(
  '/:id',
  createValidator(
    eBodyPutTypes.part ? eBodyPutTypes.part : 'body',
    jBodyPutTypes,
    eBodyPutTypes,
  ),
  putType,
);

routerType.get(
  '/:id',
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  getTypeById,
);

routerType.post(
  '/',
  createValidator(
    ePostBody.part ? ePostBody.part : 'body',
    jPostBody,
    ePostBody,
  ),
  postType,
);
export default routerType;
