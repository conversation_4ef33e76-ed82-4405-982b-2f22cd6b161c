import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Type } from './type.model';
import { DMLoaiHinhTruc } from './dmLoaiHinhTruc.model';
import { eQN } from './eQN.model';

@Entity()
export class CauHinhEqnLoaiHinhTruc extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column({
    type: 'varchar',
    length: 36,
  })
  eqn?: string;
  @ManyToOne(() => eQN)
  @JoinColumn({ name: 'eqn' })
  eQN?: eQN;

  @Column({
    type: 'varchar',
    length: 36,
  })
  maLoaiHinhTruc?: string;
  @ManyToOne(() => DMLoaiHinhTruc)
  @JoinColumn({ name: 'maLoaiHinhTruc', referencedColumnName: 'ma' })
  loaiHinhTruc?: DMLoaiHinhTruc;

  @Column()
  value?: string;

  @Column({
    type: 'int',
    nullable: true,
  })
  idLyDo?: number;

  @ManyToOne(() => Type)
  @JoinColumn({ name: 'idLyDo' })
  lyDo?: Type;

  @BeforeInsert()
  @BeforeUpdate()
  updateFullText() {
    this.fullText = [this.eqn, this.maLoaiHinhTruc, this.value]
      .filter(Boolean)
      .join(' ')
      .trim();
  }
}
