import {
  Before<PERSON>nse<PERSON>,
  Column,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { GuardPost } from './guardPost.model';
import { Type } from './type.model';
import { Organization } from './organization.model';
import { parseJson } from '../utils/json';

export type VehicleDataType = {
  licensePlate: string;
  commodity: string;
};

@Entity('pending_guest')
export class PendingGuest extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'id_vehicle_log',
    type: 'nvarchar',
    length: 50,
    nullable: true,
    default: null,
  })
  idVehicleLog?: string;

  @Column({
    name: 'vehicle_data',
    type: 'nvarchar',
    nullable: true,
    default: null,
    length: 2000,
  })
  vehicleData?: string;

  @Column({
    name: 'default_image',
    type: 'text',
    nullable: true,
    default: null,
  })
  defaultImage?: string; // hình ảnh mặc định

  @Column({
    name: 'vehicle_type_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  vehicleTypeId?: number; // loại xe
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'vehicle_type_id' })
  vehicleType?: Type;

  @Column({
    name: 'vehicle_classification_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  vehicleClassificationId?: number; // Phân loại xe
  @ManyToOne(() => Type)
  @JoinColumn({ name: 'vehicle_classification_id' })
  vehicleClassification?: Type;

  @Column({
    name: 'etiquette',
    type: 'nvarchar',
    length: 200,
    nullable: true,
    default: null,
  })
  etiquette?: string; // lễ tiết tác phong

  @Column({
    name: 'guard_post_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  guardPostId?: number; // vị trí (Chòi gác nơi xe vào) guardPostId
  @ManyToOne(() => GuardPost)
  @JoinColumn({ name: 'guard_post_id' })
  guardPost?: GuardPost;

  // thông tin thẻ của khách
  @Column({
    name: 'guest_card_id',
    type: 'nvarchar',
    length: 50,
    nullable: true,
    default: null,
  })
  guestCardId?: string;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    nullable: true,
    default: null,
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization; // Thông tin đơn vị

  @BeforeInsert()
  beforeCreate() {
    const vehicleData = parseJson<VehicleDataType>(this.vehicleData);
    const cols = [
      this.vehicleType?.name,
      this.vehicleClassification?.name,
      this.etiquette,
      vehicleData?.licensePlate || '',
      vehicleData?.commodity || '',
      this.guestCardId,
    ];
    this.fullText = cols.join(' ')?.trim();
  }
}
