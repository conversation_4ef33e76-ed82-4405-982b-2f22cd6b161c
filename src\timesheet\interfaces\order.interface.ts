import { eQN } from 'src/models';

export interface IType {
  id: string;
  date_created: string;
  date_updated: string;
  name: string;
  short_name: string;
  is_enable: boolean;
  order_number: number;
}

export interface IOrganization {
  id: string;
  date_created: string;
  date_updated: string;
  name: string;
  short_name: string;
  code: string | null;
  ward_id: string;
  attribute: any;
  is_enable: boolean;
  parent_id: string | null;
  tree_level: number;
  order_number: number | null;
  tree_path: string;
}

export interface IPersonalIdentify {
  id: string;
  date_created: string;
  date_updated: string;
  full_name: string;
  short_name: string | null;
  other_name: string | null;
  cccd: string | null;
  e_qn: string;
  gender: boolean;
  birthday: string | null;
  phone_number: string;
  identification: string | null;
  image: string | null;
  is_enable: boolean;
  type_id: IType;
  org_id: IOrganization;
}

export interface IOrder {
  id: string;
  date_created: string;
  date_updated: string;
  order_type_id: string;
  phone_number: string;
  address_from: string;
  ward_id_from: string;
  address_to: string;
  ward_id_to: string;
  distance: string;
  transport_id: string;
  date_of_depart: string;
  date_of_return: string;
  date_of_presence: string;
  date_of_confirm: string | null;
  num_of_days: number;
  days_on_road: string;
  reason: string;
  is_enable: boolean;
  created_by: string;
  status: string;
  rejected_by: string | null;
  rejection_reason: string | null;
  approved_by: string | null;
  assign_approve_role: string | null;
  personal_identify_id: IPersonalIdentify;
  approved_org: IOrganization | null;
  personal_identify_org: IOrganization | null;
  created_org: IOrganization | null;
  rejected_org: IOrganization | null;
}

export interface IOrderResponse {
  success: boolean;
  data: IOrder[];
}

export interface IWorkingSchedule {
  season: 'summer' | 'winter';
  startTime: string; // format "HH:mm"
  endTime: string; // format "HH:mm"
  effectiveFrom: string; // "MM-DD"
  effectiveTo: string; // "MM-DD"
  yearSpan?: boolean; // có vắt năm không
}

export interface IWorkingScheduleConfig {
  orgCode: string;
  workingScheduleConfig: IWorkingSchedule[];
}

// New interfaces for enhanced group orders
export interface IGroupOptions {
  startDate?: string | Date;
  endDate?: string | Date;
  org_id?: string;
}

export interface IDateStats {
  date: string;
  total: number;
  statusCounts: Record<string, number>;
}

export interface IFormattedOrgStats {
  organization: {
    id: string;
    name: string;
  };
  totalOrders: number;
  statusSummary: Record<string, number>;
  dailyStats: Array<{
    date: string;
    total: number;
    statuses: Record<string, number>;
  }>;
}

export interface IDashboardByOrgStats {
  organization: {
    id: string;
    name: string;
  };
  totalCount: number;
  dungGioCount: number;
  khongChamCount: number;
  diMuonCount: number;
  vangCoLyDoCount: number;
  nghiPhepCount: number;
}

export interface ITimeKeepingDetail {
  id?: string;
  check_in_time?: string;
  name?: string;
  rank?: string;
  position?: string;
  location?: string;
  method?: string;
  unit?: string;
  time?: string;
  late?: string;
  reason?: string;
  status?: string;
  date?: string;
  eqnId?: string;
  check_type_id?: number;
  checkInType?: string;
  biometricData?: string;
  attendanceImage?: string;
}

export interface IDashboard {
  totalCount: number;
  dungGioCount: number;
  khongChamCount: number;
  diMuonCount: number;
  vangCoLyDoCount: number;
  nghiPhepCount: number;
  dashboardByOrgStats: IDashboardByOrgStats[];
  // attendanceLog: Array<ITimeKeepingDetail>;
}

export interface IEQNTimeSheetDetail {
  totalCount: number;
  dungGioCount: number;
  khongChamCount: number;
  diMuonCount: number;
  vangCoLyDoCount: number;
  nghiPhepCount: number;
  eQN: eQN;
  attendanceLog: Array<ITimeKeepingDetail>;
}
