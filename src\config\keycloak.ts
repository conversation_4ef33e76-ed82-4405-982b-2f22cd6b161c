export const K<PERSON><PERSON><PERSON><PERSON><PERSON>_CONFIG = {
  KEYCLOAK_URL: process.env.KEYCLOAK_URL || 'http://localhost:8080/',
  KEYCLOAK_REALM: process.env.KEYCLOAK_REALM || 'tttm',
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CLIENT_ID: process.env.<PERSON><PERSON><PERSON><PERSON><PERSON>K_CLIENT_ID || 'tttm',
  <PERSON><PERSON><PERSON><PERSON>OA<PERSON>_CLIENT_SECRET:
    process.env.KEYCLOAK_CLIENT_SECRET || 'your-client-secret',
} as const;

// Keycloak endpoints
export const KEYCLOAK_ENDPOINTS = {
  TOKEN: `${KEYCLOAK_CONFIG.KEYCLOAK_URL}realms/${KEYCLOAK_CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/token`,
  USERINFO: `${KEYCLOAK_CONFIG.KEYCLOAK_URL}realms/${KEYCL<PERSON>K_CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/userinfo`,
  LOGOUT: `${<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_CONFIG.KEYCLOAK_URL}realms/${<PERSON><PERSON><PERSON><PERSON><PERSON>K_CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/logout`,
  CERTS: `${KEYCLOAK_CONFIG.KEYCLOAK_URL}realms/${KEYCLOAK_CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/certs`,
  AUTH: `${KEYCLOAK_CONFIG.KEYCLOAK_URL}realms/${KEYCLOAK_CONFIG.KEYCLOAK_REALM}/protocol/openid-connect/auth`,
} as const;
