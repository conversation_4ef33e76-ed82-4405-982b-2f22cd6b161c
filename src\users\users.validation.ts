import Joi from 'joi';
import { IApiError } from '../types/validation';

export const jIdInParams = Joi.object({
  id: Joi.number().required(),
});
export const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng Id (Path) chưa chính xác',
  errors: { id: 'id Định dạng chọn chính xác' },
  statusCode: 400,
};

export const jBodyPostUser = Joi.object({
  username: Joi.string().min(1).max(300).required(),
  password: Joi.string().min(6).max(300).required(),
  name: Joi.string().min(1).max(300).required(),
  organizationId: Joi.string().required(),
  roleId: Joi.number().required(),
  contact: Joi.string().min(1).max(300),
  notes: Joi.string().min(1).max(300),
  positionId: Joi.number().allow(null),
  rankId: Joi.number().allow(null),
  image: Joi.string().min(1).max(300),
  status: Joi.boolean().required(),
  manageOrgCode: Joi.string(),
});
export const jBodyPuttUser = Joi.object({
  username: Joi.string().min(1).max(300),
  name: Joi.string().min(1).max(300),
  organizationId: Joi.string().allow(null),
  positionId: Joi.number().allow(null),
  rankId: Joi.number().allow(null),
  roleId: Joi.number().allow(null),
  contact: Joi.string().min(1).max(300).allow(null),
  notes: Joi.string().min(1).max(300).allow(null),
  image: Joi.string().min(1).max(300).allow(null),
  status: Joi.boolean(),
  manageOrgCode: Joi.string().allow(null),
});
export const jBodyChangePassUser = Joi.object({
  password: Joi.string().min(1).max(300).required(),
  newPassword: Joi.string().min(1).max(300).required(),
});
export const jSearchUsers = Joi.object({
  ranksId: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  positionsId: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  organizationsCode: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string()),
  ),
  roleIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  username: Joi.string().allow(null, ''),
  name: Joi.string().allow(null, ''),
  contact: Joi.string().allow(null, ''),
  limit: Joi.number(),
  page: Joi.number(),
  search: Joi.string().allow(null, ''),
  startDate: Joi.date(),
  endDate: Joi.date(),
});

export const jBodyLogin = Joi.object({
  username: Joi.string().min(1).max(300).required(),
  password: Joi.string().min(6).max(300).required(),
});

export const jBodyLoginKeycloak = Joi.object({
  code: Joi.string().required(),
  redirectUri: Joi.string().uri().required(),
});

export const eMessage: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng chưa chính xác',
  statusCode: 400,
};

export const jIdInUpdateStatus = Joi.array().items(Joi.number());
export const jLogout = Joi.object({
  keycloakRFToken: Joi.string().required(),
});
