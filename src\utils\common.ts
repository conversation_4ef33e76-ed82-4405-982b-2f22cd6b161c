import { LENGTH_CODE, LevelData, PREFIX_TT } from '../constants';

export const isNumeric = (value: string): boolean => {
  const numberValue = parseInt(value);
  return !isNaN(numberValue) && isFinite(numberValue);
};

export const getCodeOrgSearchByLevel = (
  inputString: string,
  level: LevelData,
): string => {
  const expectedLength: number = LENGTH_CODE;
  if (inputString.length !== expectedLength) {
    throw new Error(
      `Input string must be exactly ${expectedLength} characters long`,
    );
  }
  let lengthToCut: number;
  switch (level) {
    case LevelData.SELF:
      lengthToCut = 11;
      break;
    case LevelData.REGION:
      lengthToCut = 8;
      break;
    case LevelData.DIVISION:
      lengthToCut = 5;
      break;
    case LevelData.ALL:
      lengthToCut = 2;
      break;
    default:
      throw new Error('Level must be 1, 2, or 3');
  }
  const cutString: string = inputString.substring(0, lengthToCut);

  return cutString;
};

export const getCodeOrgCheckAncestor = (code: string): string => {
  const expectedCodeLength = LENGTH_CODE;
  if (code.length !== expectedCodeLength) {
    throw new Error(
      `Input string must be exactly ${expectedCodeLength} characters long`,
    );
  }

  const prefix = code.slice(0, 2);
  let lengthToCut = 5;
  let additionalPart = '00.00.00.00';

  if (prefix === PREFIX_TT) {
    lengthToCut = 2;
    additionalPart = '00.00.00.00.00';
  }

  const cutCode = code.slice(0, lengthToCut).replace(/\./g, '');
  const paddedCode = `${cutCode}${additionalPart}`;

  return paddedCode;
};

export const getObjCodeOrgSearchGroupByLevel = (
  inputString: string,
  level: LevelData,
) => {
  const expectedLength: number = LENGTH_CODE;
  if (inputString.length !== expectedLength) {
    throw new Error(
      `Input string must be exactly ${expectedLength} characters long`,
    );
  }
  let lengthToCut: number;
  let additionalPart: string;

  switch (level) {
    case LevelData.DIVISION:
      lengthToCut = 8;
      additionalPart = '.00.00.00';
      break;
    case LevelData.REGION:
      lengthToCut = 11;
      additionalPart = '.00.00';
      break;
    default:
      throw new Error('Level must be 1, 2, or 3');
  }
  return { lengthToCut, additionalPart };
};
export const sortDescending = <T>(
  array: T[],
  getValue: (item: T) => number,
): T[] => {
  return array.sort((a, b) => (getValue(b) - getValue(a)) as number);
};

export const sortAscending = <T>(
  items: T[],
  getSortValue: (item: T) => number,
): T[] => {
  return items.sort((a, b) => getSortValue(a) - getSortValue(b));
};

export const removeDuplicateCommonNames = <T extends { commonName: string }>(
  allocations: T[],
): T[] => {
  let previousCommonName: string | null = null;

  return allocations.map((allocation, index) => {
    if (index === 0 || allocation.commonName !== previousCommonName) {
      previousCommonName = allocation.commonName;
      return allocation;
    }
    return { ...allocation, commonName: '', voiceCommonName: '' };
  });
};
export const removeDuplicateDays = <T extends { day: number }>(
  allocations: T[],
): T[] => {
  let previousDay: number | null = null;

  return allocations.map((allocation, index) => {
    if (index === 0 || allocation.day !== previousDay) {
      previousDay = allocation.day;
      return allocation;
    }
    return { ...allocation, day: '' };
  });
};
export const removeDuplicateDate = <T extends { date: string }>(
  allocations: T[],
): T[] => {
  let previousDay: string | null = null;

  return allocations.map((allocation, index) => {
    if (index === 0 || allocation.date !== previousDay) {
      previousDay = allocation.date;
      return allocation;
    }
    return { ...allocation, date: '' };
  });
};

export const removeDuplicates = <T extends { [key: string]: unknown }>(
  allocations: T[],
  property: keyof T,
): T[] => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let previousValue: any = null;

  return allocations.map((allocation, index) => {
    if (index === 0 || allocation[property] != previousValue) {
      previousValue = allocation[property];
      return allocation;
    }
    return { ...allocation, [`isSame_${property?.toString()}`]: true };
  });
};

export const specificCommonPrefix = (codes: string[]): string[] => {
  function removeZeros(codes: string[]): string[] {
    return codes.map((code) => code.replace(/\.00/g, ''));
  }

  const codesWithoutZeros = removeZeros(codes);
  if (codes.length === 0) return [];

  codesWithoutZeros.sort();

  const uniquePrefixes = new Set<string>();
  let i = 0;

  while (i < codesWithoutZeros.length) {
    const prefix = codesWithoutZeros[i];
    let j = i + 1;

    while (
      j < codesWithoutZeros.length &&
      codesWithoutZeros[j].startsWith(prefix)
    ) {
      j++;
    }

    uniquePrefixes.add(prefix);
    i = j;
  }

  return Array.from(uniquePrefixes);
};

export const getValidPrefix = (input: string): string => {
  const segments = input.split('.');
  const firstZeroIndex = segments.findIndex((segment) => segment === '00');
  const result =
    firstZeroIndex === -1
      ? segments.join('.')
      : segments.slice(0, firstZeroIndex).join('.');

  return result;
};
