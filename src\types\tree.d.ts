export type ITreeNode = {
  id?: number | string;
  parentId?: number | string;
  name?: string;
  hasChildren?: boolean;
  level?: number;
  children?: ITreeNode[] | undefined;
  orderBy?: number;
  isEnable?: boolean;
  shortName?: string;
  code?: string;
  parentCode?: string;
  count?: string | number;
  scope?: string;
  desc?: string;
  orderNumber?: number;
};
export type ITree = {
  length: number;
  countUseLeaf?: number;
  countCurrentLeaf?: number;
  countRemainLeaf?: number;
  node?: ITreeNode[];
};

export type ITreeNodeOrg = {
  code: string;
  parentCode?: string;
  name: string;
  hasChildren: boolean;
  level: number;
  children: ITreeNodeOrg[];
  order?: number;
};
export type ITreeOrg = {
  length: number;
  countUseLeaf?: number;
  countCurrentLeaf?: number;
  countRemainLeaf?: number;
  node?: ITreeNodeOrg[];
};

export type IUserInTreeNodeOrg = {
  id: number | string;
  parentId?: number | string;
  name: string;
  hasChildren: boolean;
  level: number;
  children: IUserInTreeNodeOrg[];
  user?: boolean;
};
