import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { UserGuideContent } from './userGuideContent.model';

@Entity('user-guides')
export class UserGuide {
  @PrimaryColumn({ type: 'nvarchar', length: 250 })
  slug?: string;

  @Column({ length: 250, type: 'nvarchar' })
  name?: string;

  @Column({ type: 'nvarchar', length: 250 })
  parentSlug?: string;

  @Column({ type: 'tinyint', default: 0 })
  order?: number;

  @OneToMany(() => UserGuide, (userGuide) => userGuide.parent)
  children?: UserGuide[];

  @ManyToOne(() => UserGuide, (userGuide) => userGuide.children)
  @JoinColumn({ name: 'parentSlug', referencedColumnName: 'slug' })
  parent?: UserGuide;

  @OneToOne(() => UserGuideContent)
  @JoinColumn({ name: 'slug' })
  content?: UserGuideContent;
}
