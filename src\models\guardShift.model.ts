import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { ShiftPost } from './shiftPost.model';
import { Organization } from './organization.model';
import { ShiftInspection } from './shiftInspection.model';

@Entity('guardShifts')
export class GuardShift extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    type: 'datetime2',
  })
  date?: Date;

  @Column({
    name: 'start_time',
    type: 'datetime2',
  })
  startTime?: Date;

  @Column({
    name: 'end_time',
    type: 'datetime2',
  })
  endTime?: Date;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;

  @Column({
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  note?: string;

  @Column({
    name: 'order_num',
    type: 'int',
  })
  orderNum?: number;

  @OneToMany(() => ShiftPost, (shiftPost) => shiftPost.guardShift)
  shiftPosts?: ShiftPost[];

  @OneToMany(
    () => ShiftInspection,
    (shiftInspection) => shiftInspection.guardShift,
  )
  shiftInspections?: ShiftInspection[];
}
