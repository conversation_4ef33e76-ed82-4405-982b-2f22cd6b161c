import { MigrationInterface, QueryRunner } from 'typeorm';

export class T1753692836617 implements MigrationInterface {
  name = 'T1753692836617';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP CONSTRAINT "FK_695f5ceab2fbe28b772ef23a74f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP CONSTRAINT "FK_6db52086f2363bf60584eee49a4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_7ee12dcdc9f2bc35d74ec160e65"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_c0342b8979509156404d3e61ae0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP COLUMN "detail_duty_now_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD "detail_duty_now_id" uniqueidentifier NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP COLUMN "detail_duty_feature_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD "detail_duty_feature_id" uniqueidentifier NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP COLUMN "detail_duty_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD "detail_duty_id" uniqueidentifier`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP COLUMN "detail_duty_out_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD "detail_duty_out_id" uniqueidentifier`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD CONSTRAINT "FK_695f5ceab2fbe28b772ef23a74f" FOREIGN KEY ("detail_duty_now_id") REFERENCES "cat_lich_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD CONSTRAINT "FK_6db52086f2363bf60584eee49a4" FOREIGN KEY ("detail_duty_feature_id") REFERENCES "cat_lich_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_7ee12dcdc9f2bc35d74ec160e65" FOREIGN KEY ("detail_duty_id") REFERENCES "cat_lich_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_c0342b8979509156404d3e61ae0" FOREIGN KEY ("detail_duty_out_id") REFERENCES "cat_lich_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_c0342b8979509156404d3e61ae0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP CONSTRAINT "FK_7ee12dcdc9f2bc35d74ec160e65"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP CONSTRAINT "FK_6db52086f2363bf60584eee49a4"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP CONSTRAINT "FK_695f5ceab2fbe28b772ef23a74f"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP COLUMN "detail_duty_out_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD "detail_duty_out_id" bigint`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" DROP COLUMN "detail_duty_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD "detail_duty_id" bigint`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP COLUMN "detail_duty_feature_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD "detail_duty_feature_id" bigint NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" DROP COLUMN "detail_duty_now_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD "detail_duty_now_id" bigint NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_c0342b8979509156404d3e61ae0" FOREIGN KEY ("detail_duty_out_id") REFERENCES "detailDutys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "guestLogs" ADD CONSTRAINT "FK_7ee12dcdc9f2bc35d74ec160e65" FOREIGN KEY ("detail_duty_id") REFERENCES "detailDutys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD CONSTRAINT "FK_6db52086f2363bf60584eee49a4" FOREIGN KEY ("detail_duty_feature_id") REFERENCES "detailDutys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "situationSummaries" ADD CONSTRAINT "FK_695f5ceab2fbe28b772ef23a74f" FOREIGN KEY ("detail_duty_now_id") REFERENCES "detailDutys"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
