import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';

@Entity('configShifts')
export class ConfigShift extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'start_time',
    type: 'nvarchar',
    nullable: true,
  })
  startTime?: string;

  @Column({
    name: 'end_time',
    type: 'nvarchar',
    nullable: true,
  })
  endTime?: string;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;

  @Column({
    name: 'order_num',
    type: 'int',
  })
  orderNum?: number;
}
