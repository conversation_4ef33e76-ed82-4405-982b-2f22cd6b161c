import { ISearchQuery } from 'src/types/req';
import { database } from '../config';
import { Type, User } from '../models';
import { Log } from '../models/log.model';
import { IApiError } from '../types/validation';
import { ERROR } from '../utils/error';
import { IGetLogsQuery } from './logs';
import { DEFAULT_LIMIT, createLinks } from '../utils/pagination';

export const insertLog = async (log: Partial<Log>) => {
  const errors: IApiError['errors'] = [];
  const repoLog = database.getRepository(Log);
  const repoType = database.getRepository(Type);
  const repoUser = database.getRepository(User);

  const exitsType = await repoType.find({
    where: [{ id: log.typeId }],
    withDeleted: true,
  });
  const exitsUser = await repoUser.find({
    where: [{ id: log.userId }],
    withDeleted: true,
  });

  if (!exitsType) {
    errors.push({
      value: `type: ${log.typeId}`,
      message: 'Loại log không tồn tại.',
    });
  }
  if (!exitsUser) {
    errors.push({
      value: `user: ${log.userId}`,
      message: 'Tài khoản không tồn tại.',
    });
  }

  const error = {
    part: 'body',
    code: ERROR.BAD_REQUEST,
    message: 'Kiểm tra lại thông tin của các trường dữ liệu đầu vào',
    errors,
  };
  if (errors && errors.length > 0) {
    return { error };
  }
  log.fullText = `${log.content}${log.ip}`;

  const tempLog = repoLog.create(log);
  const newLog = await repoLog.save(tempLog);

  return { ...newLog };
};

export const find = async (query: ISearchQuery<IGetLogsQuery>) => {
  const repo = database.getRepository(Log);
  const conditions = repo
    .createQueryBuilder('logs')
    .withDeleted()
    .leftJoinAndSelect('logs.type', 'type')
    .leftJoinAndSelect('logs.user', 'user')
    .orderBy('logs.id', 'DESC');

  if (query.fullText && query.fullText != '') {
    conditions.andWhere('LOWER(logs.fullText) like LOWER(:fullText)', {
      fullText: `%${query.fullText}%`,
    });
  }
  if (query.startTime && query.endTime) {
    conditions.andWhere('logs.createdAt BETWEEN :startTime AND :endTime', {
      startTime: new Date(query.startTime),
      endTime: new Date(query.endTime),
    });
  }
  if (query.typeIds && query.typeIds.length > 0) {
    conditions.andWhere('logs.typeId IN (:...typeIds)', {
      typeIds: query.typeIds,
    });
  }

  const limit: number = query.limit ? Number(query.limit) : DEFAULT_LIMIT;
  const page: number = query.page ? Number(query.page) : 1;

  const totalItems = await conditions.getCount();
  const totalPages = Math.ceil(totalItems / limit);

  const links = createLinks('/log?', query, page, totalPages);

  const logs = await conditions
    .skip((page - 1) * limit)
    .take(limit)
    .getMany();
  return {
    meta: {
      totalItems,
      itemsPerPage: query.limit,
      totalPages,
      currentPages: query.page,
      items: [...logs],
    },
    links,
  };
};
