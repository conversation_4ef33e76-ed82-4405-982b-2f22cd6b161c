import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { DMLoaiHinhTruc } from './dmLoaiHinhTruc.model';
import { PositionCategory } from './positionCategory.model';

@Entity('chucVuLoaiHinhTruc')
export class ChucVuLoaiHinhTruc extends EntityModel {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id?: number;

  @Column({
    name: 'maLoaiHinhTruc',
    type: 'varchar',
    length: 36,
  })
  maLoaiHinhTruc?: string;
  @ManyToOne(() => DMLoaiHinhTruc)
  @JoinColumn({ name: 'maLoaiHinhTruc', referencedColumnName: 'ma' })
  dMLoaiHinhTruc?: DMLoaiHinhTruc;

  @Column({
    name: 'maChucVu',
    type: 'varchar',
    length: 36,
  })
  maChucVu?: string;
  @ManyToOne(() => PositionCategory)
  @JoinColumn({ name: 'maChucVu', referencedColumnName: 'code' })
  positionCategory?: PositionCategory;
}
