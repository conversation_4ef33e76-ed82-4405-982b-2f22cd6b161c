import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
// import { hasPerm } from '../middlewares/has-perm.middleware';
// import { RolePerms } from '../constants';
import {
  createWorkingSchedule,
  deleteWorkingSchedule,
  getWorkingSchedule,
  searchWorkingSchedules,
  updateWorkingSchedule,
} from './working-schedule.controller';
import {
  eBodyWorkingSchedule,
  eIdInParams,
  eSearchQuery,
  jBodyWorkingSchedule,
  jIdInParams,
  jSearchWorkingSchedule,
} from './working-schedule.validation';

const router = express.Router();

router.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editWorkingSchedule]),
  createValidator('body', jBodyWorkingSchedule, eBodyWorkingSchedule),
  createWorkingSchedule,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editWorkingSchedule]),
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyWorkingSchedule, eBodyWorkingSchedule),
  updateWorkingSchedule,
);

router.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editWorkingSchedule]),
  createValidator('params', jIdInParams, eIdInParams),
  deleteWorkingSchedule,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewWorkingSchedule]),
  createValidator('params', jIdInParams, eIdInParams),
  getWorkingSchedule,
);

router.get(
  '/',
  checkAuth,
  // hasPerm([RolePerms.viewWorkingSchedule]),
  createValidator('query', jSearchWorkingSchedule, eSearchQuery),
  searchWorkingSchedules,
);

export default router;
