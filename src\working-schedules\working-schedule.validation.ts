import Joi from 'joi';
import { IApiError } from '../types/validation';

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jBodyWorkingSchedule = Joi.object({
  startTime: Joi.string().allow('').optional(),
  endTime: Joi.string().allow('').optional(),
  codeOrg: Joi.string().max(100).required(),
  season: Joi.string().max(50).allow('').optional(),
  months: Joi.string().max(250).allow('').optional(),
  status: Joi.string().max(50).allow('').optional(),
  effectiveFrom: Joi.string().max(50).allow('').optional(),
  effectiveTo: Joi.string().max(50).allow('').optional(),
});

const eBodyWorkingSchedule: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const jSearchWorkingSchedule = Joi.object({
  codeOrg: Joi.string(),
  season: Joi.string(),
  status: Joi.string(),
  startTime: Joi.string(),
  endTime: Joi.string(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export {
  jIdInParams,
  eIdInParams,
  jBodyWorkingSchedule,
  eBodyWorkingSchedule,
  jSearchWorkingSchedule,
  eSearchQuery,
};
