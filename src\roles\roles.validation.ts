import Joi from 'joi';
import { IApiError } from '../types/validation';
import { enumStrValues2Array } from '../utils/array';
import { LevelData } from '../constants';

export const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

export const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng Id (Path) chưa chính xác',
  errors: { id: 'id Định dạng chọn chính xác' },
  statusCode: 400,
};

export const eMessage: IApiError = {
  part: 'body',
  message: 'Định dạng chưa chính xác',
  statusCode: 400,
  code: 'VALIDATION_ERROR',
};

export const jBodyPostRole = Joi.object({
  name: Joi.string().min(1).max(300).required(),
  desc: Joi.string().min(1).max(300),
  levelData: Joi.string()
    .valid(...enumStrValues2Array(LevelData))
    .required(),
  status: Joi.number().valid(0, 1),
});

export const jBodyPutRole = Joi.object({
  name: Joi.string().min(1).max(300),
  desc: Joi.string().min(1).max(300),
  levelData: Joi.string()
    .valid(...enumStrValues2Array(LevelData))
    .required(),
  status: Joi.number().valid(0, 1),
});

export const eBodyPostRole: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng  chưa chính xác',
  statusCode: 400,
};
export const jRolePermissionsBody = Joi.array().items(Joi.number());

export const jRoleUsersBody = Joi.object({
  userId: Joi.number(),
});
export const jRoleAreasBody = Joi.object({
  areaId: Joi.number(),
});
export const eRolePermissionsBody: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng chưa chính xác',
  statusCode: 400,
  errors: { permissionsId: 'permissionsId Định dạng chọn chính xác' },
};

export const jIdInUpdateStatus = Joi.array().items(Joi.number());

export const jQueryRole = Joi.object({
  name: Joi.string(),
  desc: Joi.string(),
  search: Joi.string().allow(''),
  status: Joi.number(),
  levelData: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string()),
  ),
});
