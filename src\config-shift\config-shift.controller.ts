import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ISearchQuery } from '../types/req';
import { ERROR } from '../utils/error';
import { ISearchConfigShiftQuery } from './config-shift';
import * as configShiftsService from './config-shift.service';
import { ConfigShift } from '../models';

export const createConfigShifts = async (
  req: Request & { body: ConfigShift },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await configShiftsService.create(req.body);
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateConfigShifts = async (
  req: Request & { body: ConfigShift } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await configShiftsService.update(
      Number(req.params.id),
      req.body,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Cấu hình ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteConfigShifts = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await configShiftsService.remove(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Cấu hình ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getConfigShifts = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await configShiftsService.findById(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Cấu hình ca gác không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const searchConfigShifts = async (
  req: Request & { query: ISearchQuery<ISearchConfigShiftQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await configShiftsService.search(req.query);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
