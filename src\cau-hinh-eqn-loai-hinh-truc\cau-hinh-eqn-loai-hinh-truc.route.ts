import express from 'express';
import { checkAuth } from '../middlewares/auth.middleware';
import { createCauHinheQNLoaiHinhTruc } from './cau-hinh-eqn-loai-hinh-truc.controller';
import { createValidator } from '../utils/validator';
import {
  eCreate,
  jBodyCauHinheQNLoaiHinhTruc,
} from './cau-hinh-eqn-loai-hinh-truc.validation';
const router = express.Router();
router.post(
  '/',
  checkAuth,
  createValidator('body', jBodyCauHinheQNLoaiHinhTruc, eCreate),
  createCauHinheQNLoaiHinhTruc,
);
export default router;
