import { faker } from '@faker-js/faker';
import { v4 as uuidv4 } from 'uuid';

import { ISearchQuery } from 'src/types/req';
import { database } from '../config';
import { eQN, Organization, User } from '../models';
import { MilitaryRank } from '../models/militaryRank.model';
import { PositionCategory } from '../models/positionCategory.model';
import {
  getAllChildOrganizations,
  getAllIdsByArray,
} from '../organizations/organization.service';
import { createLinks } from '../utils/pagination';
import {
  IDashboard,
  IDashboardQuery,
  IListEQNQuery,
  ISearcEQNQuery,
} from './eqn';
export const seachTreeOrg = async (query: ISearchQuery<ISearcEQNQuery>) => {
  if (!query.codeOrg || query.codeOrg === '') {
    return {
      meta: {
        totalItems: 0,
        itemsPerPage: query.limit,
        totalPages: 0,
        currentPage: query.page,
      },
      data: [],
      links: createLinks('/eqn/tree/org?', query, query.page, 0),
    };
  }

  const repo = database.getRepository(Organization);
  const qb = repo.createQueryBuilder('org').withDeleted();

  // Base query: Filter organizations with depth = 1
  // qb.where('org.depth = :depth', { depth: 1 });

  qb.leftJoinAndSelect('org.children', 'child');
  qb.leftJoinAndSelect('child.eQns', 'eQns');

  if (query.name) {
    qb.andWhere('eQns.fullName LIKE :name', { name: `%${query.name}%` });
  }

  if (query.codeOrg) {
    qb.andWhere('org.code = :codeOrg', { codeOrg: query.codeOrg });
  }

  if (query.typeId) {
    qb.andWhere('eQns.typeId = :typeId', { typeId: query.typeId });
  }

  // Join with organization
  qb.leftJoin('eQns.organization', 'eQNOrg').addSelect([
    'eQNOrg.code',
    'eQNOrg.name',
  ]);

  // Join with reinforcement organization
  qb.leftJoin(
    'eQns.reinforcementOrganization',
    'eQNReinforcementOrg',
  ).addSelect(['eQNReinforcementOrg.code', 'eQNReinforcementOrg.name']);

  // Join with type
  qb.leftJoin('eQns.type', 'eQNType').addSelect(['eQNType.id', 'eQNType.name']);

  // Join with position
  qb.leftJoin('eQns.position', 'eQNPosition').addSelect([
    'eQNPosition.id',
    'eQNPosition.name',
    'eQNPosition.code',
  ]);

  qb.leftJoin('eQns.Rank', 'eQNRank').addSelect(['eQNRank.id', 'eQNRank.name']);

  // Join with users
  qb.leftJoin('eQns.userInfo', 'eQNUser').addSelect([
    'eQNUser.id',
    'eQNUser.username',
    'eQNUser.name',
  ]);

  // Select all fields
  qb.select([
    'org.id',
    'org.code',
    'org.name',
    'org.desc',
    'org.shortName',
    'org.parentId',
    'child.id',
    'child.code',
    'child.name',
    'child.desc',
    'child.shortName',
    'child.parentId',
    'eQns.id',
    'eQns.fullName',
    'eQns.shortName',
    'eQns.orgCode',
    'eQns.reinforcementOrgId',
    'eQns.typeId',
    'eQns.statusId',
    'eQns.positionId',
    'eQns.phoneNumber',
    'eQns.isEnable',
    'eQns.addressBooks',
    'eQns.birthPlaceWardId',
    'eQns.birthday',
    'eQns.cccd',
    'eQns.cccdIssuedWardId',
    'eQns.gender',
    'eQns.disabledReasonId',
    'eQns.identification',
    'eQns.image',
    'eQNOrg.code',
    'eQNOrg.name',
    'eQNReinforcementOrg.code',
    'eQNReinforcementOrg.name',
    'eQNType.id',
    'eQNType.name',
    'eQNPosition.id',
    'eQNPosition.name',
    'eQNPosition.code',
    'eQNRank.id',
    'eQNRank.name',
    'eQNUser.id',
    'eQNUser.username',
    'eQNUser.name',
  ]);

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit);
  const [data, total] = await qb.getManyAndCount();

  const links = createLinks(
    '/eqn/tree/org?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
    },
    data,
    links,
  };
};

export const getListQN = async (query: ISearchQuery<IListEQNQuery>) => {
  const { limit, page, orgCode, positionId, rankId, typeId, fullText } = query;
  const skip = (page - 1) * limit;
  const qQNQuery = database
    .createQueryBuilder(eQN, 'eqn')
    .withDeleted()
    .leftJoinAndSelect('eqn.type', 'type')
    .leftJoinAndSelect('eqn.organization', 'org')
    .leftJoinAndSelect('eqn.reinforcementOrganization', 'reinforcementOrg')
    .leftJoinAndSelect('eqn.position', 'position')
    .leftJoinAndSelect('eqn.Rank', 'rank');

  if (orgCode) {
    const allOrgWithChildren = await getAllChildOrganizations(orgCode);
    if (!allOrgWithChildren || allOrgWithChildren.length === 0) {
      return {
        meta: {
          totalItems: 0,
          itemsPerPage: limit,
          totalPages: Math.ceil(0 / limit),
          currentPage: page,
          items: [],
        },
        links: createLinks('/eqn/list?', query, page, Math.ceil(0 / limit)),
      };
    }

    const orgCodes = allOrgWithChildren.map((org) => `'${org.code}'`).join(',');
    qQNQuery.andWhere(`eqn.orgCode In (${orgCodes})`);
  }
  if (positionId) {
    qQNQuery.andWhere('eqn.positionId = :positionId', { positionId });
  }
  if (rankId) {
    qQNQuery.andWhere('eqn.rankId = :rankId', { rankId });
  }
  if (typeId) {
    qQNQuery.andWhere('eqn.typeId = :typeId', { typeId });
  }
  if (fullText) {
    qQNQuery.andWhere(
      '(eqn.eQN LIKE :fullText OR eqn.fullName LIKE :fullText OR eqn.phoneNumber LIKE :fullText)',
      { fullText: `%${fullText}%` },
    );
  }

  qQNQuery.orderBy('eqn.createdAt', 'DESC').skip(skip).take(limit);
  const [data, total] = await qQNQuery.getManyAndCount();
  const links = createLinks(
    '/eqn/list?',
    query,
    page,
    Math.ceil(total / limit),
  );
  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};

export const getQNById = async (id: string) => {
  const eqn = await database
    .getRepository(eQN)
    .createQueryBuilder('eqn')
    .withDeleted()
    .leftJoinAndSelect('eqn.type', 'type')
    .leftJoinAndSelect('eqn.organization', 'organization')
    .leftJoinAndSelect(
      'eqn.reinforcementOrganization',
      'reinforcementOrganization',
    )
    .leftJoinAndSelect('eqn.position', 'position')
    .leftJoinAndSelect('eqn.Rank', 'Rank')
    .where('eqn.id = :id', { id })
    .getOne();

  if (!eqn) {
    throw new Error('QN not found');
  }
  return eqn;
};

export const getRanks = async () => {
  const ranks = await database
    .getRepository(MilitaryRank)
    .createQueryBuilder('rank')
    .withDeleted()
    .getMany();
  return ranks ?? [];
};

export const getPositions = async () => {
  const positions = await database
    .getRepository(PositionCategory)
    .createQueryBuilder('position')
    .withDeleted()
    .getMany();
  return positions ?? [];
};

export const getOrgs = async () => {
  const orgs = await database
    .getRepository(Organization)
    .createQueryBuilder('org')
    .withDeleted()
    .select(['org.id', 'org.code', 'org.name'])
    .getMany();
  return orgs ?? [];
};

export const getDashboard = async (query: IDashboardQuery, user: User) => {
  const allOrgWithChildren = await getAllChildOrganizations(
    query.codeOrg,
    user,
  );

  if (!allOrgWithChildren || allOrgWithChildren.length === 0) {
    return [];
  }

  const orgCodes = allOrgWithChildren.map((org) => `'${org.code}'`).join(',');

  const createQueryBuilder = database
    .getRepository(eQN)
    .createQueryBuilder('eqn')
    .withDeleted()
    .leftJoinAndSelect('eqn.organization', 'org')
    .where(`eqn.orgCode IN (${orgCodes})`);

  const eqns = await createQueryBuilder.getMany();
  const totalCount = eqns.length;

  const dashboardStats: IDashboard = {
    totalEqnCount: totalCount,
    organization: {
      id: allOrgWithChildren[0].id ?? '',
      name: allOrgWithChildren[0].name ?? '',
    },
    childOrgs: [],
  };

  const currentOrg =
    allOrgWithChildren.length > 0 ? allOrgWithChildren[0] : null;
  const childOrgs = currentOrg?.children || [currentOrg];

  for (const org of childOrgs) {
    const orgStats = eqns.filter((eqn) => {
      return (
        eqn?.organization?.code?.toLocaleLowerCase() ===
        org?.code?.toLocaleLowerCase().toString()
      );
    });
    const allOrgWithChildren = getAllIdsByArray(org?.children);

    const data = {
      organization: { id: org?.id ?? '', name: org?.name ?? '' },
      totalEqnCountByOrg: orgStats.length,
    };

    // get stats for each org child
    for (const orgChild of allOrgWithChildren) {
      const orgChildStats = orgStats.filter((eqn) => {
        return (
          eqn.organization?.id?.toLocaleLowerCase() ===
          (orgChild.id ? orgChild.id.toLocaleLowerCase().toString() : '')
        );
      });

      data.totalEqnCountByOrg += orgChildStats.length;
    }

    dashboardStats.childOrgs.push(data);
  }

  return dashboardStats;
};
function createPerson(
  orgCode: string,
  positionId: string,
  rankId: string,
  fullName: string,
  shortName: string,
): eQN {
  return {
    id: uuidv4(),
    fullName: fullName,
    shortName: shortName,
    orgCode: orgCode,
    typeId: 'SQ',
    rankId: rankId,
    positionId: positionId,
    phoneNumber: faker.phone.number(),
    isEnable: true,
    addressBooks: faker.location.streetAddress(),
    birthPlaceWardId: uuidv4(),
    birthday: faker.date.birthdate({ min: 1980, max: 2000, mode: 'year' }),
    cccd: faker.string.numeric(12),
    cccdIssuedWardId: uuidv4(),
    gender: faker.datatype.boolean(),
    identification: faker.string.alphanumeric(10),
    createdAt: new Date(),
    updatedAt: new Date(),
    reinforcementOrgId: orgCode,
  };
}
export const createPersons = async () => {
  const repoEqn = database.getRepository(eQN);
  const temp1 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Nguyễn Thị Ngọc',
    'ntn',
  );
  await repoEqn.save(repoEqn.create(temp1));

  const temp2 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Nguyễn Toàn',
    'nt',
  );
  await repoEqn.save(repoEqn.create(temp2));

  const temp3 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Trần Văn Nam',
    'tvn',
  );
  await repoEqn.save(repoEqn.create(temp3));

  const temp5 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Nguyễn Công Nhật',
    'ncn',
  );
  await repoEqn.save(repoEqn.create(temp5));

  const temp6 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Đ/c Yến',
    'yen',
  );
  await repoEqn.save(repoEqn.create(temp6));

  const temp7 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Đ/c Tâm',
    'tam',
  );
  await repoEqn.save(repoEqn.create(temp7));

  const temp8 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Đ/c Cường',
    'cuong',
  );
  await repoEqn.save(repoEqn.create(temp8));

  const temp9 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Đ/c Bằng',
    'bang',
  );
  await repoEqn.save(repoEqn.create(temp9));

  const temp10 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Đ/c Hồng',
    'hong',
  );
  await repoEqn.save(repoEqn.create(temp10));

  const temp11 = createPerson(
    '***********.00.00',
    'PC009',
    'RANK014',
    'Đ/c Đạt',
    'dat',
  );
  await repoEqn.save(repoEqn.create(temp11));
  // ==================================
  // const cs1 = createPerson(
  //   '45.06.03.00.00.00',
  //   'PC009',
  //   'RANK014',
  //   'Đ/c Thu',
  //   'thu',
  // );
  // await repoEqn.save(repoEqn.create(cs1));

  // const cs2 = createPerson(
  //   '45.06.03.00.00.00',
  //   'PC009',
  //   'RANK014',
  //   'Đ/c Nhi',
  //   'nhi',
  // );
  // await repoEqn.save(repoEqn.create(cs2));

  // const cs3 = createPerson(
  //   '45.06.03.00.00.00',
  //   'PC009',
  //   'RANK014',
  //   'Đ/c Trường',
  //   'trường',
  // );
  // await repoEqn.save(repoEqn.create(cs3));

  // const cs4 = createPerson(
  //   '45.05.03.00.00.00',
  //   'PC009',
  //   'RANK014',
  //   'Đ/c Tuấn',
  //   'tuan',
  // );
  // await repoEqn.save(repoEqn.create(cs4));

  // const cs5 = createPerson(
  //   '45.05.03.00.00.00',
  //   'PC009',
  //   'RANK014',
  //   'Đ/c Khoa',
  //   'khoa',
  // );
  // await repoEqn.save(repoEqn.create(cs5));

  // const cs6 = createPerson(
  //   '45.05.03.00.00.00',
  //   'PC009',
  //   'RANK014',
  //   'Đ/c Bình',
  //   'binh',
  // );
  // await repoEqn.save(repoEqn.create(cs6));

  return 200;
};
