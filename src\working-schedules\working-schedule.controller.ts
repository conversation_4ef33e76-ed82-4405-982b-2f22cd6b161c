import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { WorkingSchedule } from '../models';
import { ERROR } from '../utils/error';
import * as workingScheduleService from './working-schedule.service';
import { ISearchWorkingScheduleQuery } from './working-schedule';
import { ISearchQuery } from 'src/types/req';

export const createWorkingSchedule = async (
  req: Request & { body: WorkingSchedule },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await workingScheduleService.create(req.body);
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateWorkingSchedule = async (
  req: Request & { body: WorkingSchedule } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    await workingScheduleService.update(Number(req.params.id), {
      ...req.body,
    });
    return res.json({
      message: 'Cập nhật thành công',
    });
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteWorkingSchedule = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await workingScheduleService.remove(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getWorkingSchedule = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await workingScheduleService.findById(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const searchWorkingSchedules = async (
  req: Request & { query: ISearchQuery<ISearchWorkingScheduleQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await workingScheduleService.search(req.query);
    return res.json(result);
  } catch (e) {
    next(e);
  }
};
