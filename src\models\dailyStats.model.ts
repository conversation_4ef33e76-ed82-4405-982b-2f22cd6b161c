import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  <PERSON>To<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';

@Entity('daily_stats')
export class DailyStats extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'report_date',
    type: 'datetime2',
    nullable: false,
  })
  reportDate?: Date;

  @Column({
    name: 'org_id',
    type: 'nvarchar',
    nullable: false,
  })
  orgId?: string;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
    nullable: false,
    default: '',
  })
  codeOrg?: string;

  @Column({
    name: 'total_count',
    type: 'int',
    nullable: false,
    default: 0,
  })
  totalCount?: number;

  @Column({
    name: 'dung_gio_count',
    type: 'int',
    nullable: false,
    default: 0,
  })
  dungGioCount?: number;

  @Column({
    name: 'khong_cham_count',
    type: 'int',
    nullable: false,
    default: 0,
  })
  khongChamCount?: number;

  @Column({
    name: 'di_muon_count',
    type: 'int',
    nullable: false,
    default: 0,
  })
  diMuonCount?: number;

  @Column({
    name: 'vang_co_ly_do_count',
    type: 'int',
    nullable: false,
    default: 0,
  })
  vangCoLyDoCount?: number;

  @Column({
    name: 'nghi_phep_count',
    type: 'int',
    nullable: false,
    default: 0,
  })
  nghiPhepCount?: number;

  @Column({
    name: 'note',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  note?: string;

  @ManyToOne(() => Organization, (organization) => organization.id)
  @JoinColumn({ name: 'org_id', referencedColumnName: 'id' })
  organization?: Organization;
}
