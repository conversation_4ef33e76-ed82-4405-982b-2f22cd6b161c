import { FindOptionsWhere, Like } from 'typeorm';
import { database } from '../config';
import { Error as ErrorModel } from '../models';
import { IGetErrorsQuery, IPostError, IPutError } from './errors';
import { ERROR } from '../utils/error';
import { ISearchQuery } from '../types/req';
import { createLinks, DEFAULT_LIMIT } from '../utils/pagination';

export const createError = async (data: IPostError): Promise<ErrorModel> => {
  const errorRepo = database.getRepository(ErrorModel);
  let error = errorRepo.create({
    name: data.name,
    content: data.content,
    type: data.type,
    status: data.status,
    handleMethod: data.handleMethod,
    fullText: `${data.name} ${data.content} ${data.type} ${data.status}`,
  });

  error = await errorRepo.save(error);

  return error;
};

export const deleteError = async (
  id: number,
): Promise<{ success: boolean }> => {
  const errorRepo = database.getRepository(ErrorModel);

  const error = await errorRepo.findOne({
    where: { id },
  });
  if (!error) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  await errorRepo.remove(error);

  return { success: true };
};

export const findErrors = async (
  searchQuery: ISearchQuery<IGetErrorsQuery>,
) => {
  const errorRepo = database.getRepository(ErrorModel);

  const limit = searchQuery.limit ?? DEFAULT_LIMIT;
  const page = searchQuery.page ?? 1;

  const where: FindOptionsWhere<ErrorModel> = {};
  if (searchQuery.search) {
    where.fullText = Like(`%${searchQuery.search}%`);
  }
  if (searchQuery.content) {
    where.content = Like(`%${searchQuery.content}%`);
  }
  if (searchQuery.name) {
    where.name = Like(`%${searchQuery.name}%`);
  }
  if (searchQuery.type) {
    where.type = searchQuery.type;
  }
  if (searchQuery.status) {
    where.status = searchQuery.status;
  }
  if (searchQuery.handleMethod) {
    where.handleMethod = Like(`%${searchQuery.handleMethod}%`);
  }

  const [errors, total] = await errorRepo.findAndCount({
    where,
    order: {
      ...(searchQuery.sortBy
        ? {
            [searchQuery.sortBy]: searchQuery.order ?? 'DESC',
          }
        : {
            createdAt: 'DESC',
          }),
    },
    take: limit,
    skip: (page - 1) * limit,
  });

  const totalPages = Math.ceil(total / limit);
  const links = createLinks('/errors?', searchQuery, page, totalPages);

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages,
      currentPage: page,
      items: errors,
    },
    links,
  };
};

export const editError = async (id: number, payload: IPutError) => {
  const errorRepo = database.getRepository(ErrorModel);

  const error = await errorRepo.findOne({
    where: { id },
  });

  if (!error) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  error.content = payload.content ?? error.content;
  error.type = payload.type ?? error.type;
  error.status = payload.status ?? error.status;
  error.handleMethod = payload.handleMethod ?? error.handleMethod;
  error.name = payload.name ?? error.name;

  return await errorRepo.save(error);
};

export const findError = async (id: number) => {
  const errorRepo = database.getRepository(ErrorModel);
  const error = await errorRepo.findOne({
    where: { id },
  });
  if (!error) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  return error;
};
