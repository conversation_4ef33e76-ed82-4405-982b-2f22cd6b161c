import { File, User } from '../models';
import config, { dirUploadFile, database } from '../config';
import path, { dirname, join } from 'path';
import { In } from 'typeorm';
import { ERROR } from '../utils/error';
import jwt from 'jsonwebtoken';
import sharp from 'sharp';
import multer from 'multer';
import fs from 'fs-extra';
import { cwd } from 'process';
import ffmpeg from 'fluent-ffmpeg';
import ffmpegStatic from 'ffmpeg-static';
import log from '../utils/log';

const storage = multer.diskStorage({
  destination: async (req, file, cb) => {
    let { prefix } = req.body;
    const { prefix: qPrefix } = req.query;
    prefix = prefix || qPrefix || 'default';
    const now = new Date();
    const destination = `${prefix}/${now.getTime()}`;
    const newPath = `${dirUploadFile.serverUpload}/${destination}`;
    if (!(await fs.exists(`${dirUploadFile.serverUpload}/${prefix}`))) {
      await fs.mkdir(`${dirUploadFile.serverUpload}/${prefix}`, {
        recursive: true,
      });
    }
    if (!(await fs.exists(newPath))) {
      await fs.mkdir(newPath);
    }
    cb(null, dirUploadFile.dirUpload + '/' + destination);
  },
  filename: (req, file, cb) => {
    cb(null, Buffer.from(file.originalname, 'latin1').toString('utf-8'));
  },
});

export const upload = multer({
  storage,
});

export const uploadFiles = async (
  files: Express.Multer.File[],
  type: string,
  userId: number,
) => {
  const fileRepo = database.getRepository(File);
  const imageTypes = ['image/png', 'image/jpeg', 'image/webp'];
  const videoTypes = ['video/mp4', 'video/webm'];
  let filesData = await Promise.all(
    files.map(async (file) => {
      file.originalname = Buffer.from(file.originalname, 'latin1').toString(
        'utf-8',
      );
      const key = join(
        file.destination?.replace(dirUploadFile.dirUpload + '/', ''),
        file.originalname,
      );
      const originPath = join(cwd(), dirUploadFile.dirUpload, key);
      let thumbnailKey = ''; // this save to database

      if (imageTypes.includes(file.mimetype) && type !== 'avatar') {
        thumbnailKey = join('thumbnail', key); // this save to database

        const thumbnailPath = join(
          cwd(),
          dirUploadFile.dirUpload,
          thumbnailKey,
        );
        const thumbnailDir = dirname(thumbnailPath);
        try {
          if (!(await fs.exists(thumbnailDir))) {
            await fs.mkdir(thumbnailDir, { recursive: true });
          }
          sharp(originPath)
            .resize(200, 200, {
              fit: 'cover',
              position: 'center',
            })
            .toFile(thumbnailPath);
        } catch (error) {
          log.debug('Error creating thumbnail:', error);
        }
      } else if (videoTypes.includes(file.mimetype)) {
        const pathSplit = key.split('.');
        pathSplit.pop();
        thumbnailKey = join('thumbnail', pathSplit.join('.') + '.png'); //this save to database
        const thumbnailPath = join(
          cwd(),
          dirUploadFile.dirUpload,
          thumbnailKey,
        );
        ffmpeg.setFfmpegPath(ffmpegStatic || '');

        const thumbnailDir = dirname(thumbnailPath);
        if (!(await fs.exists(thumbnailDir))) {
          await fs.mkdir(thumbnailDir, { recursive: true });
        }
        ffmpeg(originPath).screenshots({
          timestamps: ['1'], // Time in seconds to capture the thumbnail
          filename: path.basename(thumbnailPath), // Output file name
          folder: path.dirname(thumbnailPath), // Output folder
          size: '200x200', // Thumbnail size
        });
      }

      return fileRepo.create({
        name: file.originalname,
        key,
        thumbnail: thumbnailKey,
        mime: file.mimetype,
        size: file.size,
        type,
        userId,
      });
    }),
  );

  filesData = await fileRepo.save(filesData);

  return filesData;
};

/**
 *
 * @param ids
 * @param userId - nếu có userId thì chỉ xóa file của userId đó
 * @returns
 */
export const deleteFiles = async (
  ids: number[],
  type?: string,
  userId?: number,
) => {
  const fileRepo = database.getRepository(File);
  const files = await fileRepo.find({
    where: { id: In(ids), ...(type ? { type } : {}) },
  });
  if (!files.length) {
    return;
  }

  if (userId && files.some((file) => file.userId !== userId)) {
    throw new Error(ERROR.BAD_REQUEST);
  }

  for (const file of files) {
    const filePath = join(cwd(), dirUploadFile.dirUpload, file?.key || '');
    const thumbnailPath = join(
      cwd(),
      dirUploadFile.dirUpload,
      file?.thumbnail || '',
    );
    try {
      await fs.unlink(filePath);
      if (file.thumbnail) {
        await fs.unlink(thumbnailPath);
      }
    } catch (_) {
      console.log(`File ${filePath} or ${thumbnailPath} not found`);
    }
  }
  await fileRepo.remove(files);
  return files;
};

const createFileToken = (user: { id: number; username: string }) =>
  jwt.sign(user, config.jwtSecretFile, {
    expiresIn: Number(config.fileTokenTime),
  });

export const generateFileToken = async (user?: User) => {
  const token = createFileToken({
    id: user?.id || 0,
    username: user?.username || '',
  });
  const decode = jwt.decode(token) as jwt.JwtPayload;
  return {
    token,
    expiresAt: decode.exp,
  };
};
