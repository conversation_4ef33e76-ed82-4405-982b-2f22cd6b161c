import Joi from 'joi';
import { IApiError } from '../types/validation';

export const jSearchShiftInspection = Joi.object({
  qneId: Joi.string().optional(),
  datetime: Joi.array().optional(),
  posts: Joi.array().optional(),
  page: Joi.number().optional().default(1),
  limit: Joi.number().optional().default(10),
  fullText: Joi.string().optional(),
});

export const eSearchShiftInspection: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: '<PERSON><PERSON> liệu không hợp lệ',
  statusCode: 400,
};

export const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

export const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

export const jCreateShiftInspection = Joi.object({
  datetime: Joi.string().optional(),
  contentCheck: Joi.string().optional(),
  posts: Joi.string().optional(),
  note: Joi.string().optional(),
  weaponCheck: Joi.string().optional(),
  equipCheck: Joi.string().optional(),
  situationCheck: Joi.string().optional(),
  guardShiftId: Joi.number().optional(),
});

export const eCreateShiftInspection: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

export const jUpdateShiftInspection = Joi.object({
  datetime: Joi.string().optional(),
  contentCheck: Joi.string().optional(),
  posts: Joi.string().optional(),
  note: Joi.string().optional(),
  weaponCheck: Joi.string().optional(),
  equipCheck: Joi.string().optional(),
  situationCheck: Joi.string().optional(),
  guardShiftId: Joi.number().optional(),
});

export const eUpdateShiftInspection: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};
