import Joi from 'joi';
import { IApiError } from '../types/validation';

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jBodyEquipment = Joi.object({
  name: Joi.string().max(250).required(),
  unitId: Joi.number().required(),
  codeOrg: Joi.string().max(100).required(),
  count: Joi.number().required(),
  desc: Joi.string().max(2000),
  status: Joi.string().max(50),
});

const eBodyEquipment: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: '<PERSON><PERSON> liệu không hợp lệ',
  statusCode: 400,
};

const jSearchEquipment = Joi.object({
  name: Joi.string(),
  codeOrg: Joi.string(),
  status: Joi.string(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export {
  jIdInParams,
  eIdInParams,
  jBodyEquipment,
  eBodyEquipment,
  jSearchEquipment,
  eSearchQuery,
};
