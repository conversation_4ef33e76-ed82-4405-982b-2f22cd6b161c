export type IPostError = {
  name: string;
  content: string;
  type: string;
  status: string;
  handleMethod: string;
};

export type IGetErrorsQuery = {
  search?: string;
  name?: string;
  content?: string;
  handleMethod?: string;
  status?: string;
  type?: string;
  page?: number;
  limit?: number;
  order?: ASC | DESC;
  sortBy?: string;
};

export type IPutError = {
  name: string;
  content: string;
  type: string;
  status: string;
  handleMethod: string;
};

export type IAddUpdateHandleError = {
  dueDateStart: string;
  dueDateEnd: string;
  content: string;
  status: string;
};
