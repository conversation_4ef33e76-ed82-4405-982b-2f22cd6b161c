import Joi from 'joi';
import { IApiError } from '../types/validation';

const jBodySearchTypes = Joi.object({
  ids: Joi.alternatives().try(Joi.number(), Joi.array().items(Joi.number())),
  name: Joi.string(),
  desc: Joi.string(),
  scope: Joi.string(),
  status: Joi.boolean(),
  symbol: Joi.string(),
  q: Joi.string(),
  limit: Joi.number(),
  page: Joi.number(),
});
const eBodySearchTypes: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Chuỗi tìm kiếm có thể chứa các trường dữ liệu của Types',
  statusCode: 400,
};

const jBodyPutTypes = Joi.object({
  name: Joi.string().min(0).max(50),
  desc: Joi.string(),
  status: Joi.number(),
});
const eBodyPutTypes: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng chưa chính xác',
  statusCode: 400,
};

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});
const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng Id (Path) chưa chính xác',
  statusCode: 400,
  errors: {
    key: 'id',
    message: 'Định dạng Id (Path) chưa chính xác',
  },
};

const jPostBody = Joi.object({
  name: Joi.string().required(),
  desc: Joi.string(),
  status: Joi.boolean(),
  scope: Joi.string().required(),
  value: Joi.string(),
});

const ePostBody: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu đầu vào chưa chính xác',
  statusCode: 400,
};
export {
  jBodySearchTypes,
  eBodySearchTypes,
  jBodyPutTypes,
  eBodyPutTypes,
  jIdInParams,
  eIdInParams,
  jPostBody,
  ePostBody,
};
