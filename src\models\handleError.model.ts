import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';

import EntityModel from './entity.model';
import { ReportError } from './reportError.model';

@Entity('handle_errors')
export class HandleError extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ type: 'int', name: 'user_id' })
  userId?: number;

  @Column({ type: 'datetime', nullable: true, name: 'due_date_start' })
  dueDateStart?: Date;

  @Column({ type: 'datetime', nullable: true, name: 'due_date_end' })
  dueDateEnd?: Date;

  @Column({ type: 'nvarchar', length: 2000, nullable: true })
  content?: string;

  @Column({ type: 'nvarchar', length: 50 })
  status?: string; // inprogress, done

  @Column({ name: 'report_error_id', nullable: true })
  reportErrorId?: number;
  @OneToOne(() => ReportError, (reportError) => reportError.handleError, {
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'report_error_id' })
  reportError?: HandleError;
}
