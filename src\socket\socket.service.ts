import http from 'http';
import { Server } from 'socket.io';
import jwt from 'jsonwebtoken';
import config from '../config';

export let io: Server;

export function initSocket(
  server: http.Server<typeof http.IncomingMessage, typeof http.ServerResponse>,
) {
  io = new Server(server, {
    cors: {
      origin: '*',
      credentials: true,
    },
  });

  io.on('connection', async (socket) => {
    // Try to retrieve token from several possible locations sent by the client
    const authHeader = socket.handshake.headers['authorization'] as
      | string
      | undefined;
    interface IHandshakeAuth {
      token?: string;
    }
    const handshake = socket.handshake as typeof socket.handshake & {
      auth?: IHandshakeAuth;
      query?: Record<string, string>;
    };

    const token =
      handshake.auth?.token || // Preferred way ("auth" payload)
      handshake.query?.token || // Fallback query param
      authHeader?.split(' ')?.[1];
    let userId: number;
    let orgCode: string;
    if (!token) {
      socket.disconnect();
    } else {
      try {
        const payload = jwt.verify(token, config.jwtsecret) as unknown as {
          id: number;
          username: string;
          ip: string;
          orgCode: string;
          manageOrgCode: string;
        };
        userId = payload.id;
        orgCode = payload.orgCode;
        socket.join(`user:${payload.id}`);
        socket.join(`org:${payload.manageOrgCode}`);
      } catch (_error) {
        socket.disconnect();
      }
    }

    socket.on('disconnect', () => {
      socket.leave(`user:${userId}`);
      socket.leave(`org:${orgCode}`);
    });
  });
}
