import { mssqlConfig } from '../config/sms.database.config';
import { DataSource } from 'typeorm';

export const sendServiceSMS = async ({
  mobile,
  sms_content,
}: {
  mobile: string;
  sms_content: string;
}) => {
  try {
    const pool = await new DataSource({
      type: 'mssql',
      host: mssqlConfig.host,
      port: mssqlConfig.port,
      username: mssqlConfig.username,
      password: mssqlConfig.password,
      database: mssqlConfig.database,
      options: mssqlConfig.options,
    });
    await pool.initialize();
    const tableName = 'DoIT_SendSMS_NET';
    const result = await pool.query(
      `INSERT INTO ${tableName} (Mobile, sms_content) VALUES (@0, @1)`,
      [mobile, sms_content], // Array of values in order
    );
    await pool.destroy();
    return result;
  } catch (error) {
    console.error('Error sending SMS:', error);
    throw error;
  }
};
