import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import EntityModel from './entity.model';
import { Log } from './log.model';
import { User } from './user.model';

@Entity('types')
export class Type extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ type: 'nvarchar', length: 100, nullable: false })
  name?: string;

  @Column({ type: 'nvarchar', length: 200, nullable: true })
  desc?: string;

  @Column({ type: 'nvarchar', length: 50, nullable: false })
  scope?: string;

  @Column({ type: 'bit', nullable: false, default: 1 })
  status?: boolean;

  @Column({ type: 'nvarchar', length: 500, nullable: true })
  value?: string;

  @OneToMany(() => Log, (log) => log.type)
  logs?: Log[];

  @OneToMany(() => User, (user) => user.rankId)
  ranks?: User[];

  @OneToMany(() => User, (user) => user.positionId)
  positions?: User[];
}
