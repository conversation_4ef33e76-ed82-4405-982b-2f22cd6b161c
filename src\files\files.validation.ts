import Joi from 'joi';
import { IApiError } from 'src/types/validation';

export const jBodyPostUploadFiles = Joi.object({
  prefix: Joi.string().required(),
  type: Joi.string().required(),
});

export const jBodyDeleteFiles = Joi.object({
  ids: Joi.array().items(Joi.number()).required(),
});

export const jSlugInParams = Joi.object({
  slug: Joi.string().required(),
});

export const eSlugInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'Định dạng slug (Path) chưa chính xác',
  errors: { slug: 'slug Định dạng chọn chính xác' },
  statusCode: 400,
};

export const jBodyPutReportError = Joi.object({
  content: Joi.string().allow(null, null),
  slug: Joi.string().min(1),
  parentSlug: Joi.string().allow(null, null),
  order: Joi.number().min(1),
  name: Joi.string().min(1),
});
