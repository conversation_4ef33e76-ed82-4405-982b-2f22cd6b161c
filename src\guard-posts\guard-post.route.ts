import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  createGuardPost,
  deleteGuardPost,
  getGuardPost,
  getGuardPostWithOrg,
  searchGuardPosts,
  updateGuardPost,
} from './guard-post.controller';
import {
  eBodyGuardPost,
  eIdInParams,
  eSearchQuery,
  jBodyGuardPost,
  jIdInParams,
  jSearchGuardPost,
} from './guard-post.validation';

const router = express.Router();

router.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editGuardPost]),
  createValidator('body', jBodyGuardPost, eBodyGuardPost),
  createGuardPost,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editGuardPost]),
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyGuardPost, eBodyGuardPost),
  updateGuardPost,
);

router.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editGuardPost]),
  createValidator('params', jIdInParams, eIdInParams),
  deleteGuardPost,
);

router.get('/with-org', checkAuth, getGuardPostWithOrg);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewGuardPost]),
  createValidator('params', jIdInParams, eIdInParams),
  getGuardPost,
);

router.get(
  '/',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuardPost,
  //   RolePerms.viewGuardShiftManagement,
  //   RolePerms.viewGuardShift,
  // ]),
  createValidator('query', jSearchGuardPost, eSearchQuery),
  searchGuardPosts,
);

export default router;
