import { Organization } from '../../models';
import { database } from '../../config';
import * as fs from 'fs';
import path from 'path';
interface DonVi {
  id: string;
  tree_level: number;
  name: string;
  parent_id?: string;
  code: string;
  short_name: string;
  // [key: string]: any; // Nếu có các thuộc t<PERSON>h kh<PERSON>c
}
export const syncAllDonVi = async () => {
  try {
    const filePath = path.join(__dirname, '../donvi.json');
    const fileContent = fs.readFileSync(filePath, 'utf-8'); // Đọc nội dung file
    const staticData: DonVi[] = JSON.parse(fileContent);
    console.log('Initializing sync for DonVi');
    const donViRepo = database.getRepository(Organization);
    const sortedItems = staticData.sort(
      (a, b) => (Number(a.tree_level) || 0) - (Number(b.tree_level) || 0),
    );
    let updatedCount = 0;
    const createdCount = 0;
    //  console.log(`Đã lấy ${sortedItems.length} bản ghi cho DonVi`);
    console.log('Tiến hành xóa mềm tất cả các bản ghi trong bảng DonVi');
    await donViRepo.createQueryBuilder().softDelete().execute(); //Xóa mềm tất cả các bản ghi trong bảng
    let codeNull = 0;
    for (const item of sortedItems) {
      console.log(`Bản ghi số: ${sortedItems.indexOf(item) + 1}`);
      const existingRecord = await donViRepo.findOne({
        where: { id: item.id },
        withDeleted: true,
      });
      // lấy mpath fullname từ bảng DonVi
      let mpathFullName = item.short_name; // Mặc định là tên của bản ghi hiện tại
      if (item.parent_id) {
        let currentParentId = item.parent_id;
        const parentNames: string[] = [];
        // Truy xuất tất cả các cha
        while (currentParentId) {
          const parentRecord = await donViRepo.findOne({
            where: { id: currentParentId },
            withDeleted: true,
          });
          if (parentRecord) {
            parentNames.unshift(parentRecord?.shortName || ''); // Thêm tên cha vào đầu danh sách
            currentParentId = parentRecord.parentId ?? ''; // Tiếp tục với cha của cha
          } else {
            break; // Dừng nếu không tìm thấy cha
          }
        }
        mpathFullName = `${item.short_name}/${parentNames.reverse().join('/')}`;
      }
      let mpath = item.id; // Mặc định là tên của bản ghi hiện tại
      if (item.parent_id) {
        let currentParentId = item.parent_id;
        const parentNames: string[] = [];
        // Truy xuất tất cả các cha
        while (currentParentId) {
          const parentRecord = await donViRepo.findOne({
            where: { id: currentParentId },
            withDeleted: true,
          });
          if (parentRecord) {
            parentNames.unshift(parentRecord?.id || ''); // Thêm tên cha vào đầu danh sách
            currentParentId = parentRecord.parentId ?? ''; // Tiếp tục với cha của cha
          } else {
            break; // Dừng nếu không tìm thấy cha
          }
        }
        mpath = `${item.id}.${parentNames.reverse().join('.')}`;
      }
      console.log(`mpath: ${mpath}`);
      if (existingRecord) {
        console.log(`đang cập nhật bản ghi với id: ${item.id}`);
        await donViRepo.save(existingRecord);
        await donViRepo.update(
          { id: item.id },
          {
            ...existingRecord,
            name: item.name,
            fullText: item.name,
            parentId: item.parent_id ?? undefined,
            shortName: item.short_name,
            depth: item.tree_level,
            code: item.code ?? codeNull.toString(),
            mPathFullName: mpathFullName,
            mpath: mpath,
          },
        );
        updatedCount++;
      } else {
        console.log(`tạo mới bản ghi với id: ${item.id}`);
        if (item.parent_id) {
          const parent = await donViRepo.findOne({
            where: { id: item.parent_id },
            withDeleted: true,
          });
          if (!parent) {
            console.error(
              `Không tìm thấy bản ghi cha với id: ${item.parent_id}`,
            );
            continue; // Bỏ qua nếu không tìm thấy cha
          }
          await donViRepo.save(
            donViRepo.create({
              id: item.id,
              name: item.name,
              fullText: item.name,
              parentId: item.parent_id ?? undefined,
              // Gán bản ghi cha hợp lệ
              shortName: item.short_name,
              depth: item.tree_level,
              code: item.code ?? codeNull.toString(),
              mPathFullName: mpathFullName,
              mpath: mpath,
            }),
          );
        } else {
          await donViRepo.save(
            donViRepo.create({
              id: item.id,
              name: item.name,
              // parentId: undefined,
              fullText: item.name,
              shortName: item.short_name,
              depth: item.tree_level,
              code: item.code ?? codeNull.toString(),
              mPathFullName: mpathFullName,
              mpath: mpath,
            }),
          );
        }
      }
      codeNull++;
    }
    console.log(`- Cập nhật: ${updatedCount} bản ghi`);
    console.log(`- Tạo mới: ${createdCount} bản ghi`);
  } catch (error) {
    console.log('Error syncing DonVi:', error);
  }
};
export const syncDonVi = async () => {
  try {
    await syncAllDonVi();
  } catch (error) {
    console.error(`Lỗi khi xử lý DonVi`, error);
  }
};
