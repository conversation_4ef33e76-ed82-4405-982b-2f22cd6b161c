import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import {
  createError,
  deleteError,
  editError,
  findErrors,
  findError,
} from './errors.service';
import { ISearchQuery } from 'src/types/req';
import { IGetErrorsQuery } from './errors';
import { IApiError } from 'src/types/validation';

export const postError = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const error = req.body;
    const getObj = await createError(error);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const deleteErrorC = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const getObj = await deleteError(id);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      const error: IApiError = {
        code: ERROR.DATA_NOT_FOUND,
        message: 'Báo cáo lỗi không tồn tại',
        statusCode: status.BAD_REQUEST,
      };
      return res.status(status.BAD_REQUEST).json(error);
    } else if (e instanceof Error && e.message === ERROR.HANDLE_ERROR_EXISTED) {
      const error: IApiError = {
        code: ERROR.HANDLE_ERROR_EXISTED,
        message: 'Báo cáo lỗi đã được tiếp nhận',
        statusCode: status.BAD_REQUEST,
      };
      return res.status(status.BAD_REQUEST).json(error);
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const getErrors = async (
  req: Request & { query: ISearchQuery<IGetErrorsQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = await findErrors(req.query);
    return res.json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const putError = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const data = await editError(id, req.body);
    return res.json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getError = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { id } = req.params;
    const data = await findError(id);
    return res.json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
