import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  createEquipHandover,
  deleteEquipHandover,
  getAllEquipHandovers,
  getEquipHandover,
  updateEquipHandover,
} from './equip-handover.controller';
import {
  eCreateEquipHandover,
  eIdEquipHandover,
  eSearchEquipHandover,
  eUpdateEquipHandover,
  jCreateEquipHandover,
  jIdEquipHandover,
  jSearchEquipHandover,
  jUpdateEquipHandover,
} from './equip-handover.validation';

const router = express.Router();

router.post(
  '',
  checkAuth,
  // hasPerm([RolePerms.editEquipHandover]),
  createValidator('body', jCreateEquipHandover, eCreateEquipHandover),
  createEquipHandover,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewEquipHandover, RolePerms.viewSituationSummary]),
  createValidator('params', jIdEquipHandover, eIdEquipHandover),
  getEquipHandover,
);

router.get(
  '',
  checkAuth,
  // hasPerm([RolePerms.viewEquipHandover, RolePerms.viewSituationSummary]),
  createValidator('query', jSearchEquipHandover, eSearchEquipHandover),
  getAllEquipHandovers,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editEquipHandover]),
  createValidator('params', jIdEquipHandover, eIdEquipHandover),
  createValidator('body', jUpdateEquipHandover, eUpdateEquipHandover),
  updateEquipHandover,
);

router.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.deleteEquipHandover]),
  createValidator('params', jIdEquipHandover, eIdEquipHandover),
  deleteEquipHandover,
);

export default router;
