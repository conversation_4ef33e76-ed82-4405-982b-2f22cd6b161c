import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  OneToMany,
  ManyToOne,
  JoinC<PERSON>umn,
  BeforeInsert,
} from 'typeorm';
import EntityModel from './entity.model';
import { IdentifyCard } from './identifyCard.model';
import { Type } from './type.model';

@Entity('guests')
export class Guest extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    type: 'nvarchar',
    length: 250,
  })
  fullName?: string;

  @Column({
    name: 'date_of_birth',
    type: 'datetime2',
    nullable: true,
    default: null,
  })
  dateOfBirth?: Date;

  @Column({
    name: 'sex_id',
    type: 'int',
    nullable: true,
    default: null,
  })
  sexId?: number;
  @ManyToOne(() => Type)
  @JoinColumn({
    name: 'sex_id',
  })
  sex?: Type;

  @Column({
    type: 'nvarchar',
    length: 100,
    nullable: true,
    default: null,
  })
  nationality?: string;

  @Column({
    name: 'permanent_address',
    type: 'nvarchar',
    length: 500,
    nullable: true,
    default: null,
  })
  permanentAddress?: string;

  @Column({
    type: 'nvarchar',
    length: 250,
    nullable: true,
    default: null,
  })
  occupation?: string;

  @Column({
    name: 'phone_number',
    type: 'nvarchar',
    length: 20,
  })
  phoneNumber?: string;

  @Column({
    type: 'nvarchar',
    length: 250,
  })
  office?: string;

  @Column({
    type: 'text',
    nullable: true,
    default: null,
  })
  avatar?: string;

  @Column({
    type: 'nvarchar',
    length: 2000,
    nullable: true,
    default: null,
  })
  notes?: string;

  @OneToMany(() => IdentifyCard, (identifyCard) => identifyCard.guest)
  identifyCards?: IdentifyCard[];

  @BeforeInsert()
  beforeCreate() {
    const cols = [
      this.fullName,
      this.nationality,
      this.permanentAddress,
      this.occupation,
      this.phoneNumber,
      this.office,
    ];
    this.fullText = cols.join(' ')?.trim();
  }
}
