import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import {
  ChucVuLoaiHinhTruc,
  DMLoaiHinhTruc,
  DonviLoaiHinhTruc,
  LoaiHinhTrucChiTiet,
  User,
} from '../models';
import { ERROR } from '../utils/error';
import * as service from './dm-loai-hinh-truc.service';
import * as serviceDonViLoaiHinhTruc from './don-vi-loai-hinh-lich-truc.service';
import * as serviceChucVuLoaiHinhTruc from './chuc-vu-loai-hinh-lich-truc.service';
import * as serviceLoaiHinhTrucChiTiet from './loai-hinh-truc-chi-tiet.service';
export const createLoaiHinhTruc = async (
  req: Request & { body: DMLoaiHinhTruc },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await service.create(req.body, user);
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.DATA_EXISTED:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Mã loại trực đã tồn tại',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const createDonViLoaiHinhTruc = async (
  req: Request & { body: DonviLoaiHinhTruc } & { params: { id: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await serviceDonViLoaiHinhTruc.create(
      req.params.id.toString(),
      req.body,
      user,
    );
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.ORGANIZATION_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Mã đơn vị không tồn tại',
            statusCode: 400,
          });
        case ERROR.LOAI_HINH_TRUC_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Loại hình trực không tồn tại',
            statusCode: 400,
          });
        case ERROR.DATA_EXISTED:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Đã tồn tại đơn vị trong loại hình trực',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const createChucVuLoaiHinhTruc = async (
  req: Request & { body: ChucVuLoaiHinhTruc } & { params: { id: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await serviceChucVuLoaiHinhTruc.create(
      req.params.id.toString(),
      req.body,
      user,
    );
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.POSITION_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Chức vụ không tồn tại',
            statusCode: 400,
          });
        case ERROR.LOAI_HINH_TRUC_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Loại hình trực không tồn tại',
            statusCode: 400,
          });
        case ERROR.DATA_EXISTED:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Đã tồn tại đơn vị trong loại hình trực',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};
export const createLoaiHinhTrucChiTiet = async (
  req: Request & { body: LoaiHinhTrucChiTiet } & { params: { id: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await serviceLoaiHinhTrucChiTiet.create(
      req.params.id.toString(),
      req.body,
      user,
    );
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.LOAI_HINH_TRUC_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.DATA_EXISTED,
            message: 'Loại hình trực không tồn tại',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};
