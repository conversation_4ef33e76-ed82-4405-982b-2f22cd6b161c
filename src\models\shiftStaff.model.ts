import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { ShiftPost } from './shiftPost.model';
import { eQN } from './eQN.model';

@Entity('shiftStaff')
export class ShiftStaff extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'shift_post_id',
    type: 'int',
  })
  shiftPostId?: number;
  @ManyToOne(() => ShiftPost, (shiftPost) => shiftPost.shiftStaffs)
  @JoinColumn({ name: 'shift_post_id' })
  shiftPost?: ShiftPost;

  @Column({
    name: 'qneid',
    type: 'varchar',
    length: 36,
    default: null,
  })
  qneid?: string;
  @ManyToOne(() => eQN)
  @JoinColumn({ name: 'qneid' })
  eQN?: eQN;

  @Column({
    name: 'is_leader',
    type: 'bit',
    default: 0,
  })
  isLeader?: boolean;
}
