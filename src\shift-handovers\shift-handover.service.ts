import dayjs from 'dayjs';
import { database } from '../config';
import {
  eQN,
  GuardPost,
  ShiftHandover,
  ShiftPost,
  ShiftStaff,
} from '../models';
import { ISearchQuery } from '../types/req';
import { ERROR } from '../utils/error';
import { createLinks } from '../utils/pagination';
import {
  ICreateShiftHandover,
  ISearchShiftHandoverQuery,
} from './shift-handover';

function createFullText(shiftHandover: Partial<ShiftHandover>): string {
  const fields = [
    shiftHandover.createdAt,
    shiftHandover.guardPostId,
    shiftHandover.qneidSuperVisor,
    shiftHandover.taskContent,
    shiftHandover.taskContinue,
    shiftHandover.bookHandover,
    shiftHandover.equipmentHandover,
    shiftHandover.vehicleCountStart,
    shiftHandover.vehicleEntryCountDuring,
    shiftHandover.vehicleExitCountDuring,
    shiftHandover.vehicleCountHandover,
    shiftHandover.shiftNowId,
    shiftHandover.shiftFeatureId,
  ];
  return fields.filter(Boolean).join(' ');
}

export const create = async (data: ICreateShiftHandover) => {
  const repoHandover = database.getRepository(ShiftHandover);
  const repoGuardPost = database.getRepository(GuardPost);
  // const repoGuardShift = database.getRepository(GuardShift);
  const repoShiftPost = database.getRepository(ShiftPost);

  const shiftShow = await repoShiftPost.findOne({
    where: { id: data.shiftNowId },
  });
  const shiftFeature = await repoShiftPost.findOne({
    where: { id: data.shiftFeatureId },
  });

  const guardPost = await repoGuardPost.findOne({
    where: { id: data.guardPostId },
  });

  if (!shiftFeature || !shiftShow || !guardPost) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  const now = dayjs();

  const shiftNowExits = await repoHandover.findOne({
    where: {
      shiftNowId: data.shiftNowId,
      shiftFeatureId: data.shiftFeatureId,
      guardPostId: data.guardPostId,
    },
  });

  if (shiftNowExits) {
    throw new Error(ERROR.SHIFT_HANDOVER_EXISTS);
  }

  const handoverPayload = repoHandover.create({
    guardPostId: data.guardPostId,
    shiftNowId: data.shiftNowId,
    shiftFeatureId: data.shiftFeatureId,
    taskContent: data.taskContent,
    taskContinue: data.taskContinue,
    bookHandover: data.bookHandover,
    vehicleCountStart: data.vehicleCountStart,
    vehicleEntryCountDuring: data.vehicleEntryCountDuring,
    vehicleExitCountDuring: data.vehicleExitCountDuring,
    vehicleCountHandover: data.vehicleCountHandover,
    equipmentHandover: data.equipmentHandover,
    createdAt: now.toDate(),
    fullText: '',
    qneidSuperVisor: data.qneidSuperVisor,
  });

  handoverPayload.fullText = createFullText(handoverPayload);
  const saved = await repoHandover.save(handoverPayload);
  return { data: saved };
};

export const update = async (id: number, data: Partial<ShiftHandover>) => {
  const repo = database.getRepository(ShiftHandover);

  const existing = await repo.findOne({ where: { id } });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.update(id, data);
  return { message: 'Cập nhật thành công' };
};

export const remove = async (id: number) => {
  const repo = database.getRepository(ShiftHandover);
  const existing = await repo.findOne({ where: { id } });

  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.delete(id);
  return { message: 'Xóa thành công' };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(ShiftHandover);
  const handover = await repo.findOne({ where: { id } });

  if (!handover) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  return handover;
};

export const search = async (
  query: ISearchQuery<ISearchShiftHandoverQuery>,
) => {
  const repo = database.getRepository(ShiftHandover);

  const qb = repo
    .createQueryBuilder('handover')
    .leftJoinAndMapOne(
      'handover.guardPost',
      GuardPost,
      'guardPost',
      'guardPost.id = handover.guard_post_id',
    )
    .leftJoinAndMapOne(
      'handover.shiftNow',
      ShiftPost,
      'shiftNow',
      'shiftNow.id = handover.shift_now_id',
    )
    .leftJoinAndMapOne(
      'handover.shiftFeature',
      ShiftPost,
      'shiftFeature',
      'shiftFeature.id = handover.shift_feature_id',
    )

    .leftJoinAndSelect('shiftNow.guardShift', 'guardShiftNow')
    .leftJoinAndSelect('shiftNow.guardPost', 'guardPostNow')
    .leftJoinAndSelect('shiftNow.shiftStaffs', 'staff')
    .leftJoinAndSelect('staff.eQN', 'eQNSnow')

    .leftJoinAndSelect('shiftFeature.guardShift', 'guardShiftFeature')
    .leftJoinAndSelect('shiftFeature.guardPost', 'guardPostFeature')
    .leftJoinAndSelect('shiftFeature.shiftStaffs', 'staffFeature')
    .leftJoinAndSelect('staffFeature.eQN', 'eQNFeature');

  if (query.fromDate) {
    qb.andWhere('handover.createdAt >= :fromDate', {
      fromDate: query.fromDate,
    });
  }

  if (query.fullText) {
    qb.andWhere('handover.fullText LIKE :fullText', {
      fullText: `%${query.fullText}%`,
    });
  }

  if (
    query.createdAt &&
    Array.isArray(query.createdAt) &&
    query.createdAt.length === 2
  ) {
    const [start, end] = query.createdAt;
    if (start && end) {
      qb.andWhere('handover.createdAt BETWEEN :startDate AND :endDate', {
        startDate: dayjs(start).startOf('day').toDate(),
        endDate: dayjs(end).endOf('day').toDate(),
      });
    }
  }

  if (query.guardShiftName) {
    qb.andWhere('guardShiftNow.name LIKE :guardShiftName', {
      guardShiftName: `%${query.guardShiftName}%`,
    });
  }

  if (query.qneidSuperVisor) {
    qb.andWhere('handover.qneid_supervisor LIKE :qneidSuperVisor', {
      qneidSuperVisor: `%${query.qneidSuperVisor}%`,
    });
  }

  if (query.shiftNowName) {
    qb.andWhere(
      'EXISTS (' +
        qb
          .subQuery()
          .select('1')
          .from(ShiftStaff, 'sf')
          .leftJoin(eQN, 'su', 'su.id = sf.qneid')
          .where('sf.shift_post_id = shiftNow.id')
          .andWhere('su.fullName LIKE :shiftNowName')
          .getQuery() +
        ')',
      { shiftNowName: `%${query.shiftNowName}%` },
    );
  }

  if (query.shiftNowId) {
    qb.andWhere('handover.shift_now_id = :shiftNowId', {
      shiftNowId: query.shiftNowId,
    });
  }

  if (query.shiftFeatureName) {
    qb.andWhere(
      'EXISTS (' +
        qb
          .subQuery()
          .select('1')
          .from(ShiftStaff, 'sf')
          .leftJoin(eQN, 'su', 'su.id = sf.qneid')
          .where('sf.shift_post_id = shiftFeature.id')
          .andWhere('su.fullName LIKE :shiftFeatureName')
          .getQuery() +
        ')',
      { shiftFeatureName: `%${query.shiftFeatureName}%` },
    );
  }

  if (query.guardPostId) {
    qb.andWhere('handover.guard_post_id = :guardPostId', {
      guardPostId: query.guardPostId,
    });
  }

  if (query.toDate) {
    qb.andWhere('handover.createdAt <= :toDate', { toDate: query.toDate });
  }

  if (query.orgCode) {
    qb.andWhere('guardPost.codeOrg = :codeOrg', { codeOrg: query.orgCode });
  }

  if (query.manageOrgCode) {
    qb.andWhere('guardPost.manageOrgCode = :manageOrgCode', {
      manageOrgCode: query.manageOrgCode,
    });
  }
  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip)
    .take(limit)
    .orderBy('handover.createdAt', 'DESC')
    .addOrderBy('handover.id', 'ASC');
  const [data, total] = await qb.getManyAndCount();

  const links = createLinks(
    '/shift-handover/search?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};
