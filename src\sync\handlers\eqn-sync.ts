import { eQN, Organization } from '../../models';
import { database } from '../../config';
import * as fs from 'fs';
import path from 'path';
interface eqn {
  id: string;
  full_name: string;
  birth_date: Date;
  gender: boolean;
  cccd: string;
  type_id: string;
  org_id: string;
  phone_number: string;
  email: string;
  military_code: string;
  rank_id: string;
  position_id: string;
  order_type_code: string;
  status: string;
  // [key: string]: any; // Nếu có các thuộc t<PERSON>h khác
}
export const syncAlleQN = async () => {
  try {
    const filePath = path.join(__dirname, '../eqn.json');
    const fileContent = fs.readFileSync(filePath, 'utf-8'); // Đọc nội dung file
    const staticData: eqn[] = JSON.parse(fileContent);
    console.log('Initializing sync for EQN');
    const eQNRepo = database.getRepository(eQN);
    const donViRepo = database.getRepository(Organization);
    const sortedItems = staticData;
    let updatedCount = 0;
    const createdCount = 0;
    const typeId = 'SQ';
    const rankId = 'RANK001';
    const positionId = 'PC001';
    //  console.log(`Đã lấy ${sortedItems.length} bản ghi cho DonVi`);
    console.log('Tiến hành xóa mềm tất cả các bản ghi trong bảng eQN');
    await eQNRepo.createQueryBuilder().softDelete().execute(); //Xóa mềm tất cả các bản ghi trong bảng
    let codeNull = 0;
    let codeOrg: string | undefined; // Declare codeOrg variable
    for (const item of sortedItems) {
      console.log(`Bản ghi số: ${sortedItems.indexOf(item) + 1}`);
      const existingRecord = await eQNRepo.findOne({
        where: { id: item.id },
        withDeleted: true,
      });
      // lấy mpath fullname từ bảng DonVi
      console.log(`org_id: ${item.org_id}`);
      if (item.org_id) {
        const donVi = await donViRepo.findOne({
          where: { id: item.org_id },
          withDeleted: true,
        });
        console.log(`donVi: ${donVi}`);
        if (donVi) {
          codeOrg = donVi.code;
        } else {
          console.log(`Không tìm thấy bản ghi DonVi với id: ${item.org_id}`);
        }
      }
      if (existingRecord) {
        console.log(`đang cập nhật bản ghi với id: ${item.id}`);
        await eQNRepo.save(existingRecord);
        await eQNRepo.update(
          { id: item.id },
          {
            ...existingRecord,
            eQN: codeNull.toString(),
            fullName: item.full_name,
            shortName: item.full_name,
            // orgCode: item.org_id,
            orgCode: codeOrg,
            reinforcementOrgId: codeOrg,
            typeId: item.type_id,
            rankId: item.rank_id,
            statusId: item.status,
            positionId: item.position_id,
            phoneNumber: item.phone_number,
            isEnable: item.status === 'active' ? true : false,
            birthday: item.birth_date,
            cccd: item.cccd,
            gender: item.gender,
          },
        );
        updatedCount++;
      } else {
        console.log(`tạo mới bản ghi với id: ${item.id}`);
        if (!codeOrg) {
          console.error(
            `Không thể tạo bản ghi mới vì codeOrg không hợp lệ cho id: ${item.id}`,
          );
          continue; // Bỏ qua bản ghi nếu codeOrg không hợp lệ
        }
        await eQNRepo.save(
          eQNRepo.create({
            id: item.id,
            // eQN: item.id,
            eQN: codeNull.toString(),
            fullName: item.full_name,
            shortName: item.full_name,
            orgCode: codeOrg,
            reinforcementOrgId: codeOrg,
            typeId: item.type_id ?? typeId,
            rankId: item.rank_id ?? rankId,
            // rankId:  codeNull.toString(),
            statusId: item.status,
            positionId: item.position_id ?? positionId,
            phoneNumber: item.phone_number,
            isEnable: item.status === 'active' ? true : false,
            birthday: item.birth_date,
            cccd: item.cccd,
          }),
        );
      }
      codeNull++;
    }
    //console.log(`Đã xử lý ${staticData.length} bản ghi cho DonVi:`);
    console.log(`- Cập nhật: ${updatedCount} bản ghi`);
    console.log(`- Tạo mới: ${createdCount} bản ghi`);
  } catch (error) {
    console.log('Error syncing DonVi:', error);
  }
};
export const synceQN = async () => {
  try {
    await syncAlleQN();
  } catch (error) {
    console.error(`Lỗi khi xử lý DonVi`, error);
  }
};
