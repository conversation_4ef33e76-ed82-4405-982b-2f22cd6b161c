import { MigrationInterface, QueryRunner } from "typeorm";

export class T1753777022938 implements MigrationInterface {
    name = 'T1753777022938'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" DROP CONSTRAINT "FK_8b2509728952a07a078556704d6"`);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" DROP COLUMN "maLoaiHinhTruc"`);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" ADD "maLoaiHinhTruc" varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "short_name" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "status_id" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "phone_number" varchar(20) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "address_books" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "birth_place_ward_id" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "birthday" date NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "cccd" varchar(20) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "cccd_issued_ward_id" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "gender" bit NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "disabled_reason_id" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "identification" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "image" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "createdAt" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e" DEFAULT GETDATE() FOR "createdAt"`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "updatedAt" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910" DEFAULT GETDATE() FOR "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" ADD CONSTRAINT "FK_8b2509728952a07a078556704d6" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("ma") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" DROP CONSTRAINT "FK_8b2509728952a07a078556704d6"`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910" DEFAULT getdate() FOR "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "updatedAt" datetime2`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e" DEFAULT getdate() FOR "createdAt"`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "createdAt" datetime2`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "image" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "identification" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "disabled_reason_id" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "gender" bit`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "cccd_issued_ward_id" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "cccd" varchar(20)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "birthday" date`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "birth_place_ward_id" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "address_books" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "phone_number" varchar(20)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "status_id" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "short_name" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" DROP COLUMN "maLoaiHinhTruc"`);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" ADD "maLoaiHinhTruc" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "chucVuLoaiHinhTruc" ADD CONSTRAINT "FK_8b2509728952a07a078556704d6" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
