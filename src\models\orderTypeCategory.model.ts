import { Column, Entity, PrimaryColumn } from 'typeorm';

@Entity('order_type_categories')
export class OrderTypeCategory {
  @PrimaryColumn({
    type: 'varchar',
    length: 36,
  })
  id?: string;

  @Column({
    type: 'nvarchar',
    length: 50,
    nullable: false,
  })
  code?: string;

  @Column({
    name: 'date_created',
    type: 'datetime2',
    default: () => 'GETDATE()',
    nullable: true,
  })
  dateCreated?: Date;

  @Column({
    name: 'date_updated',
    type: 'datetime2',
    default: () => 'GETDATE()',
    nullable: true,
  })
  dateUpdated?: Date;

  @Column({
    name: 'has_days_on_road',
    type: 'bit',
    default: false,
    nullable: true,
  })
  hasDaysOnRoad?: boolean;

  @Column({
    name: 'is_absent',
    type: 'bit',
    default: false,
    nullable: true,
  })
  isAbsent?: boolean;

  @Column({
    name: 'is_enable',
    type: 'bit',
    default: true,
    nullable: true,
  })
  isEnable?: boolean;

  @Column({
    type: 'nvarchar',
    length: 255,
    nullable: true,
  })
  name?: string;

  @Column({
    name: 'order_number',
    type: 'int',
    nullable: true,
  })
  orderNumber?: number;

  @Column({
    name: 'verify_date',
    type: 'bit',
    default: false,
    nullable: true,
  })
  verifyDate?: boolean;
}
