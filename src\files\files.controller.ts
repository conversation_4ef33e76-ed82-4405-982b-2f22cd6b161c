import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import {
  deleteFiles as deleteFilesFunc,
  generateFileToken,
  uploadFiles,
} from './files.service';
import config, { dirUploadFile } from '../config';
import jwt from 'jsonwebtoken';
import { join, parse } from 'path';
import { cwd } from 'process';
import { readFile } from 'fs/promises';

export const postUploadFiles = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const files = req.files as Express.Multer.File[];
    const { type } = req.body;
    const userId = req?.user?.id || 0;
    const getObj = await uploadFiles(files, type, userId);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const deleteFiles = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { ids } = req.body;
    const getObj = await deleteFilesFunc(ids);
    return res.json(getObj);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: status.BAD_REQUEST,
      });
    }
    next(e);
  }
};

export const getFileToken = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const getObj = await generateFileToken(req.user);
    return res.json(getObj);
  } catch (e) {
    next(e);
  }
};

export const getFile = async (
  req: Request & { query: { key?: string; token?: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { key, token } = req.query;
    try {
      jwt.verify(token?.toString() || '', config.jwtSecretFile);
    } catch (_) {
      throw new Error(ERROR.AUTHENTICATION_ERROR);
    }
    const path = join(cwd(), dirUploadFile.dirUpload, key?.toString() || '');
    res.sendFile(path, (err) => {
      if (err) {
        res.status(404).send('File not found');
      }
    });
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.UNAUTHORIZED).json({
        code: ERROR.AUTHENTICATION_ERROR,
        message: e.message.toString(),
        statusCode: status.UNAUTHORIZED,
      });
    }
    next(e);
  }
};

export const downloadFileByRelativePath = async (
  req: Request & { query: { key?: string; token?: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const { key, token } = req.query;
    try {
      jwt.verify(token?.toString() || '', config.jwtSecretFile);
    } catch (_) {
      throw new Error(ERROR.AUTHENTICATION_ERROR);
    }
    const parsed = parse(key?.toString() || '');
    const path = join(cwd(), key?.toString() || '');
    const buffer = await readFile(path);

    if (parsed.ext === '.docx') {
      res.setHeader(
        'Content-Type',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      );
    }

    res.setHeader('Content-Disposition', 'attachment; filename=' + parsed.base);
    res.send(buffer);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.UNAUTHORIZED).json({
        code: ERROR.AUTHENTICATION_ERROR,
        message: e.message.toString(),
        statusCode: status.UNAUTHORIZED,
      });
    }
    next(e);
  }
};
