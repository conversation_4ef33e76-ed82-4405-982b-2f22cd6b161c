import { Column, Entity, PrimaryColumn, DeleteDateColumn } from 'typeorm';

@Entity('military_types')
export class MilitaryType {
  @PrimaryColumn({ type: 'varchar', length: 36 })
  id?: string;

  @Column({
    name: 'date_created',
    type: 'datetime2',
    default: () => 'GETDATE()',
    nullable: true,
  })
  dateCreated?: Date;

  @Column({
    name: 'date_updated',
    type: 'datetime2',
    default: () => 'GETDATE()',
    nullable: true,
  })
  dateUpdated?: Date;

  @Column({ name: 'is_enable', type: 'bit', default: true, nullable: true })
  isEnable?: boolean;

  @Column({ type: 'nvarchar', length: 255, nullable: true })
  name?: string;

  @Column({ name: 'short_name', type: 'nvarchar', length: 50, nullable: false })
  shortName?: string;

  @Column({ name: 'order_number', type: 'int', nullable: true })
  orderNumber?: number;

  @DeleteDateColumn()
  deletedAt?: Date;
}
