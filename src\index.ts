import dotenv from 'dotenv-safe';
dotenv.config({
  allowEmptyValues: true,
});
import config from './config';

import app from './app';
import log from './utils/log';
import { database } from './config';
import { initIdReaderWebSocket } from './id-reader';

const port = config.port;
database
  .initialize()
  .then(() => {
    log.info('Connected to oracle db');

    app.listen(port, () => {
      log.info('Server started at port', port);

      // Initialize ID reader WebSocket connection
      initIdReaderWebSocket();
    });
  })
  .catch((err) => {
    log.error(err);
    return;
  });
