import dayjs from 'dayjs';

export const eachDayOfInterval = (
  startDate: Date | string,
  endDate: Date | string,
) => {
  const days = [];
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  for (let day = start; day <= end; day = day.add(1, 'day')) {
    days.push(day);
  }
  return days;
};

export const eachMonthOfInterval = (
  startDate: Date | string,
  endDate: Date | string,
) => {
  const months = [];
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  for (let month = start; month <= end; month = month.add(1, 'month')) {
    months.push(month);
  }
  return months;
};
