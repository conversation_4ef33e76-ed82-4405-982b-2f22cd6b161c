import { MigrationInterface, QueryRunner } from 'typeorm';

export class T1753698214480 implements MigrationInterface {
  name = 'T1753698214480';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" ADD "mpath" nvarchar(255) CONSTRAINT "DF_f505e3de3e9c32dd6df8ec72a45" DEFAULT ''`,
    );
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" DROP COLUMN "parentId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" ADD "parentId" uniqueidentifier`,
    );
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" ADD CONSTRAINT "FK_74f088e98f4f13138e7e72fcafe" FOREIGN KEY ("parentId") REFERENCES "dm_loai_hinh_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" DROP CONSTRAINT "FK_74f088e98f4f13138e7e72fcafe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" DROP COLUMN "parentId"`,
    );
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" ADD "parentId" nvarchar(255)`,
    );
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" DROP CONSTRAINT "DF_f505e3de3e9c32dd6df8ec72a45"`,
    );
    await queryRunner.query(
      `ALTER TABLE "dm_loai_hinh_truc" DROP COLUMN "mpath"`,
    );
  }
}
