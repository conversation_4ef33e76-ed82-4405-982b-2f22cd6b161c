import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import * as timesheetService from './timesheet.service';
import { ITimesheetListQuery } from './timesheet.dto';

export const getDashboardStats = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { startDate, endDate, orgCode, eQNId } = req.query;
    const user = req.user;
    // Validate date range
    if (
      (startDate && !Date.parse(startDate as string)) ||
      (endDate && !Date.parse(endDate as string))
    ) {
      return res.status(status.BAD_REQUEST).json({
        part: 'query',
        code: ERROR.BAD_REQUEST,
        message: 'Invalid date format',
        statusCode: 400,
      });
    }

    const result = await timesheetService.getDashboardStats(
      {
        startDate: startDate as string,
        endDate: endDate as string,
        orgCode: orgCode as string,
        eQNId: eQNId as string,
      },
      user,
    );

    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'query',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getAttendanceReport = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { startDate, endDate, orgCode, eQNId } = req.query;

    const result = await timesheetService.getAttendanceReport({
      startDate: startDate as string,
      endDate: endDate as string,
      orgCode: orgCode as string,
      eQNId: eQNId as string,
    });

    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'query',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const createAttendance = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = req.body;
    const result = await timesheetService.createAttendanceLog(data);
    return res.status(201).json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getDashboardDetailByEQN = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const { startDate, endDate, eQNId } = req.query;
    const user = req.user;

    // Validate date range
    if (
      (startDate && !Date.parse(startDate as string)) ||
      (endDate && !Date.parse(endDate as string))
    ) {
      return res.status(status.BAD_REQUEST).json({
        part: 'query',
        code: ERROR.BAD_REQUEST,
        message: 'Invalid date format',
        statusCode: 400,
      });
    }

    const result = await timesheetService.getDashboardDetailByEQN(
      {
        startDate: startDate as string,
        endDate: endDate as string,
        eQNId: eQNId as string,
      },
      user,
    );

    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'query',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const createAttendanceReason = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = req.body;
    const result = await timesheetService.createAttendanceReason(data);
    return res.status(201).json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getAttendanceList = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const query = {
      ...req.query,
      limit: Number(req.query.limit) || 10,
      page: Number(req.query.page) || 1,
    };
    const result = await timesheetService.getAttendanceList(
      query as ITimesheetListQuery,
      req.user,
    );

    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'query',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const docxAttendanceList = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const {
      startDate,
      endDate,
      orgCode,
      status,
      location,
      checkTypes,
      limit = 10,
      page = 1,
    } = req.query;

    const result = await timesheetService.exportDocxAttendanceList({
      startDate: startDate as string,
      endDate: endDate as string,
      orgCode: orgCode as string,
      status: status as string,
      location: location as string,
      checkTypes: checkTypes as string,
      limit: parseInt(limit as string),
      page: parseInt(page as string),
    });

    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'query',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
