export enum ERROR {
  BAD_REQUEST = 'BAD_REQUEST',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  DATA_NOT_FOUND = 'DATA_NOT_FOUND',
  DATA_EXISTED = 'DATA_EXISTED',
  DATA_ERROR = 'DATA_ERROR',
  NO_DATA = 'NO_DATA',
  NO_SAME = 'NO_SAME',
  NO_OBJECT_SAME = 'NO_OBJECT_SAME',
  SHIFT_HANDOVER_EXISTS = 'SHIFT_HANDOVER_EXISTS',
  DATA_HAS_CHILD = 'DATA_HAS_CHILD',

  HANDLE_ERROR_EXISTED = 'HANDLE_ERROR_EXISTED',

  INVALID_DATE = 'INVALID_DATE',

  ORGANIZATION_NOT_FOUND = 'ORGANIZATION_NOT_FOUND',

  GUEST_LOG_NOT_FOUND = 'GUEST_LOG_NOT_FOUND',
  PRE_REGISTERED_GUEST_NOT_FOUND = 'PRE_REGISTERED_GUEST_NOT_FOUND',
  GUARD_POST_NOT_FOUND = 'GUARD_POST_NOT_FOUND',
  PENDING_GUEST_NOT_FOUND = 'PENDING_GUEST_NOT_FOUND',
  PARENT_GUEST_LOG_NOT_FOUND = 'PARENT_GUEST_LOG_NOT_FOUND',
  MEET_USER_NOT_FOUND = 'MEET_USER_NOT_FOUND',
  GUEST_ORGANIZATION_NOT_FOUND = 'GUEST_ORGANIZATION_NOT_FOUND',
  GUEST_TYPE_NOT_FOUND = 'GUEST_TYPE_NOT_FOUND',
  IDENTIFY_DOCUMENT_TYPE_NOT_FOUND = 'IDENTIFY_DOCUMENT_TYPE_NOT_FOUND',
  PURPOSE_CATEGORY_NOT_FOUND = 'PURPOSE_CATEGORY_NOT_FOUND',
  VEHICLE_TYPE_NOT_FOUND = 'VEHICLE_TYPE_NOT_FOUND',
  VEHICLE_CLASSIFICATION_NOT_FOUND = 'VEHICLE_CLASSIFICATION_NOT_FOUND',
  CONFIG_SHIFT_NOT_FOUND = 'CONFIG_SHIFT_NOT_FOUND',
  EQN_NOT_FOUND = 'EQN_NOT_FOUND',

  POSITION_NOT_FOUND = 'POSITION_NOT_FOUND',
  LOAI_HINH_TRUC_NOT_FOUND = 'LOAI_HINH_TRUC_NOT_FOUND',
}
