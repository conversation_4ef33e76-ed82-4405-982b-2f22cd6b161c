import Joi from 'joi';
import { IApiError } from '../types/validation';

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jBodyConfigShift = Joi.object({
  codeOrg: Joi.string().max(100).required(),
  orderNum: Joi.number().required(),
  startTime: Joi.string().required(),
  endTime: Joi.string().required(),
  id: Joi.number(),
});

const eBodyConfigShift: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const jSearchConfigShift = Joi.object({
  startTime: Joi.string(),
  endTime: Joi.string(),
  codeOrg: Joi.string(),
  orderNum: Joi.number(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
  ids: Joi.array().items(Joi.number()),
});

const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export {
  jIdInParams,
  eIdInParams,
  jBodyConfigShift,
  eBodyConfigShift,
  jSearchConfigShift,
  eSearchQuery,
};
