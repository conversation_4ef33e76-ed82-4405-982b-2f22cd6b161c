export type ISearchEQNQuery = {
  start_date?: string;
  end_date?: string;
  name?: string;
  typeId?: number;
  codeOrg?: string;
  limit: number;
  page: number;
};

export type IListEQNQuery = {
  rankId?: string;
  typeId?: string;
  orgCode?: string;
  positionId?: number;
  fullText?: string;
  limit: number;
  page: number;
};

export interface ITimesheetQuery {
  startDate?: string;
  endDate?: string;
  orgCode?: string;
  eQNId?: string;
}

export interface ICreateAttendanceLog {
  eQNId: string;
  dateUTC: string;
  checkOutTime?: string;
  status: 'granted' | 'denied';
  deviceId?: string;
  deviceName?: string;
  type?: number;
  biometricData?: string;
  attendanceImage?: string;
}

export interface ITimesheetDetailQuery {
  startDate?: string;
  endDate?: string;
  eQNId?: string;
}

export interface ICreateAttendanceReason {
  eQNId: string;
  reason: string;
  date: string;
}

export interface ITimesheetListQuery {
  startDate?: string;
  endDate?: string;
  orgCode?: string;
  status?: string;
  location?: string;
  checkTypes?: string;
  limit: number;
  page: number;
  keyword?: string;
}
