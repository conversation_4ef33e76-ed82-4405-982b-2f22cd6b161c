import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import {
  findOrganizationById,
  insertOrganization,
  getAll,
  findTreeByCode,
  findAllOrgTreeOfRole,
  searchTreeOrganizationOfRole,
  deleteOrganization,
  putOrganization,
  findAllOrgTreeOfManager,
  searchTreeOrganizationOfManager,
} from './organization.service';
import { Organization } from '../models';
import { ISearchQuery } from '../types/req';
import { ISearchOrganizationsQuery } from './organization';

export const getAllFlatOrganization = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const organization = await getAll();
    return res.json(organization);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getOrganizationById = async (
  req: Request & { params: { id: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = req.params.id;
    const organization = await findOrganizationById(id);
    if (organization) return res.json(organization);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Organization không tồn tại',
        statusCode: 400,
        errors: { field: 'code', message: 'Organization không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getSearchOrganizationsOfRole = async (
  req: Request & { query: ISearchQuery<ISearchOrganizationsQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const organizations = await searchTreeOrganizationOfRole(
      req.query,
      req.user,
    ); //Theo thuật toán cũ
    return res.json(organizations);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const postOrganization = async (
  req: Request & { body: Organization },
  res: Response,
  next: NextFunction,
) => {
  try {
    const organization: Organization = req.body;
    const postOrganization = await insertOrganization(organization);

    if (postOrganization.error)
      return res.status(status.BAD_REQUEST).json(postOrganization.error);
    return res.json(postOrganization.newOrganization);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const updateOrganization = async (
  req: Request & { body: Organization } & { params: { id: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const organization: Organization = req.body;
    const result = await putOrganization(req.params.id, organization);

    if (result && result.error)
      return res.status(status.BAD_REQUEST).json(result.error);
    return res.json(result.newOrganization);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Không tìm thấy dữ liệu',
        statusCode: 400,
        errors: { field: 'id', message: 'Không tìm thấy dữ liệu' },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const dropOrganization = async (
  req: Request & { params: { id: string } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const postOrganization = await deleteOrganization(req.params.id);

    return res.json(postOrganization);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Không tìm thấy dữ liệu',
        statusCode: 400,
        errors: { field: 'id', message: 'Không tìm thấy dữ liệu' },
      });
    } else if (e instanceof Error && e.message === ERROR.DATA_EXISTED) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Đơn vị tồn tại đơn vị con',
        statusCode: 400,
        errors: { field: 'id', message: 'Đơn vị tồn tại đơn vị con' },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const getAllOfRole = async (
  req: Request & { query: ISearchQuery<ISearchOrganizationsQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const organizations = await findAllOrgTreeOfRole(req.query, req.user);

    return res.json(organizations);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getTreeByCode = async (
  req: Request & { params: { code: string } } & { query: { depth?: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const organization = req.query.depth
      ? await findTreeByCode(req.params.code, Number(req.query.depth))
      : await findTreeByCode(req.params.code);
    return res.json(organization);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Không tìm thấy dữ liệu',
        statusCode: 400,
        errors: { field: 'code', message: 'Không tìm thấy dữ liệu' },
      });
    }
    next(e);
  }
};

export const getAllOfManager = async (
  req: Request & { query: ISearchQuery<ISearchOrganizationsQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const organizations = await findAllOrgTreeOfManager(req.query);

    return res.json(organizations);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const getSearchOrganizationsOfManager = async (
  req: Request & { query: ISearchQuery<ISearchOrganizationsQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const organizations = await searchTreeOrganizationOfManager(req.query); //Theo thuật toán cũ
    return res.json(organizations);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
