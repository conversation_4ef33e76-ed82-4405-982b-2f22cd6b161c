import queryString from 'query-string';
import { ISearchQuery } from '../types/req';

export const DEFAULT_LIMIT = 10;
export const DEFAULT_LIMIT_TOP = 10;
export const DEPTH = 3;

const createLinks = <T>(
  path: string,
  query: ISearchQuery<T>,
  page: number,
  totalPages: number,
) => {
  const linkQuery = { ...query };
  delete linkQuery.page;
  const first = path + queryString.stringify(linkQuery);

  linkQuery.page = page + 1;
  const next = page < totalPages ? path + queryString.stringify(linkQuery) : '';
  linkQuery.page = page - 1;
  const previous =
    page === 2
      ? first
      : page > 1
        ? path + queryString.stringify(linkQuery)
        : '';
  linkQuery.page = totalPages;
  const last =
    totalPages === 1 ? first : path + queryString.stringify(linkQuery);

  return { first, next, previous, last };
};
const limitPage = (curLimit?: number, curPage?: number) => {
  const limit: number = curLimit ? Number(curLimit) : DEFAULT_LIMIT;
  const page: number = curPage ? Number(curPage) : 1;
  const skip: number = (page - 1) * limit;

  return { limit, page, skip };
};
const createTreeLinks = <T>(
  path: string,
  query: ISearchQuery<T>,
  skip: number,
  pre: number,
  remain: number,
) => {
  const linkQuery = { ...query };
  linkQuery.skip = 0;
  const first = path + queryString.stringify(linkQuery);

  linkQuery.skip = skip;
  const next = remain > 0 ? path + queryString.stringify(linkQuery) : '';

  linkQuery.skip = pre;
  const previous = path + queryString.stringify(linkQuery);

  return { first, next, previous };
};

export { createLinks, limitPage, createTreeLinks };
