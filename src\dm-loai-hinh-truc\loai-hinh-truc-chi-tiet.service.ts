import { insertLog } from '../logs/logs.service';
import { database } from '../config';
import { DMLoaiHinhTruc, LoaiHinhTrucChiTiet, User } from '../models';
import { ERROR } from '../utils/error';
import { LOG } from '../constants';

export const create = async (
  idLoaiTruc: string,
  data: LoaiHinhTrucChiTiet,
  user: User,
) => {
  const repo = database.getRepository(LoaiHinhTrucChiTiet);
  const loaiHinhTrucRepo = database.getRepository(DMLoaiHinhTruc);

  const loaiHinhTruc = await loaiHinhTrucRepo.findOne({
    where: { id: idLoaiTruc },
    withDeleted: true,
  });
  if (!loaiHinhTruc) {
    throw new Error(ERROR.LOAI_HINH_TRUC_NOT_FOUND);
  }
  console.log('newObj:', { ...data, maLoaiHinhTruc: loaiHinhTruc.ma });
  const newObj = repo.create({ ...data, maLoaiHinhTruc: loaiHinhTruc.ma });

  const saved = await repo.save(newObj);
  await insertLog({
    ...(user && {
      content: `Tạo mới chi tiết loại hình lịch trực: ${saved.nhiemVu} - ${saved.dMLoaiHinhTruc?.ma}`,
    }),
    ip: '127.0.0.1',
    ...(user && { userId: user.id }),
    typeId: LOG.CREATE,
    createdAt: new Date(),
    updatedAt: new Date(),
  });
  return { data: saved };
};
