import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  createGuardSchedule,
  createGuardShifts,
  deleteGuardPostInGuardSchedule,
  deleteGuardShifts,
  deleteShiftPosts,
  deleteShiftStaff,
  duplicateGuardSchedule,
  getGuardSchedule,
  getGuardShifts,
  getShiftPosts,
  getShiftStaff,
  searchGuardShifts,
  updateGuardShifts,
  updateShiftPosts,
  updateShiftStaff,
} from './guard-shifts.controller';
import {
  eBodyGuardSchedule,
  eBodyGuardShifts,
  eBodyShiftPosts,
  eBodyShiftStaff,
  eGuardPostInParams,
  eIdInParams,
  eSearchQuery,
  eShiftStaffInParams,
  jBodyDeleteGuardPostInGuardSchedule,
  jBodyDuplicateGuardSchedule,
  jBodyGuardSchedule,
  jBodyGuardShifts,
  jBodyShiftPosts,
  jBodyShiftStaff,
  jGuardPostInParams,
  jGuardScheduleInParams,
  jIdInParams,
  jSearchGuardShifts,
  jShiftPostsInParams,
  jShiftStaffInParams,
} from './guard-shifts.validation';

const router = express.Router();

router.get(
  '/guard-schedule/',
  checkAuth,
  // hasPerm([RolePerms.viewGuardShift, RolePerms.viewGuardShiftManagement]),
  createValidator('query', jGuardScheduleInParams, eSearchQuery),
  getGuardSchedule,
);

router.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift]),
  createValidator('body', jBodyGuardShifts, eBodyGuardShifts),
  createGuardShifts,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift]),
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyGuardShifts, eBodyGuardShifts),
  updateGuardShifts,
);

router.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift]),
  createValidator('params', jIdInParams, eIdInParams),
  deleteGuardShifts,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewGuardShift]),
  createValidator('params', jIdInParams, eIdInParams),
  getGuardShifts,
);

router.get(
  '/',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewGuardShift,
  //   RolePerms.viewShiftHandover,
  //   RolePerms.viewShiftInspection,
  //   RolePerms.viewGuestOrganizationInDuty,
  //   RolePerms.viewTongHopTHTrucBan,
  //   RolePerms.vehicleInOut,
  // ]),
  createValidator('query', jSearchGuardShifts, eSearchQuery),
  searchGuardShifts,
);

router.get(
  '/:guardShiftId/shift-posts/:postId',
  checkAuth,
  // hasPerm([RolePerms.viewGuardShift]),
  createValidator('params', jShiftPostsInParams, eSearchQuery),
  getShiftPosts,
);

router.put(
  '/:guardShiftId/shift-posts/:postId',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift]),
  createValidator('params', jShiftPostsInParams, eIdInParams),
  createValidator('body', jBodyShiftPosts, eBodyShiftPosts),
  updateShiftPosts,
);

router.delete(
  '/:guardShiftId/shift-posts/:postId',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift]),
  createValidator('params', jShiftPostsInParams, eIdInParams),
  deleteShiftPosts,
);

router.get(
  '/:guardShiftId/shift-posts/:postId/shift-staff/:qneid',
  checkAuth,
  // hasPerm([RolePerms.viewGuardShift]),
  createValidator('params', jShiftStaffInParams, eSearchQuery),
  getShiftStaff,
);

router.put(
  '/:guardShiftId/shift-posts/:postId/shift-staff/:qneid',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift]),
  createValidator('params', jShiftStaffInParams, eShiftStaffInParams),
  createValidator('body', jBodyShiftStaff, eBodyShiftStaff),
  updateShiftStaff,
);

router.delete(
  '/:guardShiftId/shift-posts/:postId/shift-staff/:qneid',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift]),
  createValidator('params', jShiftStaffInParams, eBodyShiftStaff),
  deleteShiftStaff,
);

router.post(
  '/guard-schedule',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift, RolePerms.editGuardShiftManagement]),
  createValidator('body', jBodyGuardSchedule, eBodyGuardSchedule),
  createGuardSchedule,
);

router.post(
  '/guard-schedule/duplicate',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift, RolePerms.editGuardShiftManagement]),
  createValidator('body', jBodyDuplicateGuardSchedule, eBodyGuardSchedule),
  duplicateGuardSchedule,
);

router.delete(
  '/guard-schedule/guard-post/:guardPostId',
  checkAuth,
  // hasPerm([RolePerms.editGuardShift, RolePerms.editGuardShiftManagement]),
  createValidator('params', jGuardPostInParams, eGuardPostInParams),
  createValidator(
    'body',
    jBodyDeleteGuardPostInGuardSchedule,
    eBodyGuardSchedule,
  ),
  deleteGuardPostInGuardSchedule,
);

export default router;
