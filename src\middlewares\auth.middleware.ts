import { status } from 'http-status';
import { NextFunction, Request, Response } from 'express';
import jwt from 'jsonwebtoken';
import config from '../config';
import * as userService from '../users/users.service';
import { Permission, RolePermission } from '../models';
export const checkAuth = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) {
    return res.sendStatus(status.UNAUTHORIZED);
  }
  const token = authHeader.split(' ')[1];
  try {
    const payload = jwt.verify(token, config.jwtsecret) as unknown as {
      id: number;
      username: string;
      ip: string;
      orgCode: string;
      manageOrgCode: string;
    };
    const user = await userService.getAllInfoById(payload.id);
    let rolePermissions: RolePermission[] = [];
    let permissions: Permission[] = [];

    if (user && user.role) {
      rolePermissions = [...user.role.rolePermissions];
      if (rolePermissions.length > 0) {
        permissions = rolePermissions.map((item) => item.permission);
      }
    }

    req.user = user;
    req.permissions = permissions;
    req.permsArr = permissions
      .map((item) => item.resource)
      .filter((r): r is string => r !== undefined);
    req.role = user.role;
    req.ipAddress = payload.ip;

    next();
  } catch (_) {
    return res.status(status.UNAUTHORIZED).json({
      message: 'Bạn không chưa đăng nhập',
      statusCode: status.UNAUTHORIZED,
    });
  }
};
