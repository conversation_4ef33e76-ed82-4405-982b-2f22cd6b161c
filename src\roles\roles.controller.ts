import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import log from '../utils/log';
import { Role } from '../models';
import { ISearchQuery } from '../types/req';
import { ERROR } from '../utils/error';
import { IGetRolesQuery } from './roles';
import {
  addPermissions2Role,
  addUser2Role,
  changeStatusRoles,
  dropRole,
  dropUser2Role,
  find,
  findById,
  insertRole,
  removePermissions2Role,
  updateRole,
} from './roles.service';

export const search = async (
  req: Request & { query: ISearchQuery<IGetRolesQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const urlApi = await find(req.query);
    return res.json(urlApi);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const postRole = async (
  req: Request & { body: Role },
  res: Response,
  next: NextFunction,
) => {
  try {
    const role: Role = req.body;
    const postRole = await insertRole(role);
    if (postRole.error) {
      return res.status(status.BAD_REQUEST).json(postRole.error);
    }
    return res.json(postRole.newRole);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const putRole = async (
  req: Request & { body: Partial<Role> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const role: Role = req.body;
    const id = Number(req.params.id);
    log.debug('id role:', id);
    const putRole = await updateRole(id, role);
    if (putRole.error)
      return res.status(status.BAD_REQUEST).json(putRole.error);
    return res.json(putRole.updated);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const putStatusRole = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = Number(req.params.id);
    const putRole = await dropRole(id);
    return res.json(putRole.deleted);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getById = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = Number(req.params.id);
    const urlApi = await findById(id);
    return res.json(urlApi);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const putPermission2Role = async (
  req: Request & { params: { id: number } } & { body: number[] },
  res: Response,
  next: NextFunction,
) => {
  try {
    const roleId = Number(req.params.id);
    const permissionsId = req.body.map((value: string | number) =>
      Number(value),
    );
    const role = await addPermissions2Role(roleId, permissionsId);
    return res.json(role);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deletePermission2Role = async (
  req: Request & { params: { id: number } } & {
    body: { permissionsId: number[] };
  },
  res: Response,
  next: NextFunction,
) => {
  try {
    const roleId = Number(req.params.id);
    const permissionsId = req.body.map((value: string | number) =>
      Number(value),
    );
    const result = await removePermissions2Role(roleId, permissionsId);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const updateStatusRoles = async (
  req: Request & { body: number[] },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = await changeStatusRoles(req.body);
    return res.json(user);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const putUsers2Role = async (
  req: Request & { body: number[] } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = await addUser2Role(Number(req.params.id), req.body);
    return res.json(user);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const removeUsers2Role = async (
  req: Request & { body: number[] } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = await dropUser2Role(Number(req.params.id), req.body);
    return res.json(user);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Role không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
