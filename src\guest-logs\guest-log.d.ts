export type ICreateGuestLogBody = {
  parentId?: number;
  pendingGuestId?: number;

  //guestLogData
  guestCodeOrg: string;
  meetWhoId: string;
  meetOrgCode: string;
  vehicleTypeId: number;
  guestCardId: string;
  checkInTime: Date;
  checkOutTime?: Date;
  estimatedOutTime?: Date;
  guestTypeId: number;
  purposeOfVisit: string;
  purposeCategoryId: number;
  statusId: number;
  isCivil?: boolean;
  groupName?: string;
  leaderName?: string;
  leaderPosition?: string;
  numberOfVisitors?: number;
  numberOfVehicles?: number;
  cardId?: string;
  notes?: string;
  guest: {
    id: number;
    fullName: string;
    dateOfBirth: Date;
    phoneNumber: string;
    guestCodeOrg: string;
    guestOrganizationName: string;
    avatar?: string;
    sexId?: number;
    notes?: string;
    office?: string;
    occupation?: string;
    permanentAddress?: string;
    nationality?: string;
    identifyCard: {
      documentTypeId: number;
      identificationNumber: string;
      issueDate: Date;
      expiryDate: Date;
      issuingAuthority: string;
      notes: string;
      images: string[];
    };
  };
  guestIdBy: string;
  isSendMessageForMeetUser?: boolean;
};

export type IWebhookGuestLogBody = {
  idVehicleLog?: string; // id log của hệ thống camera
  defaultImage: string; // link default image
  guestCardId?: string; // mã thẻ khách
  licensePlate?: string; // biển số xe
  vehicleTypeValue?: string; // loại xe
  vehicleClassificationValue?: string; // phân loại xe
  commodity: string; // hàng hóa
  qneId?: string; // mã quân nhân
  etiquette?: string; // lễ tiết tác phong
  status: 'in' | 'out'; // trạng thái
  guardPostId: number; // vị trí chòi gác
  codeOrg: string; // mã đơn vị
};

export type ISearchPendingGuestLogQuery = {
  search: string;
  inDateFrom: Date;
  inDateTo: Date;
  createdAt?: string[];
  guestCardId: string;
  guardPostIds: number;
  licensePlate: string;
  vehicleTypeIds: number[];
  vehicleClassificationIds: number[];
};

export type ISearchGuestLogQuery = {
  search: string;
  inDateFrom: Date;
  inDateTo: Date;
  outDateFrom: Date;
  outDateTo: Date;
  guestName: string;
  guestIdentityTypeId: number;
  meetWhoId: string;
  meetOrgCode: string;
  guestTypeIds: number[];
  purposeCategoryIds: number[];
  statusIds: number[];
  vehicleTypeIds: number[];
  isInDuty: string;
  vehicleClassificationIds: number[];
  guestCardId: string;
  identificationNumber: string;
};

export type IGuestLogGroup = {
  page: number;
  limit: number;
  isInDuty: string;
  statusId: number;
  isPendingGuest: string;
} & ISearchGuestLogQuery &
  ISearchPendingGuestLogQuery;

export type IGuestLogStatsByOrgCodeQuery = {
  orgCode?: string;
  startDate: Date;
  endDate: Date;
};

export type IGuestLogStatsByOrgCodeResult = {
  orgId?: string;
  orgCode?: string;
  orgName?: string;
  totalGuests?: number;
  guestsOut?: number;
  guestsRemaining?: number;
};

export type IGuestLogHistoryInOutQuery = {
  orgCode?: string;
  startDate: Date;
  endDate: Date;
  page?: number;
  limit?: number;
};
