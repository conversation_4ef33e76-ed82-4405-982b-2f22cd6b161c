import { Column, <PERSON><PERSON>ty, OneToMany, PrimaryGeneratedColumn } from 'typeorm';

import EntityModel from './entity.model';
import { User } from './user.model';
import { RolePermission } from './rolePermission.model';
import { LevelData } from 'src/constants';

@Entity('roles')
export class Role extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ type: 'nvarchar', length: 100, nullable: false })
  name?: string;

  @Column({ type: 'nvarchar', length: 500, nullable: true })
  desc?: string;

  @Column({ type: 'bit', nullable: false, default: true })
  status?: boolean;

  @Column({ type: 'nvarchar', length: 100, nullable: true })
  levelData?:
    | LevelData.ALL
    | LevelData.SELF
    | LevelData.DIVISION
    | LevelData.REGION;

  @OneToMany(() => User, (user) => user.role)
  public users!: User[];

  @OneToMany(() => RolePermission, (rolePermission) => rolePermission.role)
  public rolePermissions!: RolePermission[];
}
