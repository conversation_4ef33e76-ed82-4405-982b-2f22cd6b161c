import Joi from 'joi';
import { IApiError } from '../types/validation';

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

const jIdVehicleLogInParams = Joi.object({
  id: Joi.string().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const eIdVehicleLogInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'id không hợp lệ',
  errors: { id: 'id phải là chuỗi' },
  statusCode: 400,
};

const jBodyGuestLog = Joi.object({
  parentId: Joi.number(),
  pendingGuestId: Joi.number(),
  meetWhoId: Joi.string().required(),
  guestCardId: Joi.string(),
  checkInTime: Joi.string().required(),
  guestCodeOrg: Joi.string(),
  estimatedOutTime: Joi.date(),
  guestTypeId: Joi.number().required(),
  purposeOfVisit: Joi.string(),
  purposeCategoryId: Joi.number().required(),
  isCivil: Joi.boolean(),
  groupName: Joi.string(),
  leaderName: Joi.string(),
  leaderPosition: Joi.string(),
  numberOfVisitors: Joi.number(),
  numberOfVehicles: Joi.number(),
  isSendMessageForMeetUser: Joi.boolean(),
  vehicleTypeId: Joi.number(),
  vehicleClassificationId: Joi.number(),
  notes: Joi.string(),
  isLeader: Joi.boolean(),
  guest: Joi.object({
    id: Joi.number(),
    fullName: Joi.string().required(),
    dateOfBirth: Joi.date(),
    phoneNumber: Joi.string().required(),
    guestCodeOrg: Joi.string(),
    guestOrganizationName: Joi.string().required(),
    avatar: Joi.string().allow(null, ''),
    sexId: Joi.number(),
    notes: Joi.string(),
    office: Joi.string().allow(''),
    occupation: Joi.string(),
    permanentAddress: Joi.string(),
    nationality: Joi.string(),
    identifyCard: Joi.object({
      documentTypeId: Joi.number().required(),
      identificationNumber: Joi.string().required(),
      issueDate: Joi.date(),
      expiryDate: Joi.date(),
      issuingAuthority: Joi.string(),
      notes: Joi.string(),
      images: Joi.array().items(Joi.string()),
    }).required(),
  }).required(),
  guestIdBy: Joi.string().required(),
  statusId: Joi.number(),
});

const eBodyGuestLog: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const jWebhookGuestLog = Joi.object({
  idVehicleLog: Joi.string(),
  defaultImage: Joi.string().allow(null),
  guestCardId: Joi.string(), // mã thẻ khách
  licensePlate: Joi.string(), // biển số xe
  vehicleTypeValue: Joi.string(), // loại xe
  vehicleClassificationValue: Joi.string(), // phân loại xe
  commodity: Joi.string().max(200), // hàng hóa
  etiquette: Joi.string().max(200), // lễ tiết tác phong
  status: Joi.string().valid('in', 'out').required(), // trạng thái
  guardPostId: Joi.number().required(), // vị trí chòi gác
  codeOrg: Joi.string().max(17).required(),
});

const eWebhookGuestLog: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu webhook không hợp lệ',
  statusCode: 400,
};

const jSearchPendingGuestLogObject = {
  search: Joi.string(),
  guestCardId: Joi.string(),
  licensePlate: Joi.string(),
  vehicleTypeIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  vehicleClassificationIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  inDateTo: Joi.date(),
  inDateFrom: Joi.date(),
  createdAt: Joi.array().items(Joi.string()),
  limit: Joi.number(),
  page: Joi.number(),
};

const jSearchPendingGuestLog = Joi.object(jSearchPendingGuestLogObject);

const jSearchGuestLogObject = {
  search: Joi.string(),
  limit: Joi.number(),
  page: Joi.number(),
  inDateTo: Joi.date(),
  inDateFrom: Joi.date(),
  outDateFrom: Joi.date(),
  outDateTo: Joi.date(),
  guestName: Joi.string(),
  guestIdentityTypeId: Joi.number(),
  meetWhoId: Joi.string(),
  meetOrgCode: Joi.string(),
  guestTypeIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  purposeCategoryIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  statusIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  vehicleTypeIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  vehicleClassificationIds: Joi.alternatives().try(
    Joi.number(),
    Joi.array().items(Joi.number()),
  ),
  isInDuty: Joi.boolean(),
  guestCardId: Joi.string(),
  identificationNumber: Joi.string(),
};

const jSearchGuestLog = Joi.object(jSearchGuestLogObject);

const eSearchPendingQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

const jDeleteGuestLogs = Joi.object({
  ids: Joi.array().items(Joi.number()).required(),
});

const jSearchGuestLogByGroup = Joi.object({
  ...jSearchGuestLogObject,
  ...jSearchPendingGuestLogObject,
  isPendingGuest: Joi.boolean(),
});

const jGuestLogStatsByOrgCode = Joi.object({
  orgCode: Joi.string(),
  startDate: Joi.date().required(),
  endDate: Joi.date().required(),
});

const jGuestLogHistoryInOut = Joi.object({
  orgCode: Joi.string(),
  startDate: Joi.date().required(),
  endDate: Joi.date().required(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
});

export {
  eIdInParams,
  eIdVehicleLogInParams,
  jBodyGuestLog,
  eBodyGuestLog,
  jWebhookGuestLog,
  eWebhookGuestLog,
  jSearchPendingGuestLog,
  jSearchGuestLog,
  eSearchPendingQuery,
  jDeleteGuestLogs,
  jSearchGuestLogByGroup as jSearchGuestLogByVehicle,
  jIdInParams,
  jIdVehicleLogInParams,
  jGuestLogStatsByOrgCode,
  jGuestLogHistoryInOut,
};
