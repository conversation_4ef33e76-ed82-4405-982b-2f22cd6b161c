import { status } from 'http-status';
import { NextFunction, Request, Response } from 'express';
import config from '../config';

export const webhookAuth = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  const authHeader = req.headers['x-key'];
  if (!authHeader) {
    return res.sendStatus(status.UNAUTHORIZED);
  }
  try {
    if (authHeader !== config.webhookKey) {
      return res.sendStatus(status.UNAUTHORIZED);
    }
    next();
  } catch (_) {
    return res.sendStatus(status.UNAUTHORIZED);
  }
};
