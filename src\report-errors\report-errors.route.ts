import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';

import { eIdInParams, eMessage, jIdInParams } from '../roles/roles.validation';
import {
  addUpdateHandle,
  deleteReport,
  deleteReportFiles,
  getReportError,
  getReportErrors,
  postReportError,
  putReportErrors,
} from './report-errors.controller';
import {
  eQueryReportErrors,
  jBodyAddUpdateHandleError,
  jBodyPostReportError,
  jBodyPutReportError,
  jQueryReportErrors,
} from './report-errors.validation';
import { jBodyDeleteFiles } from '../files/files.validation';

const routerReportError = express.Router();

routerReportError.post(
  '/',
  checkAuth,
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPostReportError,
    eMessage,
  ),
  postReportError,
);

routerReportError.put(
  '/:id',
  checkAuth,
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPutReportError,
    eMessage,
  ),
  putReportErrors,
);

routerReportError.delete(
  '/:id',
  checkAuth,
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  deleteReport,
);

routerReportError.delete(
  '/files/delete',
  checkAuth,
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyDeleteFiles,
    eMessage,
  ),
  deleteReportFiles,
);

routerReportError.get(
  '/',
  checkAuth,
  createValidator(
    eQueryReportErrors.part ? eQueryReportErrors.part : 'query',
    jQueryReportErrors,
    eQueryReportErrors,
  ),
  getReportErrors,
);

routerReportError.get(
  '/:id',
  checkAuth,
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  getReportError,
);

routerReportError.put(
  '/handle/:id',
  checkAuth,
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyAddUpdateHandleError,
    eMessage,
  ),
  addUpdateHandle,
);

export default routerReportError;
