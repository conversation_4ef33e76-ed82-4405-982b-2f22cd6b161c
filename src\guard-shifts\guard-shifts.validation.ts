import Joi from 'joi';
import { IApiError } from 'src/types/validation';

const jIdInParams = Joi.object({
  id: Joi.number().required(),
});

const eIdInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jShiftStaffInParams = Joi.object({
  guardShiftId: Joi.number().required(),
  postId: Joi.number().required(),
  qneid: Joi.string().required(),
});

const jGuardScheduleInParams = Joi.object({
  date: Joi.string().required(),
  guardPostId: Joi.array().items(Joi.number()),
  configShiftId: Joi.array().items(Joi.number()),
});

const jGuardPostInParams = Joi.object({
  guardPostId: Joi.number().required(),
});

const eShiftStaffInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const eGuardPostInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jShiftPostsInParams = Joi.object({
  guardShiftId: Joi.number().required(),
  postId: Joi.number().required(),
});

const eShiftPostsInParams: IApiError = {
  part: 'params',
  code: 'VALIDATION_ERROR',
  message: 'ID không hợp lệ',
  errors: { id: 'ID phải là số' },
  statusCode: 400,
};

const jBodyGuardShifts = Joi.object({
  date: Joi.string(),
  startTime: Joi.string(),
  endTime: Joi.string(),
  codeOrg: Joi.string(),
  note: Joi.string(),
  orderNum: Joi.number(),
});

const eBodyGuardShifts: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const eBodyGuardSchedule: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const jSearchGuardShifts = Joi.object({
  date: Joi.string(),
  startTime: Joi.string(),
  endTime: Joi.string(),
  codeOrg: Joi.string(),
  note: Joi.string(),
  orderNum: Joi.number(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

const jBodyShiftPosts = Joi.object({
  guardShiftId: Joi.number(),
  guardPostId: Joi.number(),
});

const eBodyShiftPosts: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const jBodyShiftStaff = Joi.object({
  shiftPostId: Joi.number(),
  qneid: Joi.string(),
  isLeader: Joi.boolean(),
});

const eBodyShiftStaff: IApiError = {
  part: 'body',
  code: 'VALIDATION_ERROR',
  message: 'Dữ liệu không hợp lệ',
  statusCode: 400,
};

const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

const jBodyGuardSchedule = Joi.object({
  guardPostId: Joi.number().required(),
  eQNId: Joi.array().required(),
  startTime: Joi.string().required(),
  endTime: Joi.string().required(),
  date: Joi.string().required(),
  codeOrg: Joi.string(),
  note: Joi.string(),
  orderNum: Joi.number(),
});

const jBodyDuplicateGuardSchedule = Joi.object({
  oldDate: Joi.string().required(),
  newDate: Joi.string().required(),
});

const jBodyDeleteGuardPostInGuardSchedule = Joi.object({
  date: Joi.string().required(),
});

export {
  jIdInParams,
  eIdInParams,
  jBodyGuardShifts,
  eBodyGuardShifts,
  jSearchGuardShifts,
  eSearchQuery,
  jBodyShiftPosts,
  eBodyShiftPosts,
  jShiftStaffInParams,
  jShiftPostsInParams,
  jBodyShiftStaff,
  eBodyShiftStaff,
  eShiftPostsInParams,
  eShiftStaffInParams,
  jGuardScheduleInParams,
  jBodyGuardSchedule,
  eBodyGuardSchedule,
  jBodyDuplicateGuardSchedule,
  jBodyDeleteGuardPostInGuardSchedule,
  jGuardPostInParams,
  eGuardPostInParams,
};
