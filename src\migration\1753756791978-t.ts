import { MigrationInterface, QueryRunner } from "typeorm";

export class T1753756791978 implements MigrationInterface {
    name = 'T1753756791978'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP CONSTRAINT "FK_3a0c2f5411f7661d513fb2ea910"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP COLUMN "maLoaiHinhTruc"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD "maLoaiHinhTruc" varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "dm_loai_hinh_truc" ADD CONSTRAINT "UQ_7c33bd45b7f16c300a07b962b06" UNIQUE ("ma")`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD CONSTRAINT "FK_3a0c2f5411f7661d513fb2ea910" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("ma") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP CONSTRAINT "FK_3a0c2f5411f7661d513fb2ea910"`);
        await queryRunner.query(`ALTER TABLE "dm_loai_hinh_truc" DROP CONSTRAINT "UQ_7c33bd45b7f16c300a07b962b06"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" DROP COLUMN "maLoaiHinhTruc"`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD "maLoaiHinhTruc" uniqueidentifier NOT NULL`);
        await queryRunner.query(`ALTER TABLE "donviLoaiHinhTruc" ADD CONSTRAINT "FK_3a0c2f5411f7661d513fb2ea910" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

}
