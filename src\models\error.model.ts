import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

import EntityModel from './entity.model';

@Entity('errors')
export class Error extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ type: 'nvarchar', length: 500 })
  name?: string;

  @Column({ type: 'nvarchar', length: 2000, nullable: true })
  content?: string;

  @Column({
    name: 'handle_method',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  handleMethod?: string;

  @Column({ type: 'nvarchar', nullable: true, length: 250 })
  type?: string;

  @Column({ type: 'nvarchar', length: 50 })
  status?: string; // new, reject , handling, resolve
}
