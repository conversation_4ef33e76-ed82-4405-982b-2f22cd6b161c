import express from 'express';
import { checkAuth } from '../middlewares/auth.middleware';
import {
  eCreate,
  eIdInParams,
  jBodyChucVuLoaiHinhTruc,
  jBodyDonViLoaiHinhTruc,
  jBodyLoaiHinhTruc,
  jBodyLoaiHinhTrucChiTiet,
  jIdInParams,
} from './dm-loai-hinh-truc.validation';
import {
  createChucVuLoaiHinhTruc,
  createDonViLoaiHinhTruc,
  createLoaiHinhTruc,
  createLoaiHinhTrucChiTiet,
} from './dm-loai-hinh-truc.controller';
import { createValidator } from '../utils/validator';
const router = express.Router();
router.post(
  '/',
  checkAuth,
  createValidator('body', jBodyLoaiHinhTruc, eCreate),
  createLoaiHinhTruc,
);
router.put(
  '/:id/don-vi-lich-trucs',
  checkAuth,
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyDonViLoaiHinhTruc, eCreate),
  createDonViLoaiHinhTruc,
);

router.put(
  '/:id/chuc-vu-lich-trucs',
  checkAuth,
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyChucVuLoaiHinhTruc, eCreate),
  createChucVuLoaiHinhTruc,
);

router.put(
  '/:id/loai-hinh-truc-chi-tiets',
  checkAuth,
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyLoaiHinhTrucChiTiet, eCreate),
  createLoaiHinhTrucChiTiet,
);
export default router;
