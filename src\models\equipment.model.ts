import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';
import { Type } from './type.model';

@Entity('equipments')
export class Equipment extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    type: 'nvarchar',
    length: 250,
  })
  name?: string;

  @Column({
    name: 'unit_id',
    type: 'int',
  })
  unitId?: number;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;

  @Column({
    type: 'int',
  })
  count?: number;

  @Column({
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  desc?: string;

  @Column({
    type: 'nvarchar',
    length: 50,
  })
  status?: string;

  @ManyToOne(() => Type, (type) => type.id)
  @JoinColumn({ name: 'unit_id', referencedColumnName: 'id' })
  type?: Type;
}
