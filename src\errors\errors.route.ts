import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';

import { eIdInParams, eMessage, jIdInParams } from '../roles/roles.validation';
import {
  deleteErrorC,
  getError,
  getErrors,
  postError,
  putError,
} from './errors.controller';
import {
  eQueryErrors,
  jBodyPostError,
  jQueryErrors,
} from './errors.validation';

const routerError = express.Router();

routerError.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editError]),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPostError,
    eMessage,
  ),
  postError,
);

routerError.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editError]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPostError,
    eMessage,
  ),
  putError,
);

routerError.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editError]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  deleteErrorC,
);

routerError.get(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editError]),
  createValidator(
    eQueryErrors.part ? eQueryErrors.part : 'query',
    jQueryErrors,
    eQueryErrors,
  ),
  getErrors,
);

routerError.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editError]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  getError,
);

export default routerError;
