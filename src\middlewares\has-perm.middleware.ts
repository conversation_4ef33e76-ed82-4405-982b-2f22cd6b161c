import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';

export function hasPerm(perms: string[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    const userPerms = req.permsArr;
    if (!userPerms) {
      return res.status(status.FORBIDDEN).json({
        message: 'Bạn không có quyền truy cập',
        statusCode: status.FORBIDDEN,
      });
    }
    if (userPerms && !perms.some((perm) => userPerms.includes(perm))) {
      return res.status(status.FORBIDDEN).json({
        message: 'Bạn không có quyền truy cập',
        statusCode: status.FORBIDDEN,
      });
    }
    next();
  };
}
