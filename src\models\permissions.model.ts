import {
  Column,
  <PERSON><PERSON>ty,
  OneToMany,
  PrimaryGeneratedColumn,
  Tree,
  TreeChildren,
  TreeParent,
} from 'typeorm';

import EntityModel from './entity.model';
import { RolePermission } from './rolePermission.model';

@Entity('permissions')
@Tree('closure-table')
export class Permission extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ type: 'nvarchar', length: 100, nullable: false })
  name?: string;

  @Column({ type: 'nvarchar', length: 100, nullable: true })
  displayName?: string;

  @Column({ type: 'nvarchar', length: 500, nullable: true })
  desc?: string;

  @Column({ type: 'tinyint', nullable: false, default: 1 })
  status?: number;

  @Column({ type: 'nvarchar', length: 500, nullable: true })
  resource?: string;

  @TreeChildren()
  children?: Permission[];

  @TreeParent()
  parent?: Permission;

  @Column({ nullable: true })
  public parentId!: number;

  @OneToMany(
    () => RolePermission,
    (rolePermission) => rolePermission.permission,
  )
  public rolePermissions!: RolePermission[];
}
