import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  createEquipment,
  deleteEquipment,
  getEquipment,
  searchEquipments,
  updateEquipment,
} from './equipment.controller';
import {
  eBodyEquipment,
  eIdInParams,
  eSearchQuery,
  jBodyEquipment,
  jIdInParams,
  jSearchEquipment,
} from './equipment.validation';

const router = express.Router();

router.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editEquipment]),
  createValidator('body', jBodyEquipment, eBodyEquipment),
  createEquipment,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editEquipment]),
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyEquipment, eBodyEquipment),
  updateEquipment,
);

router.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editEquipment]),
  createValidator('params', jIdInParams, eIdInParams),
  deleteEquipment,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([
  // RolePerms.viewEquipment,
  // RolePerms.viewSituationSummary,
  // RolePerms.viewEquipHandover,
  // ]),
  createValidator('params', jIdInParams, eIdInParams),
  getEquipment,
);

router.get(
  '/',
  checkAuth,
  // hasPerm([
  // RolePerms.viewEquipment,
  // RolePerms.viewSituationSummary,
  // RolePerms.viewEquipHandover,
  // ]),
  createValidator('query', jSearchEquipment, eSearchQuery),
  searchEquipments,
);

export default router;
