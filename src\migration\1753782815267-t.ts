import { MigrationInterface, QueryRunner } from "typeorm";

export class T1753782815267 implements MigrationInterface {
    name = 'T1753782815267'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "eQN" DROP COLUMN "eqn"`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "FK_9863dc6eaba137e458cb589e00e"`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "FK_c28e2464cbbcbc656f9bfa1663f"`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "FK_d00712933e6aa52d5f49b5d690d"`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "reinforcement_org_id" nvarchar(17)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "type_id" varchar(36)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "status_id" nvarchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "position_id" varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "phone_number" varchar(20)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "is_enable" bit`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "address_books" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "birth_place_ward_id" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "birthday" date`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "cccd" varchar(20)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "cccd_issued_ward_id" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "gender" bit`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "disabled_reason_id" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "identification" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "image" varchar(255)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "createdAt" datetime2`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e" DEFAULT GETDATE() FOR "createdAt"`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "updatedAt" datetime2`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910" DEFAULT GETDATE() FOR "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "FK_9863dc6eaba137e458cb589e00e" FOREIGN KEY ("reinforcement_org_id") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "FK_c28e2464cbbcbc656f9bfa1663f" FOREIGN KEY ("type_id") REFERENCES "military_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "FK_d00712933e6aa52d5f49b5d690d" FOREIGN KEY ("position_id") REFERENCES "position_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "FK_d00712933e6aa52d5f49b5d690d"`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "FK_c28e2464cbbcbc656f9bfa1663f"`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "FK_9863dc6eaba137e458cb589e00e"`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "DF_a1c6d5f81a70e32c3c5223d8910" DEFAULT getdate() FOR "updatedAt"`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "updatedAt" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" DROP CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e"`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "DF_b0bb5bb85cd501e99492d6ed47e" DEFAULT getdate() FOR "createdAt"`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "createdAt" datetime2 NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "image" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "identification" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "disabled_reason_id" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "gender" bit NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "cccd_issued_ward_id" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "cccd" varchar(20) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "birthday" date NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "birth_place_ward_id" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "address_books" varchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "is_enable" bit NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "phone_number" varchar(20) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "position_id" varchar(36)`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "status_id" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "type_id" varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ALTER COLUMN "reinforcement_org_id" nvarchar(17) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "FK_d00712933e6aa52d5f49b5d690d" FOREIGN KEY ("position_id") REFERENCES "position_categories"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "FK_c28e2464cbbcbc656f9bfa1663f" FOREIGN KEY ("type_id") REFERENCES "military_types"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD CONSTRAINT "FK_9863dc6eaba137e458cb589e00e" FOREIGN KEY ("reinforcement_org_id") REFERENCES "organizations"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "eQN" ADD "eqn" nvarchar(36) NOT NULL`);
    }

}
