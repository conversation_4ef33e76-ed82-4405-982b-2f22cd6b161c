{"name": "tttm-backend", "version": "1.0.0", "description": "tttm backend", "main": "index.js", "repository": "ssh://git@***********:50553/tttm/backend", "author": "tttm", "license": "MIT", "private": true, "scripts": {"start": "node build/index.js", "dev": "nodemon --exec ts-node src/index.ts", "build": "rm -rf ./build && tsc -p tsconfig.json ", "build:windown": "tsc -p tsconfig.json ", "clean": "rm -rf ./build", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "format": "yarn prettier:fix && yarn lint:fix", "check": "yarn prettier && yarn lint", "typeorm:cli": "ts-node ./node_modules/typeorm/cli -d ./ormconfig.ts", "migration:gen": "npm run typeorm:cli -- migration:generate src/migration/t", "migration:run": "npm run typeorm:cli -- migration:run", "migration:revert": "npm run typeorm:cli -- migration:revert"}, "dependencies": {"@faker-js/faker": "^9.9.0", "@turbodocx/html-to-docx": "^1.13.4", "@types/cors": "^2.8.19", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "docxtemplater": "^3.65.2", "dotenv": "^17.2.0", "dotenv-safe": "^9.1.0", "express": "^5.1.0", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.3.0", "http-status": "^2.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "morgan": "^1.10.1", "mssql": "^11.0.1", "multer": "^2.0.2", "node-cron": "^4.2.1", "pizzip": "^3.2.0", "query-string": "^9.2.2", "redis": "^5.6.0", "reflect-metadata": "^0.2.2", "sharp": "^0.34.3", "socket.io": "^4.8.1", "tslog": "^4.9.3", "typeorm": "0.3.25", "ws": "^8.18.3"}, "devDependencies": {"@types/dotenv-safe": "^8.1.6", "@types/express": "^5.0.3", "@types/fluent-ffmpeg": "^2.1.27", "@types/fs-extra": "^11.0.4", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.17.20", "@types/morgan": "^1.9.10", "@types/multer": "^2.0.0", "@types/node": "^24.0.15", "@types/ws": "^8.18.1", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "nodemon": "^3.1.10", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.*": "eslint --cache --fix", "*.{js,css,md,ts}": "prettier --write"}}