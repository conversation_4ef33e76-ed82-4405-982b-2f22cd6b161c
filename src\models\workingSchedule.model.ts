import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';

@Entity('workingSchedules')
export class WorkingSchedule extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'start_time',
    type: 'time',
  })
  startTime?: string;

  @Column({
    name: 'end_time',
    type: 'time',
  })
  endTime?: string;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;

  @Column({
    type: 'nvarchar',
    length: 50,
  })
  season?: string;

  @Column({
    type: 'nvarchar',
    length: 250,
  })
  months?: string;

  @Column({
    type: 'nvarchar',
    length: 50,
  })
  status?: string;

  // MM-DD
  @Column({
    type: 'varchar',
    length: 20,
    nullable: false,
    default: '',
  })
  effectiveFrom?: string;

  // "MM-DD"
  @Column({
    type: 'varchar',
    length: 20,
    nullable: false,
    default: '',
  })
  effectiveTo?: string;

  @Column({
    type: 'bit',
    nullable: false,
    default: false,
  })
  yearSpan?: boolean;
}
