import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  createShiftHandover,
  deleteShiftHandover,
  getShiftHandover,
  searchShiftHandovers,
  updateShiftHandover,
} from './shift-handover.controller';
import {
  eBodyShiftHandover,
  eIdInParams,
  eSearchQuery,
  jBodyShiftHandover,
  jIdInParams,
  jSearchShiftHandover,
} from './shift-handover.validation';

const router = express.Router();

router.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editShiftHandover]),
  createValidator('body', jBodyShiftHandover, eBodyShiftHandover),
  createShiftHandover,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editShiftHandover]),
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyShiftHandover, eBodyShiftHandover),
  updateShiftHandover,
);

router.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editShiftHandover]),
  createValidator('params', jIdInParams, eIdInParams),
  deleteShiftHandover,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewShiftHandover]),
  createValidator('params', jIdInParams, eIdInParams),
  getShiftHandover,
);

router.get(
  '/',
  checkAuth,
  // hasPerm([RolePerms.viewShiftHandover]),
  createValidator('query', jSearchShiftHandover, eSearchQuery),
  searchShiftHandovers,
);

export default router;
