import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  dropPermission,
  getAncestorsById,
  getById,
  getGroupTree,
  getSearch,
  getTreeAll,
  getTreeById,
  postPermission,
  putPermission,
  updateStatusPermissions,
} from './permissions.controller';
import {
  eBodyPostPermission,
  eIdInParams,
  eMessage,
  eParamsSearchPermission,
  jBodyPostPermission,
  jBodyPutPermission,
  jIdInParams,
  jIdInUpdateStatus,
  jParamsSearchPermission,
} from './permissions.validation';
const routerPermission = express.Router();
routerPermission.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editPermission]),
  createValidator(
    eBodyPostPermission.part ? eBodyPostPermission.part : 'body',
    jBodyPostPermission,
    eBodyPostPermission,
  ),
  postPermission,
);
routerPermission.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editPermission]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  createValidator(
    eBodyPostPermission.part ? eBodyPostPermission.part : 'body',
    jBodyPutPermission,
    eBodyPostPermission,
  ),
  putPermission,
);
routerPermission.delete(
  '/:id/status',
  checkAuth,
  // hasPerm([RolePerms.editPermission]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  dropPermission,
);
routerPermission.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewPermission]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  getById,
);
routerPermission.get(
  '/:id/tree',
  checkAuth,
  // hasPerm([RolePerms.viewPermission]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  getTreeById,
);
// router.get('/', checkAuth, // hasPerm([RolePerms.viewPermission]), getAll);
routerPermission.get(
  '/tree/all',
  checkAuth,
  // hasPerm([RolePerms.viewPermission]),
  getTreeAll,
);
routerPermission.get(
  '/tree/flat',
  checkAuth,
  // hasPerm([RolePerms.viewPermission]),
  getGroupTree,
);

routerPermission.get(
  '/tree/search',
  checkAuth,
  // hasPerm([RolePerms.viewPermission]),
  createValidator(
    eParamsSearchPermission.part ? eParamsSearchPermission.part : 'query',
    jParamsSearchPermission,
    eParamsSearchPermission,
  ),
  getSearch,
);
routerPermission.get(
  '/:id/ancestors',
  checkAuth,
  // hasPerm([RolePerms.viewPermission]),
  createValidator(
    eIdInParams.part ? eIdInParams.part : 'params',
    jIdInParams,
    eIdInParams,
  ),
  getAncestorsById,
);

routerPermission.put(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editPermission]),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jIdInUpdateStatus,
    eMessage,
  ),
  updateStatusPermissions,
);
export default routerPermission;
