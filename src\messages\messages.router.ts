import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import { sendSMS } from './messages.controller';
import { eBodyMessage, jBodyMessage } from './message.validation';

const routerMessage = express.Router();

routerMessage.post(
  '/',
  checkAuth,
  // // hasPerm([RolePerms.editGuardPost]),
  createValidator('body', jBodyMessage, eBodyMessage),
  sendSMS,
);

export default routerMessage;
