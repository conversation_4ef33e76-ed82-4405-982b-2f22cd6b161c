import axios from 'axios';
import dayjs from 'dayjs';
import { Between, In } from 'typeorm';
import { database } from '../config';

import config from '../config';
import { DailyStats } from '../models/dailyStats.model';
import { AttendanceLog, eQN, Organization } from '../models/index';
import { ATTENDANCE_STATUS } from './constants';
import { IOrder, IOrderResponse } from './interfaces/order.interface';

export async function insertBatchNoAttendanceEQN() {
  try {
    const today = dayjs().startOf('day');
    const attendanceLogRepository = database.getRepository(AttendanceLog);
    const eQNRepository = database.getRepository(eQN);

    // Get all active eQNs
    const activeEQNs = await eQNRepository.find({
      where: { isEnable: true },
      select: ['id', 'orgCode'],
    });

    // Get all attendance logs for today
    const todayAttendanceLogs = await attendanceLogRepository.find({
      where: {
        checkInTime: Between(today.toDate(), today.endOf('day').toDate()),
      },
      select: ['qneid'],
    });

    // Get eQN IDs that already have attendance logs
    const existingEQNIds = new Set(todayAttendanceLogs.map((log) => log.qneid));

    // Filter out eQNs that don't have attendance logs
    const eQNsWithoutAttendance = activeEQNs.filter(
      (eqn) => !existingEQNIds.has(eqn.id),
    );

    let insertedCount = 0;
    const CHUNK_SIZE = 50;

    // Process in chunks of 50 records
    for (let i = 0; i < eQNsWithoutAttendance.length; i += CHUNK_SIZE) {
      try {
        const chunk = eQNsWithoutAttendance.slice(i, i + CHUNK_SIZE);
        const attendanceLogs = chunk.map((eqn) => {
          const attendanceLog = new AttendanceLog();
          attendanceLog.qneid = eqn.id;
          attendanceLog.checkInTime = today.toDate();
          attendanceLog.codeOrg = eqn.orgCode;
          attendanceLog.status = ATTENDANCE_STATUS.KHONG_CHAM;
          attendanceLog.lateDuration = 0;
          attendanceLog.approvedBy = '';
          attendanceLog.approvedAt = new Date();
          attendanceLog.locationCheck = '';
          attendanceLog.checkTypeId = 1;
          attendanceLog.checkDate = new Date();
          attendanceLog.requiredStartTime = new Date();
          attendanceLog.reasonText = '';
          return attendanceLog;
        });

        await attendanceLogRepository.save(attendanceLogs);
        insertedCount += attendanceLogs.length;
        console.log(
          `Successfully inserted chunk of ${attendanceLogs.length} records`,
        );
      } catch (error) {
        console.error(`Error inserting chunk starting at index ${i}:`, error);
        // Continue with next chunk even if one fails
        continue;
      }
    }

    if (insertedCount > 0) {
      console.log(
        `Successfully inserted total of ${insertedCount} attendance records for eQNs without attendance`,
      );
    } else {
      console.log('No eQNs found without attendance records for today');
    }
  } catch (error) {
    console.error('Error inserting batch attendance records:', error);
    throw error;
  }
}

export async function syncAttendanceEQNData() {
  try {
    // Get all active eQNs
    const activeEQNs = await database.getRepository(eQN).find({
      where: { isEnable: true },
      select: ['id', 'orgCode'],
    });

    const allOrgs = await database.getRepository(Organization).find({
      where: { isEnable: true },
      select: ['id', 'code'],
    });

    // Get date range for the API call
    const today = dayjs();
    const fromDate = dayjs().startOf('month').startOf('day').toISOString();
    const toDate = today.endOf('day').toISOString();

    // const donVisValue = `[${organizations.map(org => `"${org.id}"`).join(',')}]`;
    // // const donVisValue = `["be93698f-b72d-41ba-8341-1515dad85f29"]`;
    // console.log(`donVisValue`, donVisValue)

    const attendanceLogRepository = database.getRepository(AttendanceLog);
    const dailyStatsRepository = database.getRepository(DailyStats);

    // Call eQN API to get orders
    const response = await axios.get<IOrderResponse>(
      `${config.eQN.apiUrl}/api/orders/qlvr`,
      {
        params: {
          FromDate: fromDate,
          ToDate: toDate,
          // DonVis: donVisValue
        },
        headers: {
          Authorization: `Bearer ${config.eQN.apiKey}`,
        },
      },
    );

    const orders: IOrder[] = response.data?.data ?? [];

    // For each organization, check their attendance status
    for (const order of orders) {
      // get order date
      const dateOfDepart = dayjs(order.date_of_depart).toDate();
      const dateOfReturn = dayjs(order.date_of_return).toDate();

      // get all date of depart and return
      const allDates = [];
      for (
        let date = dateOfDepart;
        date <= dateOfReturn;
        date = dayjs(date).add(1, 'day').toDate()
      ) {
        allDates.push(date);
      }

      const queryBuilder = await attendanceLogRepository
        .createQueryBuilder('attendanceLog')
        .where('attendanceLog.qneid = :qneid', {
          qneid: order.personal_identify_id.id,
        })
        .andWhere(`attendanceLog.checkInTime >= :startDate`, {
          startDate: dayjs(dateOfDepart)
            .startOf('day')
            .format('YYYY-MM-DD HH:mm:ss'),
        })
        .andWhere(`attendanceLog.checkInTime <= :endDate`, {
          endDate: dayjs(dateOfReturn)
            .endOf('day')
            .format('YYYY-MM-DD HH:mm:ss'),
        });

      const getAllAttendanceLog = await queryBuilder.getMany();

      // check attendance log for each date
      for (const date of allDates) {
        const hasAttendanceLog = getAllAttendanceLog.find((log) =>
          dayjs(log.checkInTime).isSame(date, 'day'),
        );

        // get org code
        let orgCode = order.personal_identify_org?.code || '';
        const orgId = order.personal_identify_org?.id || '';

        // get org info
        const orgInfo = allOrgs.find(
          (org) => org?.id?.toLocaleLowerCase() === orgId.toLocaleLowerCase(),
        );
        if (!orgInfo) {
          continue;
        }

        // check eqn is active
        const checkEQN = activeEQNs.find(
          (eqn) =>
            eqn?.id?.toLocaleLowerCase() ===
            order.personal_identify_id.id.toLocaleLowerCase(),
        );
        if (!checkEQN) {
          continue;
        }

        orgCode = orgInfo?.code as string;

        const approvalBy = order.approved_by || '';

        // case:1 If have attendance but check in later than required start time
        if (hasAttendanceLog) {
          if (hasAttendanceLog.status === ATTENDANCE_STATUS.DI_MUON) {
            hasAttendanceLog.status = ATTENDANCE_STATUS.NGHI_PHEP;
            hasAttendanceLog.approvedBy = approvalBy || '';
            hasAttendanceLog.approvedAt = new Date();
            hasAttendanceLog.reasonText = order.reason;
            await attendanceLogRepository.save(hasAttendanceLog);
          }

          // case:2 If don't have attendance log
          if (hasAttendanceLog.status === ATTENDANCE_STATUS.KHONG_CHAM) {
            hasAttendanceLog.status = ATTENDANCE_STATUS.NGHI_PHEP;
            hasAttendanceLog.approvedBy = approvalBy || '';
            hasAttendanceLog.approvedAt = new Date();
            hasAttendanceLog.reasonText = order.reason;
            await attendanceLogRepository.save(hasAttendanceLog);
          }
          continue;
        }

        // case:3 If don't have attendance log
        if (!hasAttendanceLog) {
          const attendanceLog = new AttendanceLog();
          attendanceLog.qneid = order.personal_identify_id.id;
          attendanceLog.checkInTime = date;
          attendanceLog.codeOrg = orgCode;
          attendanceLog.status = ATTENDANCE_STATUS.NGHI_PHEP;

          // TODO: get requiredStartTime from eQN
          attendanceLog.lateDuration = 0;
          attendanceLog.approvedBy = '';
          attendanceLog.approvedAt = new Date();
          attendanceLog.locationCheck = '';
          attendanceLog.checkTypeId = 1;
          attendanceLog.checkDate = new Date();
          attendanceLog.requiredStartTime = new Date();
          attendanceLog.reasonText = order.reason;
          await attendanceLogRepository.save(attendanceLog);
        }
      }
    }

    // create daily static for each organization
    const startDate = dayjs(fromDate);
    const endDate = dayjs(toDate);
    const allEQN = await database.getRepository(eQN).find({
      where: {
        orgCode: In(allOrgs.map((org) => org.code)),
      },
      select: ['id', 'orgCode'],
    });

    for (const org of allOrgs) {
      const dailyStats = await dailyStatsRepository
        .createQueryBuilder('dailyStats')
        .where('dailyStats.orgId = :orgId', { orgId: org.id })
        .andWhere('dailyStats.reportDate >= :start_date', {
          start_date: startDate.toDate(),
        })
        .andWhere('dailyStats.reportDate <= :end_date', {
          end_date: endDate.toDate(),
        })
        .getMany();

      const eQNCount = allEQN.filter((eq) => eq.orgCode === org.code).length;

      let tempDate = startDate;
      while (tempDate.diff(endDate, 'day') <= 0) {
        let dailyStat = dailyStats.find(
          (stat) => stat?.reportDate?.getTime() === tempDate.toDate().getTime(),
        );

        const orders = await attendanceLogRepository
          .createQueryBuilder('attendanceLog')
          .where('attendanceLog.codeOrg = :codeOrg', { codeOrg: org.code })
          .andWhere('attendanceLog.checkInTime >= :checkInTimeStart', {
            checkInTimeStart: new Date(
              tempDate.startOf('day').format('YYYY-MM-DD HH:mm:ss'),
            ),
          })
          .andWhere('attendanceLog.checkInTime <= :checkInTimeEnd', {
            checkInTimeEnd: new Date(
              tempDate.endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            ),
          })
          .getMany();

        const dungGioCount =
          orders.filter((order) => order.status === ATTENDANCE_STATUS.DUNG_GIO)
            .length ?? 0;
        const diMuonCount =
          orders.filter((order) => order.status === ATTENDANCE_STATUS.DI_MUON)
            .length ?? 0;
        const vangCoLyDoCount =
          orders.filter(
            (order) => order.status === ATTENDANCE_STATUS.VANG_CO_LY_DO,
          ).length ?? 0;
        const nghiPhepCount =
          orders.filter((order) => order.status === ATTENDANCE_STATUS.NGHI_PHEP)
            .length ?? 0;

        const khongChamCount =
          eQNCount -
          (dungGioCount + diMuonCount + vangCoLyDoCount + nghiPhepCount);
        const totalCount =
          dungGioCount +
          khongChamCount +
          diMuonCount +
          vangCoLyDoCount +
          nghiPhepCount;

        if (!dailyStat) {
          dailyStat = new DailyStats();
          dailyStat.createdAt = new Date();
          dailyStat.updatedAt = new Date();
          dailyStat.fullText = `${org.id} - ${tempDate.format('YYYY-MM-DD')}`;
          dailyStat.reportDate = tempDate.toDate();
          dailyStat.orgId = org.id;
          dailyStat.codeOrg = org.code;
        }

        dailyStat.totalCount = totalCount;
        dailyStat.dungGioCount = dungGioCount;
        dailyStat.khongChamCount = khongChamCount;
        dailyStat.diMuonCount = diMuonCount;
        dailyStat.vangCoLyDoCount = vangCoLyDoCount;
        dailyStat.nghiPhepCount = nghiPhepCount;

        await dailyStatsRepository.save(dailyStat);

        tempDate = tempDate.add(1, 'day');
      }
    }

    // update attendance log for today
    await insertBatchNoAttendanceEQN();

    console.log('Attendance data synchronization completed successfully');
  } catch (error) {
    console.error('Error synchronizing attendance data:', error);
  }
}
