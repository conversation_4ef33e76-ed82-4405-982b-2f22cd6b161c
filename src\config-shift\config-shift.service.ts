import { ISearchQuery } from 'src/types/req';
import { database } from '../config';
import { ConfigShift, Organization } from '../models';
import { ERROR } from '../utils/error';
import { ISearchConfigShiftQuery } from './config-shift';
import { createLinks } from '../utils/pagination';

export const create = async (data: ConfigShift) => {
  const repo = database.getRepository(ConfigShift);
  const organizationRepo = database.getRepository(Organization);
  const org = await organizationRepo.findOne({
    where: { code: data.codeOrg },
    withDeleted: true,
  });
  if (!org) {
    throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
  }
  const newConfigShift = repo.create(data);
  const saved = await repo.save(newConfigShift);
  return { data: saved };
};

export const update = async (id: number, data: Partial<ConfigShift>) => {
  const repo = database.getRepository(ConfigShift);
  const organizationRepo = database.getRepository(Organization);
  const org = await organizationRepo.findOne({ where: { code: data.codeOrg } });
  if (!org) {
    throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
  }
  const existing = await repo.findOne({ where: { id } });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  await repo.update(id, data);
  return { message: 'Cập nhật thành công' };
};

export const remove = async (id: number) => {
  const repo = database.getRepository(ConfigShift);
  const existing = await repo.findOne({ where: { id }, withDeleted: true });
  console.log('🚀 => remove => existing:', existing);
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  await repo.delete(id);
  return { message: 'Xóa thành công' };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(ConfigShift);
  const configShift = await repo.findOne({ where: { id }, withDeleted: true });
  if (!configShift) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  return configShift;
};

export const search = async (query: ISearchQuery<ISearchConfigShiftQuery>) => {
  const repo = database.getRepository(ConfigShift);
  const qb = repo.createQueryBuilder('configShift').withDeleted();
  if (query.startTime) {
    qb.andWhere('configShift.startTime = :startTime', {
      startTime: query.startTime,
    });
  }
  if (query.endTime) {
    qb.andWhere('configShift.endTime = :endTime', {
      endTime: query.endTime,
    });
  }

  if (query.ids) {
    qb.andWhere('configShift.id IN (:...ids)', { ids: query.ids });
  }

  if (query.codeOrg) {
    qb.andWhere('configShift.codeOrg = :codeOrg', { codeOrg: query.codeOrg });
  }

  if (query.orderNum) {
    qb.andWhere('configShift.orderNum = :orderNum', {
      orderNum: query.orderNum,
    });
  }
  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;
  qb.skip(skip).take(limit);
  qb.orderBy('configShift.orderNum', 'ASC');
  const [data, total] = await qb.getManyAndCount();
  const links = createLinks(
    '/guard-post/search?',
    query,
    page,
    Math.ceil(total / limit),
  );
  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};
