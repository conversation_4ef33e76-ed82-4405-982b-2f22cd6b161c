import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  <PERSON>To<PERSON><PERSON>,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';
import { GuardShift } from './guardShift.model';

@Entity('shiftInspections')
export class ShiftInspection extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;

  @Column({
    type: 'datetime2',
  })
  datetime?: Date;

  @Column({
    type: 'nvarchar',
    length: 2000,
  })
  posts?: string; // JSON data

  @Column({
    name: 'content_check',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  contentCheck?: string;

  @Column({
    name: 'qne_id',
    type: 'nvarchar',
    length: 250,
  })
  QNeID?: string;

  @Column({
    name: 'weapon_check',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  weaponCheck?: string;

  @Column({
    name: 'equip_check',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  equipCheck?: string;

  @Column({
    name: 'situation_check',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  situationCheck?: string;

  @Column({
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  note?: string;

  @Column({
    type: 'int',
    nullable: true,
    name: 'guard_shift_id',
  })
  guardShiftId?: number;
  @ManyToOne(() => GuardShift, (guardShift) => guardShift.shiftInspections)
  @JoinColumn({
    name: 'guard_shift_id',
  })
  guardShift?: GuardShift;
}
