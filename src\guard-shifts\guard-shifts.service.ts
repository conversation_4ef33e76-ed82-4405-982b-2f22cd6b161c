import dayjs from 'dayjs';
import { In, IsNull } from 'typeorm';
import { database } from '../config';
import {
  ConfigShift,
  eQN,
  GuardPost,
  GuardShift,
  Organization,
  ShiftPost,
  ShiftStaff,
  User,
} from '../models';
import { ISearchQuery } from '../types/req';
import { ERROR } from '../utils/error';
import { createLinks } from '../utils/pagination';
import {
  GuardPostWithShifts,
  GuardShiftWithPosts,
  ICreateGuardSchedule,
  IDeleteGuardPostInGuardSchedule,
  IDuplicateGuardSchedule,
  ISearchGuardScheduleQuery,
  ISearchGuardShiftQuery,
  ShiftPostWithStaff,
  ShiftStaffWithUser,
} from './guard-shifts';

export const create = async (data: GuardShift) => {
  const repo = database.getRepository(GuardShift);
  const organizationRepo = database.getRepository(Organization);
  const org = await organizationRepo.findOne({ where: { code: data.codeOrg } });
  if (!org) {
    throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
  }
  const newGuardShift = repo.create(data);
  const saved = await repo.save(newGuardShift);

  return { data: saved };
};

export const update = async (id: number, data: Partial<GuardShift>) => {
  const repo = database.getRepository(GuardShift);
  const organizationRepo = database.getRepository(Organization);
  const org = await organizationRepo.findOne({ where: { code: data.codeOrg } });
  if (!org) {
    throw new Error(ERROR.ORGANIZATION_NOT_FOUND);
  }
  const existing = await repo.findOne({ where: { id } });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.update(id, data);
  return { message: 'Cập nhật thành công' };
};

export const remove = async (id: number) => {
  const repo = database.getRepository(GuardShift);
  const existing = await repo.findOne({ where: { id } });

  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.delete(id);
  return { message: 'Xóa thành công' };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(GuardShift);
  const guardShift = await repo.findOne({ where: { id } });

  if (!guardShift) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  return guardShift;
};

export const search = async (query: ISearchQuery<ISearchGuardShiftQuery>) => {
  const repo = database.getRepository(GuardShift);
  const qb = repo
    .createQueryBuilder('guardShifts')
    .leftJoinAndSelect('guardShifts.shiftPosts', 'shiftPosts')
    .leftJoinAndSelect('shiftPosts.shiftStaffs', 'shiftStaffs')
    .leftJoinAndSelect('shiftPosts.guardPost', 'guardPost')
    .leftJoinAndSelect('shiftStaffs.eQN', 'eQN')
    .leftJoinAndSelect('eQN.Rank', 'rank');

  if (query.date) {
    qb.andWhere('(CAST(guardShifts.date AS DATE) = CAST(:date AS DATE))', {
      date: dayjs(query.date).startOf('day').toDate(),
    });
  }
  if (query.startTime) {
    qb.andWhere('guardShifts.startTime LIKE :startTime', {
      startTime: `%${query.startTime}%`,
    });
  }
  if (query.endTime) {
    qb.andWhere('guardShifts.endTime LIKE :endTime', {
      endTime: `%${query.endTime}%`,
    });
  }
  if (query.codeOrg) {
    qb.andWhere('guardShifts.codeOrg LIKE :codeOrg', {
      codeOrg: `%${query.codeOrg}%`,
    });
  }
  if (query.note) {
    qb.andWhere('guardShifts.note LIKE :note', { note: `%${query.note}%` });
  }
  if (query.orderNum) {
    qb.andWhere('guardShifts.orderNum = :orderNum', {
      orderNum: query.orderNum,
    });
  }

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit);
  qb.orderBy('guardShifts.orderNum', 'ASC');

  const [data, total] = await qb.getManyAndCount();
  const links = createLinks(
    '/guard-shifts?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};

export const getShiftPosts = async (guardShiftId: number, postId: number) => {
  const repo = database.getRepository(ShiftPost);
  const shiftPost = await repo.findOne({
    where: { id: postId, guardShiftId },
  });
  return shiftPost;
};

export const updateShiftPosts = async (
  guardShiftId: number,
  postId: number,
  data: Partial<ShiftPost>,
) => {
  const repo = database.getRepository(ShiftPost);
  const existing = await repo.findOne({ where: { id: postId } });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  await repo.update(postId, data);
  return { message: 'Cập nhật thành công' };
};

export const deleteShiftPosts = async (
  guardShiftId: number,
  postId: number,
) => {
  const repo = database.getRepository(ShiftPost);
  const existing = await repo.findOne({
    where: { id: postId, guardShiftId },
  });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  await repo.delete(existing.id as number);
  return { message: 'Xóa thành công' };
};

export const getShiftStaff = async (
  guardShiftId: number,
  postId: number,
  qneid: string,
) => {
  const repoShiftStaff = database.getRepository(ShiftStaff);
  const repoShiftPost = database.getRepository(ShiftPost);
  const shiftPost = await repoShiftPost.findOne({
    where: { id: postId, guardShiftId },
  });
  if (!shiftPost) throw new Error(ERROR.DATA_NOT_FOUND);
  const shiftStaff = await repoShiftStaff.findOne({
    where: { shiftPostId: shiftPost.id, qneid },
  });
  if (!shiftStaff) throw new Error(ERROR.DATA_NOT_FOUND);
  return shiftStaff;
};

export const updateShiftStaff = async (
  guardShiftId: number,
  postId: number,
  qneid: string,
  data: Partial<ShiftStaff>,
) => {
  const repoShiftStaff = database.getRepository(ShiftStaff);
  const repoShiftPost = database.getRepository(ShiftPost);
  const shiftPost = await repoShiftPost.findOne({
    where: { id: postId, guardShiftId },
  });
  if (!shiftPost) throw new Error(ERROR.DATA_NOT_FOUND);
  const shiftStaff = await repoShiftStaff.findOne({
    where: { shiftPostId: shiftPost.id, qneid },
  });
  if (!shiftStaff) throw new Error(ERROR.DATA_NOT_FOUND);
  await repoShiftStaff.update(shiftStaff.id as number, data);
  return { message: 'Cập nhật thành công' };
};

export const deleteShiftStaff = async (
  guardShiftId: number,
  postId: number,
  qneid: string,
) => {
  const repoShiftStaff = database.getRepository(ShiftStaff);
  const repoShiftPost = database.getRepository(ShiftPost);
  const shiftPost = await repoShiftPost.findOne({
    where: { id: postId, guardShiftId },
  });
  if (!shiftPost) throw new Error(ERROR.DATA_NOT_FOUND);
  const shiftStaff = await repoShiftStaff.findOne({
    where: { shiftPostId: shiftPost.id, qneid },
  });
  if (!shiftStaff) throw new Error(ERROR.DATA_NOT_FOUND);
  await repoShiftStaff.delete(shiftStaff.id as number);
  return { message: 'Xóa thành công' };
};

export const getGuardSchedule = async (
  query: ISearchGuardScheduleQuery,
  user: User,
) => {
  const selectedDate = query?.date ? new Date(query.date) : new Date();

  const repoGuardShift = database.getRepository(GuardShift);
  const repoGuardPost = database.getRepository(GuardPost);
  const repoShiftPost = database.getRepository(ShiftPost);
  const repoShiftStaff = database.getRepository(ShiftStaff);
  const repoEQN = database.getRepository(eQN);
  const repoConfigShift = database.getRepository(ConfigShift);

  let configShifts = await repoConfigShift.find({
    where: { codeOrg: user?.manageOrgCode },
    order: { orderNum: 'ASC' },
  });

  if (query.configShiftId) {
    configShifts = configShifts.filter((cs) =>
      query?.configShiftId?.map(String).includes(cs?.id?.toString() as string),
    );
  }

  const guardShifts = await repoGuardShift.find({
    where: { date: dayjs(selectedDate).startOf('day').toDate() },
    order: { orderNum: 'ASC' },
  });

  const shiftPosts = await repoShiftPost.find({
    where: { guardShiftId: In(guardShifts.map((gs) => gs.id)) },
  });

  const shiftStaffs = await repoShiftStaff.find({
    where: { shiftPostId: In(shiftPosts.map((sp) => sp.id)) },
  });

  const eQNs = await repoEQN.find({
    where: { id: In(shiftStaffs.map((ss) => ss.qneid)) },
  });

  const guardPostIds = Array.from(
    new Set(shiftPosts.map((sp) => sp.guardPostId)),
  );

  let guardPosts = await repoGuardPost.find({
    where: { id: In(guardPostIds) },
  });

  if (query.guardPostId) {
    guardPosts = guardPosts.filter((gp) =>
      query?.guardPostId?.map(String).includes(gp?.id?.toString() as string),
    );
  }
  const shiftStaffMap: Record<number, ShiftStaffWithUser[]> = {};
  for (const staff of shiftStaffs) {
    if (!shiftStaffMap[staff.shiftPostId as number])
      shiftStaffMap[staff.shiftPostId as number] = [];
    shiftStaffMap[staff.shiftPostId as number].push({
      ...staff,
      eQN: (eQNs?.find((eqn) => eqn.id === staff.qneid) as eQN) || null,
    } as ShiftStaffWithUser);
  }

  const shiftPostMap: Record<string, ShiftPostWithStaff[]> = {};
  for (const post of shiftPosts) {
    const key = `${post.guardShiftId}-${post.guardPostId}`;
    if (!shiftPostMap[key]) shiftPostMap[key] = [];
    shiftPostMap[key].push({
      ...post,
      shiftStaffs: shiftStaffMap[post.id as number] || [],
    } as ShiftPostWithStaff);
  }

  const guardPostMap: Record<number, GuardPostWithShifts> = {};

  for (const guardPost of guardPosts) {
    const guardShiftsOfPost = guardShifts.filter((gs) =>
      shiftPosts.some(
        (sp) => sp.guardShiftId === gs.id && sp.guardPostId === guardPost.id,
      ),
    );

    const guardShiftsForPost = configShifts.map((configShift) => {
      const actualGuardShift = guardShiftsOfPost.find(
        (gs) => gs.orderNum === configShift.orderNum,
      );

      if (actualGuardShift) {
        return {
          ...actualGuardShift,
          shiftPosts:
            shiftPostMap[`${actualGuardShift.id}-${guardPost.id}`] || [],
        };
      } else {
        return {
          id: 0,
          createdAt: null,
          updatedAt: null,
          deletedAt: null,
          fullText: null,
          date: dayjs(selectedDate).startOf('day').toDate(),
          startTime: null,
          endTime: null,
          codeOrg: guardPost.codeOrg || null,
          note: null,
          orderNum: configShift.orderNum,
          shiftPosts: [],
        };
      }
    }) as GuardShiftWithPosts[];

    guardPostMap[guardPost.id as number] = {
      id: guardPost.id as number,
      name: guardPost.name || '',
      location: guardPost.location || '',
      guardShifts: guardShiftsForPost,
    } as GuardPostWithShifts;
  }

  return {
    guardPost: Object.values(guardPostMap),
  };
};

export const createGuardSchedule = async (
  userIdentity: User,
  payload: ICreateGuardSchedule,
) => {
  const repoeQN = database.getRepository(eQN);
  const repoGuardShift = database.getRepository(GuardShift);
  const repoShiftPost = database.getRepository(ShiftPost);
  const repoShiftStaff = database.getRepository(ShiftStaff);
  const repoConfigShift = database.getRepository(ConfigShift);
  const eQNIds = await repoeQN.find({ where: { id: In(payload.eQNId ?? []) } });

  if (eQNIds.length !== payload.eQNId?.length)
    throw new Error(ERROR.EQN_NOT_FOUND);
  const guardShifts = await repoGuardShift.find({
    where: { date: dayjs(payload.date).startOf('day').toDate() },
  });
  if (guardShifts.length === 0) {
    const configShift = await repoConfigShift.find({
      where: { codeOrg: userIdentity?.manageOrgCode, deletedAt: IsNull() },
    });
    if (!configShift || !configShift.length)
      throw new Error(ERROR.CONFIG_SHIFT_NOT_FOUND);
    const shiftsToCreate = [];
    for (let i = 0; i < configShift.length; i++) {
      const [startHour, startMinute] = (configShift?.[i]?.startTime || '')
        .split(':')
        .map(Number);
      const [endHour, endMinute] = (configShift?.[i]?.endTime || '')
        .split(':')
        .map(Number);
      const dateBase = dayjs(payload.date).startOf('day');
      const startTime = dateBase
        .hour(startHour)
        .minute(startMinute)
        .second(0)
        .toDate();
      const endTime = dateBase
        .hour(endHour)
        .minute(endMinute)
        .second(0)
        .toDate();
      shiftsToCreate.push({
        date: dateBase.toDate(),
        startTime,
        endTime,
        codeOrg: userIdentity?.manageOrgCode,
        orderNum: i + 1,
      });
    }
    await repoGuardShift.save(shiftsToCreate);
  }
  let guardShift = await repoGuardShift.findOne({
    where: {
      orderNum: payload.orderNum,
      date: dayjs(payload.date).startOf('day').toDate(),
    },
  });
  if (!guardShift)
    guardShift = await repoGuardShift.save({
      date: dayjs(payload.date).startOf('day').toDate(),
      startTime: payload.startTime,
      endTime: payload.endTime,
      codeOrg: userIdentity?.manageOrgCode,
      orderNum: payload.orderNum,
    });
  let shiftPost = await repoShiftPost.findOne({
    where: { guardPostId: payload.guardPostId, guardShiftId: guardShift.id },
  });
  if (!shiftPost)
    shiftPost = await repoShiftPost.save({
      guardPostId: payload.guardPostId,
      guardShiftId: guardShift.id,
    });
  const shiftsStaffs = await repoShiftStaff.find({
    where: { shiftPostId: shiftPost?.id },
  });
  if (shiftsStaffs.length > 1) {
    await repoShiftStaff.remove(shiftsStaffs);
    for (const qneidItem of payload?.eQNId ?? []) {
      await repoShiftStaff.save({
        shiftPostId: shiftPost?.id,
        qneid: qneidItem,
      });
    }
  } else {
    for (const qneidItem of payload?.eQNId ?? []) {
      await repoShiftStaff.save({
        shiftPostId: shiftPost?.id,
        qneid: qneidItem,
      });
    }
  }
  return { success: true };
};

function chunkArray<T>(array: T[], size: number): T[][] {
  const result: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size));
  }
  return result;
}

export const duplicateGuardSchedule = async (
  payload: IDuplicateGuardSchedule,
) => {
  const repoGuardShift = database.getRepository(GuardShift);
  const repoShiftPost = database.getRepository(ShiftPost);
  const repoShiftStaff = database.getRepository(ShiftStaff);

  const guardShiftOlds = await repoGuardShift.find({
    where: { date: dayjs(payload.oldDate).startOf('day').toDate() },
  });

  if (guardShiftOlds.length === 0) {
    throw new Error('Không có lịch gác nào trong ngày cũ để sao chép');
  }

  const shiftPostOlds = await repoShiftPost.find({
    where: { guardShiftId: In(guardShiftOlds.map((gs) => gs.id)) },
  });

  const shiftStaffOlds = await repoShiftStaff.find({
    where: { shiftPostId: In(shiftPostOlds.map((sp) => sp.id)) },
  });

  const guardShiftNews = guardShiftOlds.map((oldShift) => {
    const newStartTime = dayjs(payload.newDate)
      .hour(dayjs(oldShift.startTime).hour())
      .minute(dayjs(oldShift.startTime).minute())
      .second(dayjs(oldShift.startTime).second())
      .millisecond(dayjs(oldShift.startTime).millisecond())
      .toDate();

    const newEndTime = dayjs(payload.newDate)
      .hour(dayjs(oldShift.endTime).hour())
      .minute(dayjs(oldShift.endTime).minute())
      .second(dayjs(oldShift.endTime).second())
      .millisecond(dayjs(oldShift.endTime).millisecond())
      .toDate();

    return repoGuardShift.create({
      ...oldShift,
      id: undefined,
      date: dayjs(payload.newDate).startOf('day').toDate(),
      startTime: newStartTime,
      endTime: newEndTime,
      createdAt: undefined,
      updatedAt: undefined,
      deletedAt: undefined,
    });
  });

  // 👉 Chia batch insert để tránh lỗi 2100 parameters
  const guardShiftChunks = chunkArray(guardShiftNews, 200);
  const savedGuardShiftNews: GuardShift[] = [];
  for (const chunk of guardShiftChunks) {
    const saved = await repoGuardShift.save(chunk);
    savedGuardShiftNews.push(...saved);
  }

  const shiftIdMap = new Map(
    guardShiftOlds.map((oldShift, idx) => [
      oldShift.id,
      savedGuardShiftNews[idx].id,
    ]),
  );

  const shiftPostNews = shiftPostOlds.map((oldPost) => {
    return repoShiftPost.create({
      ...oldPost,
      id: undefined,
      guardShiftId: shiftIdMap.get(oldPost.guardShiftId),
      createdAt: undefined,
      updatedAt: undefined,
      deletedAt: undefined,
    });
  });

  const shiftPostChunks = chunkArray(shiftPostNews, 200);
  const savedShiftPostNews: ShiftPost[] = [];
  for (const chunk of shiftPostChunks) {
    const saved = await repoShiftPost.save(chunk);
    savedShiftPostNews.push(...saved);
  }

  const shiftPostIdMap = new Map(
    shiftPostOlds.map((oldPost, idx) => [
      oldPost.id,
      savedShiftPostNews[idx].id,
    ]),
  );

  const shiftStaffNews = shiftStaffOlds.map((oldStaff) => {
    return repoShiftStaff.create({
      ...oldStaff,
      id: undefined,
      shiftPostId: shiftPostIdMap.get(oldStaff.shiftPostId),
      createdAt: undefined,
      updatedAt: undefined,
      deletedAt: undefined,
    });
  });

  const shiftStaffChunks = chunkArray(shiftStaffNews, 200);
  for (const chunk of shiftStaffChunks) {
    await repoShiftStaff.save(chunk);
  }

  return {
    message: 'Sao chép lịch gác thành công!',
    newShifts: savedGuardShiftNews,
  };
};

export const deleteGuardPostInGuardSchedule = async (
  guardPostId: number,
  payload: IDeleteGuardPostInGuardSchedule,
) => {
  const repoGuardShift = database.getRepository(GuardShift);
  const repoShiftPost = database.getRepository(ShiftPost);
  const repoShiftStaff = database.getRepository(ShiftStaff);
  const guardShifts = await repoGuardShift.find({
    where: { date: dayjs(payload.date).startOf('day').toDate() },
  });
  const shiftPosts = await repoShiftPost.find({
    where: {
      guardShiftId: In(guardShifts.map((item) => item.id)),
      guardPostId: guardPostId,
    },
  });
  const shiftStaffs = await repoShiftStaff.find({
    where: { shiftPostId: In(shiftPosts.map((item) => item.id)) },
  });

  await repoShiftStaff.remove(shiftStaffs);
  await repoShiftPost.remove(shiftPosts);
  return { message: 'Xóa vọng gác trong ngày thành công!' };
};
