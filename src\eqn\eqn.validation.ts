import Joi from 'joi';
import { IApiError } from '../types/validation';

export const jSearchEQN = Joi.object({
  name: Joi.string(),
  codeOrg: Joi.string(),
  typeId: Joi.number(),
  status: Joi.string(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

export const eSearchQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export const jListEQN = Joi.object({
  orgCode: Joi.string().optional(),
  positionId: Joi.string().optional(),
  rankId: Joi.string().optional(),
  typeId: Joi.string().optional(),
  fullText: Joi.string().optional(),
  limit: Joi.number().required(),
  page: Joi.number().required(),
});

export const eListQuery: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};

export const jDashboard = Joi.object({
  name: Joi.string(),
  codeOrg: Joi.string(),
});

export const eDashboard: IApiError = {
  part: 'query',
  code: 'VALIDATION_ERROR',
  message: 'Tham số tìm kiếm không hợp lệ',
  statusCode: 400,
};
