import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
import {
  createSituationSummary,
  deleteSituationSummary,
  docxSituationSummary,
  getSituationSummary,
  searchSituationSummaries,
  updateSituationSummary,
} from './situation-summary.controller';
import {
  eBodySituationSummary,
  eIdInParams,
  jBodySituationSummary,
  jIdInParams,
} from './situation-summary.validation';

const router = express.Router();

router.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editSituationSummary]),
  createValidator('body', jBodySituationSummary, eBodySituationSummary),
  createSituationSummary,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editSituationSummary]),
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodySituationSummary, eBodySituationSummary),
  updateSituationSummary,
);

router.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editSituationSummary]),
  createValidator('params', jIdInParams, eIdInParams),
  deleteSituationSummary,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewSituationSummary]),
  createValidator('params', jIdInParams, eIdInParams),
  getSituationSummary,
);

router.get(
  '/:id/exportDocx',
  checkAuth,
  // hasPerm([RolePerms.viewSituationSummary]),
  createValidator('params', jIdInParams, eIdInParams),
  // exportToDocx,
);

router.get('/', checkAuth, searchSituationSummaries);

// router.get(
//   '/export/book',
//   checkAuth,
// // hasPerm([RolePerms.viewSituationSummary]),
//   createValidator('query', jSearchSituationSummary, eSearchQuery),
//   exportBook,
// );

router.get(
  '/:id/tkbc/docx',
  checkAuth,
  // hasPerm([RolePerms.viewExportSituationSummary]),
  createValidator('params', jIdInParams, eIdInParams),
  docxSituationSummary,
);

export default router;
