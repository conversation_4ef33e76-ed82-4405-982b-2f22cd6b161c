export type IUser = {
  id: number;
  username: string;
  password: string;
  shortName: string;
  name: string;
  status: number;
  mobilePhone: string;
  contact: string;
  notes: string;
  role: number;
  organizationId: string;
  organization: IOrganization;
};

export type IOrganization = {
  id: string;
  code: string;
  name: string;
  desc: string;
  isEnable: boolean;
  hasChild: boolean;
  shortName: string;
  orderNumber: number;
  privateName: string;
  children: IOrganization[];
  parent: IOrganization;
  parentId: string;
  users: IUser[];
  status: number;
};

export interface AuthResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
}
