import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';

import EntityModel from './entity.model';
import { ReportError } from './reportError.model';
import { User } from './user.model';

@Entity('files')
export class File extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({ length: 250, type: 'nvarchar' })
  name?: string;

  @Column({ name: 'thumbnail', type: 'nvarchar', length: 500, nullable: true })
  thumbnail?: string;

  @Column({ name: 'type', type: 'nvarchar', length: 50 })
  type?: string;

  @Column({ type: 'nvarchar', length: 500 })
  key?: string; // folder/file

  @Column({ type: 'nvarchar', length: 250 })
  mime?: string;

  @Column({ default: 0, type: 'int' })
  size?: number;

  @Column({ name: 'report_error_id', nullable: true })
  reportErrorId?: number;
  @ManyToOne(() => ReportError)
  @JoinColumn({ name: 'report_error_id' })
  reportError?: ReportError;

  @Column({ name: 'user_id' })
  userId?: number;
  @ManyToOne(() => User)
  @JoinColumn({ name: 'user_id' })
  user?: User;
}
