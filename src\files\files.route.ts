import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';

import { eMessage } from '../roles/roles.validation';
import {
  deleteFiles,
  downloadFileByRelativePath,
  getFile,
  getFileToken,
  postUploadFiles,
} from './files.controller';
import { jBodyDeleteFiles, jBodyPostUploadFiles } from './files.validation';
import { upload } from './files.service';

const routerFile = express.Router();

routerFile.post(
  '/',
  checkAuth,
  upload.array('file'),
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyPostUploadFiles,
    eMessage,
  ),
  postUploadFiles,
);

routerFile.delete(
  '/',
  checkAuth,
  createValidator(
    eMessage.part ? eMessage.part : 'body',
    jBodyDeleteFiles,
    eMessage,
  ),
  deleteFiles,
);

routerFile.get('/token', checkAuth, getFileToken);
routerFile.get('/view', getFile);
routerFile.get('/download', downloadFileByRelativePath);

export default routerFile;
