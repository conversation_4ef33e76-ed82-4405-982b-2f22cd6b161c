import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToOne,
  PrimaryColumn,
  DeleteDateColumn,
} from 'typeorm';
import { Organization } from './organization.model';
import { MilitaryType } from './militaryType.model';
import { PositionCategory } from './positionCategory.model';
import { MilitaryRank } from './militaryRank.model';
import { User } from './user.model';

@Entity('eQN')
export class eQN {
  @PrimaryColumn({ name: 'id', type: 'varchar', length: 36 })
  id?: string;

  @Column({ name: 'full_name', type: 'nvarchar', length: 255 })
  fullName?: string;

  @Column({
    name: 'short_name',
    type: 'nvarchar',
    length: 255,
    nullable: false,
  })
  shortName?: string;

  @Column({ name: 'orgCode', type: 'nvarchar', length: 17 })
  orgCode?: string;

  @Column({
    name: 'reinforcement_org_id',
    type: 'nvarchar',
    length: 17,
    nullable: true,
  })
  reinforcementOrgId?: string;

  @Column({ name: 'type_id', type: 'varchar', length: 36, nullable: true })
  typeId?: string;

  @Column({ name: 'rank_id', type: 'varchar', length: 36 })
  rankId?: string;

  @Column({ name: 'status_id', type: 'nvarchar', length: 255, nullable: true })
  statusId?: string;

  @Column({ name: 'position_id', type: 'varchar', length: 36, nullable: false })
  positionId?: string;

  @Column({
    name: 'phone_number',
    type: 'varchar',
    length: 20,
    nullable: true,
  })
  phoneNumber?: string;

  @Column({ name: 'is_enable', type: 'bit', default: true, nullable: true })
  isEnable?: boolean;

  @Column({
    name: 'address_books',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  addressBooks?: string;

  @Column({
    name: 'birth_place_ward_id',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  birthPlaceWardId?: string;

  @Column({ name: 'birthday', type: 'date', nullable: true })
  birthday?: Date;

  @Column({ name: 'cccd', type: 'varchar', length: 20, nullable: true })
  cccd?: string;

  @Column({
    name: 'cccd_issued_ward_id',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  cccdIssuedWardId?: string;

  @Column({ name: 'gender', type: 'bit', nullable: true })
  gender?: boolean;

  @Column({
    name: 'disabled_reason_id',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  disabledReasonId?: string;

  @Column({
    name: 'identification',
    type: 'varchar',
    length: 255,
    nullable: true,
  })
  identification?: string;

  @Column({ name: 'image', type: 'varchar', length: 255, nullable: true })
  image?: string;

  @Column({
    name: 'createdAt',
    type: 'datetime2',
    default: () => 'GETDATE()',
    nullable: true,
  })
  createdAt?: Date;

  @Column({
    name: 'updatedAt',
    type: 'datetime2',
    default: () => 'GETDATE()',
    nullable: true,
  })
  updatedAt?: Date;

  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'orgCode', referencedColumnName: 'code' })
  organization?: Organization;

  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'reinforcement_org_id', referencedColumnName: 'code' })
  reinforcementOrganization?: Organization;

  @ManyToOne(() => MilitaryType)
  @JoinColumn({ name: 'type_id', referencedColumnName: 'id' })
  type?: MilitaryType;

  @ManyToOne(() => PositionCategory)
  @JoinColumn({ name: 'position_id', referencedColumnName: 'id' })
  position?: PositionCategory;

  @ManyToOne(() => MilitaryRank)
  @JoinColumn({ name: 'rank_id', referencedColumnName: 'id' })
  Rank?: MilitaryRank;

  @OneToOne(() => User, (user) => user.eQN, {
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'id', referencedColumnName: 'eQNId' })
  userInfo?: User;
  @DeleteDateColumn()
  deletedAt?: Date;
}
