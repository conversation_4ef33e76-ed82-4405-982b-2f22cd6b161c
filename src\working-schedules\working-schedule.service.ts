import { ISearchQuery } from '../types/req';
import { database } from '../config';
import { WorkingSchedule } from '../models';
import { ERROR } from '../utils/error';
import { ISearchWorkingScheduleQuery } from './working-schedule';
import { createLinks } from '../utils/pagination';

export const create = async (data: WorkingSchedule) => {
  const repo = database.getRepository(WorkingSchedule);
  const newSchedule = repo.create(data);
  const saved = await repo.save(newSchedule);

  return { data: saved };
};

export const update = async (id: number, data: Partial<WorkingSchedule>) => {
  const repo = database.getRepository(WorkingSchedule);

  const existing = await repo.findOne({ where: { id }, withDeleted: true });
  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.update(id, data);
  return { message: '<PERSON>ậ<PERSON> nhật thành công' };
};

export const remove = async (id: number) => {
  const repo = database.getRepository(WorkingSchedule);
  const existing = await repo.findOne({ where: { id }, withDeleted: true });

  if (!existing) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  await repo.delete(id);
  return { message: 'Xóa thành công' };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(WorkingSchedule);
  const schedule = await repo.findOne({ where: { id }, withDeleted: true });

  if (!schedule) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  return schedule;
};

export const search = async (
  query: ISearchQuery<ISearchWorkingScheduleQuery>,
) => {
  const repo = database.getRepository(WorkingSchedule);
  const qb = repo.createQueryBuilder('schedule').withDeleted();

  if (query.codeOrg) {
    qb.andWhere('schedule.codeOrg = :codeOrg', { codeOrg: query.codeOrg });
  }

  if (query.season) {
    qb.andWhere('schedule.season = :season', { season: query.season });
  }

  if (query.status) {
    qb.andWhere('schedule.status = :status', { status: query.status });
  }

  if (query.startTime) {
    qb.andWhere('schedule.startTime >= :startTime', {
      startTime: query.startTime,
    });
  }

  if (query.endTime) {
    qb.andWhere('schedule.endTime <= :endTime', { endTime: query.endTime });
  }

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  qb.skip(skip).take(limit);

  const [data, total] = await qb.getManyAndCount();
  const links = createLinks(
    '/working-schedules/search?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: data,
    },
    links,
  };
};
