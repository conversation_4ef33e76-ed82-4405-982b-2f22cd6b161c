import { ISearchQuery } from '../types/req';
import { createLinks } from '../utils/pagination';
import { IGetTypesQuery } from './types';
import { database } from '../config';
import { Type } from '../models';
import { ERROR } from '../utils/error';

function createFullText(type: Partial<Type>): string {
  const fields = [type.name, type.desc, type.scope];
  return fields.filter(Boolean).join(' ');
}
export const findTypes = async (query: ISearchQuery<IGetTypesQuery>) => {
  const repoTypes = database.getRepository(Type);
  const conditions = repoTypes.createQueryBuilder('types').withDeleted();
  if (query.ids && query.ids.length > 0) {
    conditions.andWhere('types.id IN (:...ids)', {
      ids: query.ids,
    });
  }
  if (query.name && query.name != '') {
    conditions.andWhere('types.name like :name', {
      name: `%${query.name}%`,
    });
  }
  if (query.desc && query.desc != '') {
    conditions.andWhere('types.desc like :desc', {
      desc: `%${query.desc}%`,
    });
  }
  if (query.scopes && query.scopes.length > 0) {
    conditions.andWhere('types.scope IN (:...scopes)', {
      scopes: query.scopes,
    });
  } else if (query.scope && query.scope != '') {
    conditions.andWhere('types.scope like :scope', {
      scope: `%${query.scope}%`,
    });
  }
  if (query.status === undefined || query.status === true) {
    conditions.andWhere('types.status = :status', {
      status: 1,
    });
  } else {
    conditions.andWhere('types.status = :status', {
      status: 0,
    });
  }
  if (query.symbol && query.symbol != '') {
    conditions.andWhere('types.symbol like :symbol', {
      symbol: `%${query.symbol}%`,
    });
  }
  if (query.q && query.q != '') {
    conditions.andWhere('types.fullText like :q', { q: `%${query.q}%` });
  }
  const limit: number = query.limit ? Number(query.limit) : 100;
  const page: number = query.page ? Number(query.page) : 1;

  const totalItems = await conditions.getCount();
  const totalPages = Math.ceil(totalItems / limit);

  const links = createLinks('/types?', query, page, totalPages);

  const types = await conditions
    .skip((page - 1) * limit)
    .take(limit)
    .getMany();
  return {
    meta: {
      totalItems,
      itemsPerPage: query.limit,
      totalPages,
      currentPages: query.page,
      items: [...types],
    },
    links,
  };
};

export const updateType = async (id: number, body: Type) => {
  const repoTypes = database.getRepository(Type);
  const existed = await repoTypes.findOne({ where: { id }, withDeleted: true });
  if (!existed) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  const type = { ...existed, ...body };
  type.fullText = `${type.name} ${type.desc ? type.desc : ''} ${
    type.scope ? type.scope : ''
  }`;

  await repoTypes.save(type);

  const res = await repoTypes.findOne({ where: { id }, withDeleted: true });
  return { res };
};

export const findTypeById = async (id: number) => {
  const repoType = database.getRepository(Type);
  const type = await repoType.findOne({ where: { id }, withDeleted: true });
  if (!type) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  return type;
};
export const insertType = async (body: Type) => {
  const repoTypes = database.getRepository(Type);
  const tmpType = await repoTypes.findOne({
    where: { name: body.name, scope: body.scope },
    withDeleted: true,
  });
  if (tmpType) {
    throw new Error(ERROR.DATA_EXISTED);
  }
  body.fullText = createFullText(body);
  if (body.status === undefined) {
    body.status = true;
  }
  const type = await repoTypes.save(body);
  return type;
};
