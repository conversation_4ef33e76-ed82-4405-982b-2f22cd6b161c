import dayjs from 'dayjs';
import { In } from 'typeorm';
import { database } from '../config';
import { LOG } from '../constants';
import { insertLog } from '../logs/logs.service';
import { Organization, Type, WorkingSchedule } from '../models';
import { AttendanceLog } from '../models/attendanceLog.model';
import { DailyStats } from '../models/dailyStats.model';
import { eQN } from '../models/eQN.model';
import { User } from '../models/user.model';
import {
  getAllChildOrganizations,
  getAllIdsByArray,
} from '../organizations/organization.service';
import { exportDoc } from '../utils/exportFile';
import { createLinks } from '../utils/pagination';
import { isCheckInValid } from './check-in.service';
import { ATTENDANCE_STATUS } from './constants';
import {
  IDashboard,
  IDashboardByOrgStats,
  IEQNTimeSheetDetail,
  ITimeKeepingDetail,
} from './interfaces/order.interface';
import {
  ICreateAttendanceLog,
  ICreateAttendanceReason,
  ITimesheetDetailQuery,
  ITimesheetListQuery,
  ITimesheetQuery,
} from './timesheet.dto';

export async function getAllCheckInTypes() {
  const checkInTypes = await database.getRepository(Type).find({
    where: { scope: 'CHECKIN_TYPE' },
    withDeleted: true,
  });
  return checkInTypes;
}

export async function getDashboardStats(
  query: ITimesheetQuery,
  user: User | undefined,
) {
  try {
    const startDate = query.startDate ? new Date(query.startDate) : new Date();
    const endDate = query.endDate ? new Date(query.endDate) : new Date();

    const allOrgWithChildren = await getAllChildOrganizations(
      query.orgCode,
      user,
    );

    if (!allOrgWithChildren || allOrgWithChildren.length === 0) {
      return [];
    }

    const orgIds = allOrgWithChildren.map((org) => `'${org.id}'`).join(',');

    const createQueryBuilder = database
      .getRepository(DailyStats)
      .createQueryBuilder('ds')
      .withDeleted()
      .where(`ds.orgId IN (${orgIds})`)
      .andWhere('ds.reportDate BETWEEN :startDate AND :endDate', {
        startDate: dayjs(startDate)
          .startOf('date')
          .format('YYYY-MM-DD HH:mm:ss'),
        endDate: dayjs(endDate).endOf('date').format('YYYY-MM-DD HH:mm:ss'),
      });
    const dailyStats = await createQueryBuilder.getMany();

    const totalCount = dailyStats.reduce(
      (acc, curr) => acc + (curr?.totalCount || 0),
      0,
    );
    const dungGioCount = dailyStats.reduce(
      (acc, curr) => acc + (curr?.dungGioCount || 0),
      0,
    );
    const khongChamCount = dailyStats.reduce(
      (acc, curr) => acc + (curr?.khongChamCount || 0),
      0,
    );
    const diMuonCount = dailyStats.reduce(
      (acc, curr) => acc + (curr?.diMuonCount || 0),
      0,
    );
    const vangCoLyDoCount = dailyStats.reduce(
      (acc, curr) => acc + (curr?.vangCoLyDoCount || 0),
      0,
    );
    const nghiPhepCount = dailyStats.reduce(
      (acc, curr) => acc + (curr?.nghiPhepCount || 0),
      0,
    );

    const dashboardStats: IDashboard = {
      totalCount,
      dungGioCount,
      khongChamCount,
      diMuonCount,
      vangCoLyDoCount,
      nghiPhepCount,
      dashboardByOrgStats: [],
    };

    const currentOrg =
      allOrgWithChildren.length > 0 ? allOrgWithChildren[0] : null;
    const childOrgs = currentOrg?.children || [currentOrg];

    for (const org of childOrgs) {
      const orgStats = dailyStats.filter((ds) => {
        return (
          ds?.orgId?.toLocaleLowerCase() ===
          org?.id?.toLocaleLowerCase().toString()
        );
      });
      const allOrgWithChildren = getAllIdsByArray(org?.children);

      const data = {
        organization: {
          id: org?.id,
          name: org?.name,
        },
        totalCount: orgStats.reduce(
          (acc, curr) => acc + (curr?.totalCount || 0),
          0,
        ),
        dungGioCount: orgStats.reduce(
          (acc, curr) => acc + (curr?.dungGioCount || 0),
          0,
        ),
        khongChamCount: orgStats.reduce(
          (acc, curr) => acc + (curr?.khongChamCount || 0),
          0,
        ),
        diMuonCount: orgStats.reduce(
          (acc, curr) => acc + (curr?.diMuonCount || 0),
          0,
        ),
        vangCoLyDoCount: orgStats.reduce(
          (acc, curr) => acc + (curr?.vangCoLyDoCount || 0),
          0,
        ),
        nghiPhepCount: orgStats.reduce(
          (acc, curr) => acc + (curr?.nghiPhepCount || 0),
          0,
        ),
      };

      // get stats for each org child
      for (const orgChild of allOrgWithChildren) {
        const orgChildStats = dailyStats.filter((ds) => {
          return (
            ds?.orgId?.toLocaleLowerCase() ===
            orgChild?.id?.toLocaleLowerCase().toString()
          );
        });

        data.totalCount += orgChildStats.reduce(
          (acc, curr) => acc + (curr?.totalCount || 0),
          0,
        );
        data.dungGioCount += orgChildStats.reduce(
          (acc, curr) => acc + (curr?.dungGioCount || 0),
          0,
        );
        data.khongChamCount += orgChildStats.reduce(
          (acc, curr) => acc + (curr?.khongChamCount || 0),
          0,
        );
        data.diMuonCount += orgChildStats.reduce(
          (acc, curr) => acc + (curr?.diMuonCount || 0),
          0,
        );
        data.vangCoLyDoCount += orgChildStats.reduce(
          (acc, curr) => acc + (curr?.vangCoLyDoCount || 0),
          0,
        );
        data.nghiPhepCount += orgChildStats.reduce(
          (acc, curr) => acc + (curr?.nghiPhepCount || 0),
          0,
        );
      }
      // push data to dashboard
      dashboardStats.dashboardByOrgStats.push(data as IDashboardByOrgStats);
    }

    return dashboardStats;
  } catch (error) {
    // console.error('Failed to get dashboard stats:', error);
    throw error;
  }
}

export async function getAttendanceReport(query: ITimesheetQuery) {
  const qb = database.createQueryBuilder();

  try {
    const startDate = query.startDate
      ? new Date(query.startDate)
      : new Date(new Date().setDate(1));
    const endDate = query.endDate ? new Date(query.endDate) : new Date();

    const report = await qb
      .withDeleted()
      .select([
        'eQN.id as eQNId',
        'eQN.fullName as name',
        'eQN.orgCode',
        'org.name as department',
        'COUNT(al.id) as totalDays',
        'COUNT(CASE WHEN al.status = :granted THEN 1 END) as onTimeDays',
        'COUNT(CASE WHEN al.status = :denied THEN 1 END) as lateDays',
        'MIN(al.checkInTime) as earliestCheckIn',
        'MAX(al.checkInTime) as latestCheckIn',
      ])
      .from('AttendanceLog', 'al')
      .leftJoin('eQN', 'eQN', 'eQN.id = al.userId')
      .leftJoin('Organization', 'org', 'org.code = eQN.orgCode')
      .where('al.checkInTime BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      })
      .andWhere(query.orgCode ? 'eQN.orgCode = :orgCode' : '1=1', {
        orgCode: query.orgCode,
      })
      .andWhere(query.eQNId ? 'al.userId = :eQNId' : '1=1', {
        eQNId: query.eQNId,
      })
      .groupBy('eQN.id')
      .addGroupBy('eQN.fullName')
      .addGroupBy('eQN.orgCode')
      .addGroupBy('org.name')
      .setParameter('granted', 'granted')
      .setParameter('denied', 'denied')
      .getRawMany();

    return {
      startDate,
      endDate,
      report,
    };
  } catch (error) {
    // console.error('Failed to get attendance report:', error);
    throw error;
  }
}

export async function createAttendanceLog(data: ICreateAttendanceLog) {
  try {
    const eQNData = await database.getRepository(eQN).findOne({
      where: { id: data.eQNId },
      relations: ['organization'],
      withDeleted: true,
    });

    if (!eQNData) {
      throw new Error('eQN not found');
    }

    // get all ancestors of the organization
    const ancestors = await database
      .getTreeRepository(Organization)
      .findAncestors(eQNData.organization as Organization);

    if (!ancestors) {
      throw new Error('Organization not found');
    }

    // get all working schedules of the organization and its ancestors
    const orgsCodes = ancestors.map((org) => org.code);
    const workingSchedule = await database.getRepository(WorkingSchedule).find({
      where: [
        {
          codeOrg: In(orgsCodes),
        },
      ],
      withDeleted: true,
    });

    if (!workingSchedule) {
      throw new Error('workingSchedule not found');
    }
    const checkInStatus = isCheckInValid(dayjs(data.dateUTC), workingSchedule);
    console.log(`checkInStatus`, checkInStatus);

    const findAttendanceLog = await database
      .getRepository(AttendanceLog)
      .createQueryBuilder('al')
      .withDeleted()
      .where('al.qneid = :eQNId', { eQNId: data.eQNId })
      .andWhere('al.checkDate >= :checkDateStart', {
        checkDateStart: dayjs(data.dateUTC)
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      })
      .andWhere('al.checkDate <= :checkDateEnd', {
        checkDateEnd: dayjs(data.dateUTC)
          .endOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      })
      .getOne();

    if (findAttendanceLog) {
      throw new Error('Attendance log already exists');
    }

    const attendanceLog = new AttendanceLog();
    attendanceLog.qneid = data.eQNId;
    attendanceLog.checkInTime = new Date(data.dateUTC);

    attendanceLog.lateDuration = 0;
    attendanceLog.approvedBy = '';
    attendanceLog.approvedAt = new Date();
    attendanceLog.locationCheck = '';
    attendanceLog.checkTypeId = data.type ? Number(data.type) : 1;
    attendanceLog.checkDate = new Date(data.dateUTC);
    attendanceLog.biometricData = data.biometricData || '';
    attendanceLog.attendanceImage = data.attendanceImage || '';

    attendanceLog.requiredStartTime =
      checkInStatus.requiredCheckInTime.toDate();
    attendanceLog.codeOrg = eQNData.orgCode;
    attendanceLog.status = checkInStatus.status;
    attendanceLog.locationCheck = data.deviceName || '';
    attendanceLog.lateDuration =
      checkInStatus.status === ATTENDANCE_STATUS.DI_MUON
        ? checkInStatus.minutesDiff
        : 0;
    const savedLog = await database
      .getRepository(AttendanceLog)
      .save(attendanceLog);

    await insertLog({
      typeId: LOG.ATTENDANCELOG,
      userId: 20,
      ip: 'localhost',
      content: `Đồng bộ dữ liệu chấm công vào hệ thống quản lý vào ra`,
    });
    return savedLog;
  } catch (error) {
    // console.error('Failed to create attendance log:', error);
    throw error;
  }
}

export async function getDashboardDetailByEQN(
  query: ITimesheetDetailQuery,
  user?: User | undefined, // eslint-disable-line @typescript-eslint/no-unused-vars
) {
  try {
    const startDate = query.startDate
      ? new Date(query.startDate)
      : dayjs().startOf('month').toDate();
    const endDate = query.endDate ? new Date(query.endDate) : new Date();

    const qbAttendanceLog = database
      .getRepository(AttendanceLog)
      .createQueryBuilder('al')
      .withDeleted()
      .leftJoinAndSelect('al.eQN', 'eQN', 'eQN.id = al.qneid')
      .leftJoinAndSelect('al.organization', 'org', 'org.code = al.codeOrg')
      .leftJoinAndSelect(
        'eQN.position',
        'position',
        'position.id = eQN.positionId',
      )
      .leftJoinAndSelect('eQN.Rank', 'rank', 'rank.id = eQN.rankId')
      .where(`al.checkInTime BETWEEN :startDate AND :endDate`, {
        startDate: dayjs(startDate)
          .startOf('date')
          .format('YYYY-MM-DD HH:mm:ss'),
        endDate: dayjs(endDate).endOf('date').format('YYYY-MM-DD HH:mm:ss'),
      })
      .andWhere('al.qneid = :eQNId', { eQNId: query.eQNId })
      .select([
        'al.id',
        'al.checkInTime',
        'al.checkDate',
        'al.status',
        'al.reasonText',
        'al.locationCheck',
        'al.lateDuration',
        'al.approvedBy',
        'al.approvedAt',
        'al.checkTypeId',
        'al.attendanceImage',
        'al.qneid',
        'al.biometricData',
        'eQN',
        'org',
        'position',
        'rank',
      ])
      .orderBy('al.checkInTime', 'ASC');

    // Calculate summary statistics
    const attendanceLogs = await qbAttendanceLog.getMany();
    const result: IEQNTimeSheetDetail = {
      eQN: attendanceLogs?.[0]?.eQN as eQN,
      attendanceLog: [],
      totalCount: 0,
      dungGioCount: 0,
      khongChamCount: 0,
      diMuonCount: 0,
      vangCoLyDoCount: 0,
      nghiPhepCount: 0,
    };

    const checkInTypes = await getAllCheckInTypes();
    for (const log of attendanceLogs) {
      const attendanceLogData: ITimeKeepingDetail = {
        id: log?.id?.toString(),
        check_in_time: log?.checkInTime?.toISOString(),
        name: log?.eQN?.fullName,
        rank: log?.eQN?.Rank?.name,
        position: log?.eQN?.position?.name,
        location: log?.locationCheck,
        method: getCheckInType(log.checkTypeId as number, checkInTypes),
        unit: log?.organization?.name,
        time: log?.checkInTime?.toISOString(),
        late: log?.lateDuration ? log.lateDuration.toString() : '0',
        reason: log?.reasonText,
        status: log?.status,
        eqnId: log?.eQN?.id,
        check_type_id: log.checkTypeId,
        checkInType: getCheckInType(log.checkTypeId as number, checkInTypes),
      };
      result.attendanceLog.push(attendanceLogData);
    }

    result.totalCount = attendanceLogs.length;
    result.dungGioCount = attendanceLogs.filter(
      (log) => log.status === ATTENDANCE_STATUS.DUNG_GIO,
    ).length;
    result.khongChamCount = attendanceLogs.filter(
      (log) => log.status === ATTENDANCE_STATUS.KHONG_CHAM,
    ).length;
    result.diMuonCount = attendanceLogs.filter(
      (log) => log.status === ATTENDANCE_STATUS.DI_MUON,
    ).length;
    result.vangCoLyDoCount = attendanceLogs.filter(
      (log) => log.status === ATTENDANCE_STATUS.VANG_CO_LY_DO,
    ).length;
    result.nghiPhepCount = attendanceLogs.filter(
      (log) => log.status === ATTENDANCE_STATUS.NGHI_PHEP,
    ).length;
    return result;
  } catch (error: unknown) {
    if (error instanceof Error) {
      throw new Error(`Failed to get dashboard detail: ${error.message}`);
    }
    throw new Error('Failed to get dashboard detail: Unknown error occurred');
  }
}

export async function createAttendanceReason(data: ICreateAttendanceReason) {
  try {
    const eQNData = await database.getRepository(eQN).findOne({
      where: { id: data.eQNId },
      relations: ['organization'],
      withDeleted: true,
    });

    if (!eQNData) {
      throw new Error('eQN not found');
    }

    let attendanceLog = await database
      .getRepository(AttendanceLog)
      .createQueryBuilder('al')
      .withDeleted()
      .where('al.qneid = :eQNId', { eQNId: data.eQNId })
      .andWhere('al.checkDate >= :checkDateStart', {
        checkDateStart: dayjs(data.date)
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      })
      .andWhere('al.checkDate <= :checkDateEnd', {
        checkDateEnd: dayjs(data.date)
          .endOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      })
      .getOne();

    if (!attendanceLog) {
      attendanceLog = new AttendanceLog();
      attendanceLog.qneid = data.eQNId;
      attendanceLog.checkDate = new Date(data.date);
      attendanceLog.checkInTime = new Date(data.date);
      attendanceLog.checkTypeId = 2;
      attendanceLog.status = ATTENDANCE_STATUS.VANG_CO_LY_DO;
      attendanceLog.codeOrg = eQNData?.organization?.code;
      attendanceLog.reasonText = data.reason;
      attendanceLog.locationCheck = '';
      attendanceLog.lateDuration = 0;
      attendanceLog.approvedBy = '';
      attendanceLog.approvedAt = new Date();
      attendanceLog.requiredStartTime = new Date(data.date);
      attendanceLog.attendanceImage = '';
    }

    // check if attendance log is already marked as Dung Gio or Nghi Phep
    if (attendanceLog) {
      if (attendanceLog.status === ATTENDANCE_STATUS.NGHI_PHEP) {
        throw new Error('Attendance log already has a reason');
      }

      if (attendanceLog.status === ATTENDANCE_STATUS.DUNG_GIO) {
        throw new Error('Attendance log is already marked as Dung Gio');
      }
    }

    // get daily stats
    const dailyStats = await database
      .getRepository(DailyStats)
      .createQueryBuilder('ds')
      .withDeleted()
      .where('ds.orgId = :orgId', { orgId: eQNData?.organization?.id || '' })
      .andWhere('ds.reportDate = :reportDate', {
        reportDate: dayjs(data.date)
          .startOf('day')
          .format('YYYY-MM-DD HH:mm:ss'),
      })
      .getOne();

    if (!dailyStats) {
      throw new Error('Daily stats not found');
    }
    (dailyStats.vangCoLyDoCount as number)++;
    await database.getRepository(DailyStats).save(dailyStats);

    attendanceLog.reasonText = data.reason;
    const updatedLog = await database
      .getRepository(AttendanceLog)
      .save(attendanceLog);
    return updatedLog;
  } catch (error) {
    // console.error('Failed to create attendance reason:', error);
    throw error;
  }
}

export async function getAttendanceList(
  query: ITimesheetListQuery,
  user?: User,
) {
  try {
    const startDate = query.startDate
      ? new Date(query.startDate)
      : new Date(new Date().setDate(1));
    const endDate = query.endDate ? new Date(query.endDate) : new Date();

    console.log(`query`, query);

    const { limit, page, status, location, checkTypes, keyword } = query;
    const skip = (page - 1) * limit;

    const qb = database
      .getRepository(AttendanceLog)
      .createQueryBuilder('al')
      .withDeleted()
      .leftJoinAndSelect('al.eQN', 'eQN', 'eQN.id = al.qneid')
      .leftJoinAndSelect('al.organization', 'org', 'org.code = al.codeOrg')
      .leftJoinAndSelect(
        'eQN.position',
        'position',
        'position.id = eQN.positionId',
      )
      .leftJoinAndSelect('eQN.Rank', 'rank', 'rank.id = eQN.rankId')
      .where('al.checkInTime BETWEEN :startDate AND :endDate', {
        startDate: dayjs(startDate)
          .startOf('date')
          .format('YYYY-MM-DD HH:mm:ss'),
        endDate: dayjs(endDate).endOf('date').format('YYYY-MM-DD HH:mm:ss'),
      })
      .select([
        'al.id',
        'al.checkInTime',
        'al.checkDate',
        'al.status',
        'al.reasonText',
        'al.locationCheck',
        'al.lateDuration',
        'al.approvedBy',
        'al.approvedAt',
        'al.checkTypeId',
        'al.attendanceImage',
        'al.qneid',
        'al.biometricData',
        'eQN',
        'org',
        'position',
        'rank',
      ])
      .orderBy('al.checkInTime', 'ASC')
      .skip(skip)
      .take(limit);

    const allOrgWithChildren = await getAllChildOrganizations(
      query.orgCode,
      user,
    );
    if (!allOrgWithChildren || allOrgWithChildren.length === 0) {
      return [];
    }
    const orgCodeArray = allOrgWithChildren
      .map((org) => `'${org.code}'`)
      .join(',');

    if (orgCodeArray) {
      // const orgCodeArray = orgCode.split(',');
      qb.andWhere(`al.codeOrg IN (${orgCodeArray})`);
    }

    if (status && status !== '') {
      const statusArray = status.split(',');
      qb.andWhere('al.status IN (:...status)', { status: statusArray });
    }

    if (location && location !== '') {
      const locationArray = location.split(',');
      qb.andWhere('al.locationCheck IN (:...location)', {
        location: locationArray,
      });
    }

    if (checkTypes) {
      const checkTypesArray = checkTypes.split(',');
      qb.andWhere('al.checkTypeId IN (:...checkTypes)', {
        checkTypes: checkTypesArray,
      });
    }

    if (keyword && keyword !== '') {
      qb.andWhere('eQN.fullName LIKE :keyword', {
        keyword: `%${query.keyword}%`,
      });
    }

    const [data, total] = await qb.getManyAndCount();
    const checkInTypes = await getAllCheckInTypes();

    const items: ITimeKeepingDetail[] = data.map((log) => ({
      id: log?.id?.toString(),
      check_in_time: log?.checkInTime?.toISOString(),
      name: log?.eQN?.fullName,
      rank: log?.eQN?.Rank?.name,
      position: log?.eQN?.position?.name,
      location: log?.locationCheck,
      method: getCheckInType(log?.checkTypeId as number, checkInTypes),
      unit: log?.organization?.name,
      time: log?.checkInTime?.toISOString(),
      late: log?.lateDuration ? log.lateDuration.toString() : '0',
      reason: log?.reasonText,
      status: log?.status,
      eqnId: log?.eQN?.id,
      date: log?.checkDate?.toISOString(),
      check_type_id: log?.checkTypeId ?? 0,
      checkInType: getCheckInType(log?.checkTypeId as number, checkInTypes),
      biometricData: log?.biometricData,
      attendanceImage: log.attendanceImage,
    }));

    const totalPages = Math.ceil(total / limit);
    const links = createLinks('/attendance/list?', query, page, totalPages);

    return {
      meta: {
        totalItems: total,
        itemsPerPage: limit,
        totalPages,
        currentPage: page,
        items,
      },
      links,
    };
  } catch (error) {
    // console.error('Failed to get attendance list:', error);
    throw error;
  }
}

export async function exportDocxAttendanceList(query: ITimesheetListQuery) {
  const startDate = query.startDate
    ? new Date(query.startDate)
    : new Date(new Date().setDate(1));
  const endDate = query.endDate ? new Date(query.endDate) : new Date();

  const { orgCode, status, location, checkTypes } = query;

  const qb = database
    .getRepository(AttendanceLog)
    .createQueryBuilder('al')
    .withDeleted()
    .leftJoinAndSelect('al.eQN', 'eQN', 'eQN.id = al.qneid')
    .leftJoinAndSelect('al.organization', 'org', 'org.code = al.codeOrg')
    .leftJoinAndSelect(
      'eQN.position',
      'position',
      'position.id = eQN.positionId',
    )
    .leftJoinAndSelect('eQN.Rank', 'rank', 'rank.id = eQN.rankId')
    .where('al.checkInTime BETWEEN :startDate AND :endDate', {
      startDate: dayjs(startDate).startOf('date').format('YYYY-MM-DD HH:mm:ss'),
      endDate: dayjs(endDate).endOf('date').format('YYYY-MM-DD HH:mm:ss'),
    })
    .select([
      'al.id',
      'al.checkInTime',
      'al.checkDate',
      'al.status',
      'al.reasonText',
      'al.locationCheck',
      'al.lateDuration',
      'al.approvedBy',
      'al.approvedAt',
      'al.checkTypeId',
      'al.attendanceImage',
      'al.qneid',
      'eQN',
      'org',
      'position',
      'rank',
    ])
    .orderBy('al.checkInTime', 'ASC');

  if (orgCode && orgCode !== '') {
    const orgCodeArray = orgCode.split(',');
    qb.andWhere('al.codeOrg IN (:...orgCode)', { orgCode: orgCodeArray });
  }

  if (status && status !== '') {
    const statusArray = status.split(',');
    qb.andWhere('al.status IN (:...status)', { status: statusArray });
  }

  if (location && location !== '') {
    const locationArray = location.split(',');
    qb.andWhere('al.locationCheck IN (:...location)', {
      location: locationArray,
    });
  }

  if (checkTypes) {
    const checkTypesArray = checkTypes.split(',');
    qb.andWhere('al.checkTypeId IN (:...checkTypes)', {
      checkTypes: checkTypesArray,
    });
  }

  const [data] = await qb.getManyAndCount();
  const dataForDocx = data.map((log, index) => ({
    stt: index + 1,
    thoiGianChamCong: dayjs(log.checkInTime).format('HH:mm:ss DD/MM/YYYY'),
    ten: log?.eQN?.fullName,
    capBac: log?.eQN?.Rank?.shortName
      ? log?.eQN?.Rank?.shortName
      : log?.eQN?.Rank?.name
        ? log?.eQN?.Rank?.name
        : '',
    chucVu: log?.eQN?.position?.shortName
      ? log?.eQN?.position?.shortName
      : log?.eQN?.position?.name
        ? log?.eQN?.position?.name
        : '',
    location: log?.locationCheck ?? '',
    donVi: log?.organization?.name ?? '',
    khoangThoiGianMuon: log?.lateDuration ? log.lateDuration.toString() : '',
    thongTin: log.reasonText ?? '',
  }));
  const pathOutput = await exportDoc(
    `trich_xuat_cham_cong.docx`,
    `trich_xuat_cham_cong_${Date.now()}.docx`,
    { data: dataForDocx },
  );
  return pathOutput;
}

function getCheckInType(checkTypeId: number, checkInTypes: Type[]) {
  const checkInType = checkInTypes.find(
    (type) => type.value === checkTypeId.toString(),
  );
  return checkInType?.name ?? '';
}
