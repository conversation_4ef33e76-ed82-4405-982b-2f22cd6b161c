import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';

@Entity('guardPosts')
export class GuardPost extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    type: 'nvarchar',
    length: 250,
  })
  name?: string;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;

  @Column({
    name: 'order_num',
    type: 'int',
  })
  orderNum?: number;

  @Column({
    type: 'nvarchar',
    length: 500,
  })
  location?: string;

  @Column({
    type: 'nvarchar',
    length: 500,
    nullable: true,
  })
  code?: string;
}
