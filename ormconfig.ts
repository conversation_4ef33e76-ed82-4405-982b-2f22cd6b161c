require('dotenv').config();
import { DataSource } from 'typeorm';

export default new DataSource({
  type: 'mssql',
  host: process.env.MSSQL_HOST,
  port: parseInt(process.env.MSSQL_PORT!),
  username: process.env.MSSQL_USER,
  password: process.env.MSSQL_PW,
  database: process.env.MSSQL_DB,
  synchronize: false,
  logging: false,
  entities: ['src/models/*.ts'],
  migrations: ['src/migration/*.ts'],
  subscribers: [],
  options: {
    encrypt: process.env.NODE_ENV === 'production',
    trustServerCertificate: process.env.NODE_ENV !== 'production',
  },
});
