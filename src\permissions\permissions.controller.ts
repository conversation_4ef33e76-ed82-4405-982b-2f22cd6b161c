import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { Permission } from '../models';
import { ISearchQuery } from '../types/req';

import { ERROR } from '../utils/error';
import {
  insertPermission,
  updatePermission,
  deletePermission,
  findById,
  findTreeById,
  findAll,
  searchPermissions,
  findAncestorsById,
  findTreeAll,
  changeStatusPermission,
  findGroupTree,
} from './permissions.service';
import { ISearchPermissionQuery } from './permissions';

export const postPermission = async (
  req: Request & { body: Permission },
  res: Response,
  next: NextFunction,
) => {
  try {
    const body: Permission = req.body;
    const newPer = await insertPermission(body, req.user, req.ipAddress);
    if (newPer.error) {
      return res.status(status.BAD_REQUEST).json(newPer.error);
    }
    return res.json(newPer.newPermission);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const putPermission = async (
  req: Request & { body: Partial<Permission> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const body: Permission = req.body;
    const id = Number(req.params.id);
    const putPermission = await updatePermission(id, body);

    if (putPermission.error)
      return res.status(status.BAD_REQUEST).json(putPermission.error);
    return res.json(putPermission.updated);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Permission không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const dropPermission = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const deletedObj = await deletePermission(Number(req.params.id));

    return res.json(deletedObj);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Permission cần xóa không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }

    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const getById = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = Number(req.params.id);
    const permission = await findById(id);
    return res.json(permission);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Permision không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const getTreeById = async (
  req: Request & { params: { id: number } } & { query: { depth?: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const permission = req.query.depth
      ? await findTreeById(Number(req.params.id), Number(req.query.depth))
      : await findTreeById(Number(req.params.id));
    return res.json(permission);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Không tìm thấy dữ liệu',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    next(e);
  }
};
export const getAll = async (
  req: Request & { query: ISearchQuery<ISearchPermissionQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const permissions = await findAll(req.query);
    return res.json(permissions);
  } catch (e) {
    next(e);
  }
};
export const getSearch = async (
  req: Request & { query: ISearchQuery<ISearchPermissionQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const permissions = await searchPermissions(req.query);
    return res.json(permissions);
  } catch (e) {
    next(e);
  }
};
export const getAncestorsById = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = req.params.id;
    const ancestors = await findAncestorsById(id);
    if (ancestors) return res.json(ancestors);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Quyền không tồn tại',
        statusCode: 400,
        errors: { field: 'id', message: 'Id không tồn tại' },
      });
    }
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
export const getTreeAll = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const permissions = await findTreeAll();
    return res.json(permissions);
  } catch (e) {
    next(e);
  }
};

export const updateStatusPermissions = async (
  req: Request & { body: number[] },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = await changeStatusPermission(req.body);
    return res.json(user);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getGroupTree = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const permissions = await findGroupTree();
    return res.json(permissions);
  } catch (e) {
    next(e);
  }
};
