import { MilitaryType } from '../../models';
import { database } from '../../config';
import * as fs from 'fs';
import path from 'path';
interface type {
  id: string;
  name: string;
  short_name: string;
  // [key: string]: any; // Nếu có các thuộc t<PERSON>h kh<PERSON>c
}
export const syncAllType = async () => {
  try {
    const filePath = path.join(__dirname, '../../type.json');
    const fileContent = fs.readFileSync(filePath, 'utf-8'); // Đọc nội dung file
    const staticData: type[] = JSON.parse(fileContent);
    console.log('Initializing sync for rank');
    const positionRepo = database.getRepository(MilitaryType);
    const sortedItems = staticData;
    let updatedCount = 0;
    const createdCount = 0;
    //  console.log(`Đã lấy ${sortedItems.length} bản ghi cho DonVi`);
    console.log('Tiến hành xóa mềm tất cả các bản ghi trong bảng eQN');
    await positionRepo.createQueryBuilder().softDelete().execute(); //Xóa mềm tất cả các bản ghi trong bảng
    let codeNull = 0;
    for (const item of sortedItems) {
      console.log(`Bản ghi số: ${sortedItems.indexOf(item) + 1}`);
      const existingRecord = await positionRepo.findOne({
        where: { id: item.id },
        withDeleted: true,
      });
      // lấy mpath fullname từ bảng DonVi
      if (existingRecord) {
        console.log(`đang cập nhật bản ghi với id: ${item.id}`);
        await positionRepo.save(existingRecord);
        await positionRepo.update(
          { id: item.id },
          {
            ...existingRecord,
            // id: item.id,
            name: item.name,
            shortName: item.short_name,
            isEnable: true,
          },
        );
        updatedCount++;
      } else {
        console.log(`tạo mới bản ghi với id: ${item.id}`);
        await positionRepo.save(
          positionRepo.create({
            id: item.id,
            name: item.name,
            shortName: item.short_name,
            isEnable: true,
          }),
        );
      }
      codeNull++; // eslint-disable-line
    }
    //console.log(`Đã xử lý ${staticData.length} bản ghi cho DonVi:`);
    console.log(`- Cập nhật: ${updatedCount} bản ghi`);
    console.log(`- Tạo mới: ${createdCount} bản ghi`);
    // } else {
    //     console.log('Không có dữ liệu cho DonVi');
    // }
  } catch (error) {
    console.log('Error syncing DonVi:', error);
  }
};
export const syncType = async () => {
  try {
    await syncAllType();
  } catch (error) {
    console.error(`Lỗi khi xử lý DonVi`, error);
  }
};
