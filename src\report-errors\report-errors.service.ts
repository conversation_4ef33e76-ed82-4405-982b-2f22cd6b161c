import { And, FindOptionsWhere, In, <PERSON><PERSON><PERSON>, Like, <PERSON><PERSON>han } from 'typeorm';
import { database } from '../config';
import { File, HandleError, ReportError, User } from '../models';
import {
  IAddUpdateHandleError,
  IGetReportErrorsQuery,
  IPostReportError,
  IPutReportError,
} from './report-errors';
import { ERROR } from '../utils/error';
import { deleteFiles } from '../files/files.service';
import { ISearchQuery } from '../types/req';
import { createLinks, DEFAULT_LIMIT } from '../utils/pagination';
import { Request } from 'express';
import { FileTypes, RolePerms } from '../constants';

export const createReportError = async (
  data: IPostReportError,
  user: User,
): Promise<ReportError> => {
  const reportErrorRepo = database.getRepository(ReportError);
  const fileRepo = database.getRepository(File);
  let report = reportErrorRepo.create({
    content: data.content,
    type: data.type,
    time: data.time,
    status: 'new',
    userId: user.id,
  });
  report = await reportErrorRepo.save(report);
  if (data.fileIds?.length) {
    const files = await fileRepo.find({
      where: { id: In(data.fileIds), userId: user.id },
    });
    if (files.length > 0) {
      await fileRepo.update(
        { id: In(data.fileIds) },
        { reportErrorId: report.id },
      );
    }
  }

  return report;
};

export const deleteReportError = async (
  id: number,
  req: Request,
): Promise<{ success: boolean }> => {
  const isAdmin = req?.permsArr?.includes(RolePerms.editReportError) || false;
  const reportErrorRepo = database.getRepository(ReportError);

  const report = await reportErrorRepo.findOne({
    where: { id },
    relations: { files: true, handleError: true },
  });
  if (!report) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  if (!isAdmin && report.userId !== req?.user?.id) {
    throw new Error(ERROR.BAD_REQUEST);
  }
  if (report.handleError) {
    throw new Error(ERROR.HANDLE_ERROR_EXISTED);
  }

  if (report.files) {
    await deleteFiles(
      report.files
        .map((file) => file.id)
        .filter((id): id is number => id !== undefined),
      FileTypes.errors,
    );
  }
  await reportErrorRepo.remove(report);

  return { success: true };
};

export const findReportErrors = async (
  searchQuery: ISearchQuery<IGetReportErrorsQuery>,
  req: Request,
) => {
  const isAdmin = req?.permsArr?.includes(RolePerms.editReportError) || false;
  const reportErrorRepo = database.getRepository(ReportError);

  const limit = searchQuery.limit ?? DEFAULT_LIMIT;
  const page = searchQuery.page ?? 1;

  const where: FindOptionsWhere<ReportError> = {};
  if (searchQuery.search) {
    where.content = Like(`%${searchQuery.search}%`);
  }

  if (searchQuery.type) {
    where.type = searchQuery.type;
  }
  if (searchQuery.startDate && searchQuery.endDate) {
    where.time = And(
      LessThan(new Date(searchQuery.endDate)),
      MoreThan(new Date(searchQuery.startDate)),
    );
  }
  if (searchQuery.status) {
    where.status = searchQuery.status;
  }
  if (!isAdmin) {
    where.userId = req?.user?.id || 0;
  }

  const [reportErrors, total] = await reportErrorRepo.findAndCount({
    where,
    order: {
      ...(searchQuery.sortBy
        ? { [searchQuery.sortBy]: searchQuery.order ?? 'DESC' }
        : { createdAt: 'DESC' }),
    },
    relations: { user: { role: true, organization: true } },
    take: limit,
    skip: (page - 1) * limit,
  });

  const totalPages = Math.ceil(total / limit);
  const links = createLinks('/report-errors?', searchQuery, page, totalPages);

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages,
      currentPage: page,
      items: reportErrors,
    },
    links,
  };
};

export const editReportError = async (
  id: number,
  data: IPutReportError,
  req: Request,
) => {
  const isAdmin = req?.permsArr?.includes(RolePerms.editReportError) || false;
  const reportErrorRepo = database.getRepository(ReportError);

  const report = await reportErrorRepo.findOne({
    where: { id, ...(isAdmin ? {} : { userId: req?.user?.id || 0 }) },
  });
  if (!report) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  if (data.content) {
    report.content = data.content;
  }
  if (data.type) {
    report.type = data.type;
  }
  if (data.time) {
    report.time = new Date(data.time);
  }
  if (data.status && isAdmin) {
    report.status = data.status;
  }

  if (data.fileIds?.length) {
    const fileRepo = database.getRepository(File);
    const files = await fileRepo.find({
      where: {
        id: In(data.fileIds),
        ...(isAdmin ? {} : { userId: req?.user?.id || 0 }),
      },
    });
    if (files.length > 0) {
      await fileRepo.update(
        { id: In(data.fileIds) },
        { reportErrorId: report.id },
      );
    }
  }

  return await reportErrorRepo.save(report);
};

export const findReportError = async (id: number, req: Request) => {
  const isAdmin = req?.permsArr?.includes(RolePerms.editReportError) || false;
  const reportErrorRepo = database.getRepository(ReportError);
  const report = await reportErrorRepo.findOne({
    where: { id },
    relations: {
      files: true,
      handleError: true,
      user: { role: true, organization: true },
    },
  });
  if (!report) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }
  if (!isAdmin && report.userId !== req?.user?.id) {
    throw new Error(ERROR.BAD_REQUEST);
  }
  return report;
};

export const addUpdateHandleError = async (
  reportErrorId: number,
  payload: IAddUpdateHandleError,
  req: Request,
) => {
  const handleErrorRepo = database.getRepository(HandleError);
  const reportErrorRepo = database.getRepository(ReportError);

  const reportError = await reportErrorRepo.findOne({
    where: { id: reportErrorId },
  });
  if (!reportError) {
    throw new Error(ERROR.DATA_NOT_FOUND);
  }

  let handleError = await handleErrorRepo.findOne({ where: { reportErrorId } });

  if (!handleError) {
    handleError = handleErrorRepo.create({
      reportErrorId,
      dueDateStart: new Date(payload.dueDateStart),
      dueDateEnd: new Date(payload.dueDateEnd),
      content: payload.content,
      status: payload.status,
      userId: req?.user?.id || 0,
    });
  } else {
    handleError.dueDateStart = new Date(payload.dueDateStart);
    handleError.dueDateEnd = new Date(payload.dueDateEnd);
    handleError.content = payload.content;
    handleError.status = payload.status;
  }

  return await handleErrorRepo.save(handleError);
};
