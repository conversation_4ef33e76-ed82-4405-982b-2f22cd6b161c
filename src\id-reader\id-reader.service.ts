import WebSocket from 'ws';
import log from '../utils/log';
import config from '../config';
import { io } from '../socket/socket.service';

let wsClient: WebSocket | null = null;
let reconnectInterval: NodeJS.Timeout | null = null;
const RECONNECT_INTERVAL = 5000; // 5 seconds

export function initIdReaderWebSocket() {
  if (!config.idReaderHost) {
    log.info('ID_READER_HOST not set, skipping ID reader WebSocket connection');
    return;
  }

  const wsUrl = config.idReaderHost;
  log.info(`Connecting to ID reader WebSocket at ${wsUrl}`);

  try {
    if (wsClient) {
      wsClient.terminate();
    }

    if (reconnectInterval) {
      clearInterval(reconnectInterval);
      reconnectInterval = null;
    }

    wsClient = new WebSocket(wsUrl);

    wsClient.on('open', () => {
      log.info('Connected to ID reader WebSocket');
    });

    wsClient.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        log.debug(`Received data from ID reader: ${JSON.stringify(message)}`);

        if (message.success) {
          if (message.data && message.data.qrCode) {
            const qrCodeData = message.data.qrCode;
            log.info(`QR Code data received: ${qrCodeData}`);

            const [cccd, cmt, fullName, dateOfBirth, sex, address, issueDate] =
              qrCodeData.split('|');

            const parsedData = {
              cccd,
              cmt,
              fullName,
              dateOfBirth,
              sex,
              address,
              issueDate,
            };

            log.info(`Parsed ID data: ${JSON.stringify(parsedData)}`);

            // if (io) {
            //   io.emit('id-reader-data', parsedData);
            //   log.debug('Emitted ID reader data to connected clients');
            // }
          } else {
            log.warn('Received success message but no QR code data');
          }
        } else {
          const errorMessage = message.message || 'Unknown error';
          log.warn(`ID reader error: ${errorMessage}`);

          if (io) {
            io.emit('id-reader-error', { error: errorMessage });
            log.debug('Emitted ID reader error to connected clients');
          }
        }
      } catch (error) {
        log.error('Error processing message from ID reader:', error);
      }
    });

    wsClient.on('error', (error) => {
      log.error('ID reader WebSocket error:', error);
    });

    wsClient.on('close', (code, reason) => {
      log.warn(
        `ID reader WebSocket connection closed. Code: ${code}, Reason: ${reason || 'No reason provided'}`,
      );

      if (!reconnectInterval) {
        log.info(
          `Will attempt to reconnect to ID reader in ${RECONNECT_INTERVAL}ms`,
        );
        reconnectInterval = setInterval(() => {
          log.info('Attempting to reconnect to ID reader WebSocket...');
          initIdReaderWebSocket();
        }, RECONNECT_INTERVAL);
      }
    });

    // Set up ping/pong to detect broken connections
    const pingInterval = setInterval(() => {
      if (wsClient && wsClient.readyState === WebSocket.OPEN) {
        wsClient.ping(undefined, undefined, (err) => {
          if (err) {
            log.error('ID reader WebSocket ping error:', err);
          }
        });
      } else {
        clearInterval(pingInterval);
      }
    }, 30000); // 30 seconds

    wsClient.on('pong', () => {
      log.debug('Received pong from ID reader WebSocket');
    });
  } catch (error) {
    log.error('Failed to connect to ID reader WebSocket:', error);

    if (!reconnectInterval) {
      reconnectInterval = setInterval(() => {
        log.info('Attempting to reconnect to ID reader WebSocket...');
        initIdReaderWebSocket();
      }, RECONNECT_INTERVAL);
    }
  }
}

/**
 * Get the current WebSocket client instance
 */
export function getIdReaderWebSocketClient(): WebSocket | null {
  return wsClient;
}

/**
 * Get the current status of the ID reader connection
 */
export function getIdReaderStatus(): {
  connected: boolean;
  readyState?: number;
} {
  if (!wsClient) {
    return { connected: false };
  }

  return {
    connected: wsClient.readyState === WebSocket.OPEN,
    readyState: wsClient.readyState,
  };
}

/**
 * Close the WebSocket connection
 */
export function closeIdReaderWebSocket() {
  if (wsClient) {
    wsClient.terminate();
    wsClient = null;
  }

  if (reconnectInterval) {
    clearInterval(reconnectInterval);
    reconnectInterval = null;
  }
}
