import Cronjob from 'node-cron';
import { join, parse } from 'path';
import { cwd } from 'process';
import config from '../config';
import log from '../utils/log';
import { readdirSync, rmSync } from 'fs';
import dayjs from 'dayjs';

export const removePreviewFileCron = () => {
  Cronjob.schedule('0 1 * * *', () => {
    log.info('Start remove preview file');
    const dir = join(cwd(), 'src', config.publicPreviewPath);
    const files = readdirSync(dir);
    files.forEach((file) => {
      const parsedName = parse(file);
      const timeString = parsedName?.name?.split('-')?.pop();
      const time = new Date(Number(timeString));
      const filePath = join(dir, file);
      if (dayjs(time).isBefore(dayjs().subtract(12, 'hour'))) {
        rmSync(filePath);
        log.info('Remove file:', filePath);
      }
    });
    log.info('End remove preview file');
  });
};
