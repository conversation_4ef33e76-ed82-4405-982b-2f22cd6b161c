import 'reflect-metadata';
import { DataSource } from 'typeorm';

const database = new DataSource({
  type: 'mssql',
  host: process.env.MSSQL_HOST,

  port: parseInt(process.env.MSSQL_PORT!),
  username: process.env.MSSQL_USER,
  password: process.env.MSSQL_PW,
  database: process.env.MSSQL_DB,
  // synchronize: process.env.NODE_ENV clear=== 'test',
  synchronize: false,
  logging: false,
  entities: ['src/models/*.ts'],
  options: {
    encrypt: true, // If encryption is used
    trustServerCertificate: true, // Allow self-signed certificates
    enableArithAbort: true, // Other options as needed
  },
});

export default database;
