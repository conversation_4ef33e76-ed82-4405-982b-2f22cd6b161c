import { MigrationInterface, QueryRunner } from "typeorm";

export class T1753771074423 implements MigrationInterface {
    name = 'T1753771074423'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "loai_hinh_truc_chi_tiet" DROP COLUMN "maLoaiHinhTruc"`);
        await queryRunner.query(`ALTER TABLE "loai_hinh_truc_chi_tiet" ADD "maLoaiHinhTruc" varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" DROP COLUMN "eqn"`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" ADD "eqn" varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" DROP COLUMN "maLoaiHinhTruc"`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" ADD "maLoaiHinhTruc" varchar(36) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "loai_hinh_truc_chi_tiet" ADD CONSTRAINT "FK_9c2e7b7923413f947be6e3f48ab" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("ma") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" ADD CONSTRAINT "FK_4e1f146482370b26ba409dbca8d" FOREIGN KEY ("eqn") REFERENCES "eQN"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" ADD CONSTRAINT "FK_56414ee57d6d509dcf0cefd2307" FOREIGN KEY ("maLoaiHinhTruc") REFERENCES "dm_loai_hinh_truc"("ma") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" DROP CONSTRAINT "FK_56414ee57d6d509dcf0cefd2307"`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" DROP CONSTRAINT "FK_4e1f146482370b26ba409dbca8d"`);
        await queryRunner.query(`ALTER TABLE "loai_hinh_truc_chi_tiet" DROP CONSTRAINT "FK_9c2e7b7923413f947be6e3f48ab"`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" DROP COLUMN "maLoaiHinhTruc"`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" ADD "maLoaiHinhTruc" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" DROP COLUMN "eqn"`);
        await queryRunner.query(`ALTER TABLE "cau_hinh_eqn_loai_hinh_truc" ADD "eqn" nvarchar(255) NOT NULL`);
        await queryRunner.query(`ALTER TABLE "loai_hinh_truc_chi_tiet" DROP COLUMN "maLoaiHinhTruc"`);
        await queryRunner.query(`ALTER TABLE "loai_hinh_truc_chi_tiet" ADD "maLoaiHinhTruc" nvarchar(255) NOT NULL`);
    }

}
