import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { GuardPost, User } from '../models';
import { ERROR } from '../utils/error';
import * as guardPostService from './guard-post.service';
import { ISearchGuardPostQuery } from './guard-post';
import { ISearchQuery } from '../types/req';

export const createGuardPost = async (
  req: Request & { body: GuardPost },
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await guardPostService.create(req.body, user);
    return res.json(result.data);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.ORGANIZATION_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.PENDING_GUEST_NOT_FOUND,
            message: '<PERSON><PERSON><PERSON><PERSON> tìm thấy đơn vị',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};

export const updateGuardPost = async (
  req: Request & { body: GuardPost } & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardPostService.update(
      Number(req.params.id),
      req.body,
    );
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Bảo vệ không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const deleteGuardPost = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardPostService.remove(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Bảo vệ không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getGuardPost = async (
  req: Request & { params: { id: number } },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardPostService.findById(Number(req.params.id));
    return res.json(result);
  } catch (e) {
    if (e instanceof Error && e.message === ERROR.DATA_NOT_FOUND) {
      return res.status(status.BAD_REQUEST).json({
        code: ERROR.DATA_NOT_FOUND,
        message: 'Bảo vệ không tồn tại',
        statusCode: 400,
        errors: {
          field: 'id',
          message: 'Id không tồn tại',
        },
      });
    } else if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const searchGuardPosts = async (
  req: Request & { query: ISearchQuery<ISearchGuardPostQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const result = await guardPostService.search(req.query);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getGuardPostWithOrg = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const user = req.user as User;
    const result = await guardPostService.getGuardPosts(user);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      switch (e.message) {
        case ERROR.ORGANIZATION_NOT_FOUND:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.PENDING_GUEST_NOT_FOUND,
            message: 'Không tìm thấy đơn vị',
            statusCode: 400,
          });

        default:
          return res.status(status.BAD_REQUEST).json({
            part: 'body',
            code: ERROR.BAD_REQUEST,
            message: e.message.toString(),
            statusCode: 400,
          });
      }
    }
    next(e);
  }
};
