import express from 'express';
import { checkAuth } from '../middlewares';
import { createValidator } from '../utils/validator';
// import { RolePerms } from '../constants';
import {
  createConfigShifts,
  deleteConfigShifts,
  getConfigShifts,
  searchConfigShifts,
  updateConfigShifts,
} from './config-shift.controller';
import {
  eBodyConfigShift,
  eIdInParams,
  eSearchQuery,
  jBodyConfigShift,
  jIdInParams,
  jSearchConfigShift,
} from './config-shift.validation';

const router = express.Router();

router.post(
  '/',
  checkAuth,
  // hasPerm([RolePerms.editConfigShift]),
  createValidator('body', jBodyConfigShift, eBodyConfigShift),
  createConfigShifts,
);

router.put(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editConfigShift]),
  createValidator('params', jIdInParams, eIdInParams),
  createValidator('body', jBodyConfigShift, eBodyConfigShift),
  updateConfigShifts,
);

router.delete(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.editConfigShift]),
  createValidator('params', jIdInParams, eIdInParams),
  deleteConfigShifts,
);

router.get(
  '/:id',
  checkAuth,
  // hasPerm([RolePerms.viewConfigShift]),
  createValidator('params', jIdInParams, eIdInParams),
  getConfigShifts,
);

router.get(
  '/',
  checkAuth,
  // hasPerm([
  //   RolePerms.viewConfigShift,
  //   RolePerms.viewGuardShiftManagement,
  //   RolePerms.viewGuardShift,
  // ]),
  createValidator('query', jSearchConfigShift, eSearchQuery),
  searchConfigShifts,
);

export default router;
