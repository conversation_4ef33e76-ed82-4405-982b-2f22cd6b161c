import dayjs from 'dayjs';
import { database } from '../config';
import { ShiftInspection, User } from '../models';
import { ERROR } from '../utils/error';
import { createLinks } from '../utils/pagination';
import {
  ICreateUpdateShiftInspection,
  ISearchShiftInspectionQuery,
} from './shift-inspections';

function createFullText(shiftInspection: Partial<ShiftInspection>): string {
  const fields = [
    shiftInspection.datetime,
    shiftInspection.codeOrg,
    shiftInspection.QNeID,
    shiftInspection.weaponCheck,
    shiftInspection.equipCheck,
    shiftInspection.situationCheck,
    shiftInspection.posts,
    shiftInspection.contentCheck,
    shiftInspection.note,
    shiftInspection.guardShiftId,
  ];
  return fields.filter(Boolean).join(' ');
}

export const create = async (
  data: ICreateUpdateShiftInspection,
  user: User,
) => {
  const repo = database.getRepository(ShiftInspection);
  const shiftInspection = await repo.save({
    QNeID: user.eQNId,
    datetime: data.datetime,
    codeOrg: user.manageOrgCode,
    contentCheck: data.contentCheck,
    posts: data.posts,
    note: data.note,
    weaponCheck: data.weaponCheck,
    equipCheck: data.equipCheck,
    situationCheck: data.situationCheck,
    guardShiftId: data.guardShiftId,
    fullText: createFullText(data),
  });
  return { data: shiftInspection, message: 'Tạo kiểm tra gác thành công' };
};

export const update = async (id: number, data: Partial<ShiftInspection>) => {
  const repo = database.getRepository(ShiftInspection);
  const shiftInspection = await repo.findOne({
    where: { id },
    withDeleted: true,
  });
  if (!shiftInspection) throw new Error(ERROR.DATA_NOT_FOUND);
  const updatedShiftInspection = await repo.update(
    shiftInspection.id as number,
    data,
  );
  return {
    data: updatedShiftInspection,
    message: 'Cập nhật kiểm tra gác thành công',
  };
};

export const findById = async (id: number) => {
  const repo = database.getRepository(ShiftInspection);
  const shiftInspection = await repo
    .createQueryBuilder('shiftInspection')
    .withDeleted()
    .leftJoinAndSelect('shiftInspection.guardShift', 'guardShift')
    .where('shiftInspection.id = :id', { id })
    .getOne();
  if (!shiftInspection) throw new Error(ERROR.DATA_NOT_FOUND);
  return { data: shiftInspection };
};

export const getUser = async () => {
  const repo = database.getRepository(User);
  const user = await repo.find({ withDeleted: true });
  if (!user) throw new Error(ERROR.DATA_NOT_FOUND);
  return { data: user };
};

export const getAll = async (
  query: ISearchShiftInspectionQuery,
  user: User,
) => {
  const repo = database.getRepository(ShiftInspection);
  const qb = repo
    .createQueryBuilder('shiftInspection')
    .withDeleted()
    .leftJoinAndSelect('shiftInspection.guardShift', 'guardShift');

  const page = query.page || 1;
  const limit = query.limit || 10;
  const skip = (page - 1) * limit;

  if (query.qneId) {
    qb.andWhere('shiftInspection.QNeID LIKE :qneId', {
      qneId: `%${query.qneId}%`,
    });
  }

  if (query.posts?.length) {
    const postIds = query.posts.map((id) => Number(id));
    const likeConditions = postIds
      .map((id, idx) => `shiftInspection.posts LIKE :post${idx}`)
      .join(' OR ');

    const likeParams = Object.fromEntries(
      query.posts.map((id, idx) => [`post${idx}`, `%\"id\":${id}%`]),
    );
    qb.andWhere(`(${likeConditions})`, likeParams);
  }

  if (Array.isArray(query.datetime) && query.datetime.length === 2) {
    const [start, end] = query.datetime;
    if (start && end) {
      qb.andWhere('shiftInspection.datetime BETWEEN :startDate AND :endDate', {
        startDate: dayjs(query.datetime[0]).startOf('day').toDate(),
        endDate: dayjs(query.datetime[1]).endOf('day').toDate(),
      });
    }
  }

  if (user.manageOrgCode) {
    qb.andWhere('shiftInspection.codeOrg LIKE :codeOrg', {
      codeOrg: `%${user.manageOrgCode}%`,
    });
  }

  if (query.fullText) {
    qb.andWhere('shiftInspection.fullText LIKE :fullText', {
      fullText: `%${query.fullText}%`,
    });
  }

  qb.leftJoinAndSelect('shiftInspection.organization', 'organization')
    .orderBy('shiftInspection.createdAt', 'DESC')
    .addOrderBy('shiftInspection.datetime', 'DESC')
    .skip(skip)
    .take(limit);

  const [shiftInspections, total] = await qb.getManyAndCount();
  const links = createLinks(
    '/shift-inspections/search?',
    query,
    page,
    Math.ceil(total / limit),
  );

  return {
    meta: {
      totalItems: total,
      itemsPerPage: limit,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      items: shiftInspections,
    },
    links,
  };
};

export const deleteOne = async (id: number) => {
  const repo = database.getRepository(ShiftInspection);
  const shiftInspection = await repo.findOne({
    where: { id },
    withDeleted: true,
  });
  if (!shiftInspection) throw new Error(ERROR.DATA_NOT_FOUND);
  await repo.delete(shiftInspection.id as number);
  return { data: shiftInspection, message: 'Xóa kiểm tra gác thành công' };
};

export const exportExcel = async (
  query: ISearchShiftInspectionQuery,
  user: User,
) => {
  const data = await getAll(query, user);
  /**
   * todo: export excel
   */
  return data;
};
