import { NextFunction, Request, Response } from 'express';
import { status } from 'http-status';
import { ERROR } from '../utils/error';
import * as eQNService from './eqn.service';
import { ISearchQuery } from '../types/req';
import { IListEQNQuery, ISearcEQNQuery } from './eqn';
import { User } from 'src/models';

export const seachEQNTreeOrg = async (
  req: Request & { query: ISearchQuery<ISearcEQNQuery> },
  res: Response,
  next: NextFunction,
) => {
  try {
    const query = req.query;
    const result = await eQNService.seachTreeOrg(query);
    return res.json(result);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getListEQN = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const query = req.query as unknown as ISearchQuery<IListEQNQuery>;
    const data = await eQNService.getListQN(query);
    return res.status(200).json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getQNById = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const id = req.params.id;
    const data = await eQNService.getQNById(id);
    return res.status(200).json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getRanks = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = await eQNService.getRanks();
    return res.status(200).json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getPositions = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = await eQNService.getPositions();
    return res.status(200).json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getOrgs = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = await eQNService.getOrgs();
    return res.status(200).json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const getDashboard = async (
  req: Request,
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = await eQNService.getDashboard(req.query, req.user as User);
    return res.status(200).json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};

export const dataFake = async (
  req: Request & { body: User },
  res: Response,
  next: NextFunction,
) => {
  try {
    const data = await eQNService.createPersons();
    return res.status(200).json(data);
  } catch (e) {
    if (e instanceof Error) {
      return res.status(status.BAD_REQUEST).json({
        part: 'body',
        code: ERROR.BAD_REQUEST,
        message: e.message.toString(),
        statusCode: 400,
      });
    }
    next(e);
  }
};
