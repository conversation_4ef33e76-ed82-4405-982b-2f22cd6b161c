import { Permission, Role, User } from '../models';

type IPaginationQuery = {
  limit?: number;
  page?: number;
  depth?: number;
  skip?: number;
};
export type ISearchQuery<T> = T & IPaginationQuery;

declare global {
  namespace Express {
    interface Request {
      user?: User;
      permissions?: Permission[];
      permsArr?: string[];
      role?: Role;
      ipAddress?: string;
    }
  }
}
