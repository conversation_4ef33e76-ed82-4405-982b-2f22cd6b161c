import {
  BeforeInsert,
  BeforeUpdate,
  Column,
  Entity,
  Join<PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { DMLoaiHinhTruc } from './dmLoaiHinhTruc.model';

@Entity()
export class LoaiHinhTrucChiTiet extends EntityModel {
  @PrimaryGeneratedColumn('uuid')
  id?: string;

  @Column()
  nhiemVu?: string;

  @Column({
    nullable: true,
  })
  noiDung?: string;

  @Column({
    type: 'int',
    default: 0,
  })
  stt?: number;

  @Column({
    name: 'maLoaiHinhTruc',
    type: 'varchar',
    length: 36,
  })
  maLoaiHinhTruc?: string;
  @ManyToOne(() => DMLoaiHinhTruc)
  @JoinColumn({ name: 'maLoaiHinhTruc', referencedColumnName: 'ma' })
  dMLoaiHinhTruc?: DMLoaiHinhTruc;

  @BeforeInsert()
  @BeforeUpdate()
  updateFullText() {
    this.fullText = [this.nhiemVu, this.noiDung]
      .filter(Boolean)
      .join(' ')
      .trim();
  }
}
