import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>umn,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import EntityModel from './entity.model';
import { Organization } from './organization.model';
import { CatLichTruc } from './catLichTruc.model';

@Entity('situationSummaries')
export class SituationSummary extends EntityModel {
  @PrimaryGeneratedColumn()
  id?: number;

  @Column({
    name: 'detail_duty_now_id',
    type: 'int',
  })
  detailDutyNowId?: number;
  @ManyToOne(() => CatLichTruc)
  @JoinColumn({ name: 'detail_duty_now_id' })
  detailDutyNow?: CatLichTruc;

  @Column({
    name: 'detail_duty_feature_id',
    type: 'int',
  })
  detailDutyFeatureId?: number;
  @ManyToOne(() => CatLichTruc)
  @JoinColumn({ name: 'detail_duty_feature_id' })
  detailDutyFeature?: CatLichTruc;

  @Column({
    name: 'total_per',
    type: 'int',
  })
  totalPer?: number;

  @Column({
    name: 'present_per',
    type: 'int',
  })
  presentPer?: number;

  @Column({
    name: 'absent_per',
    type: 'nvarchar',
    length: 'max',
    nullable: true,
  })
  absentPer?: string;

  @Column({
    name: 'equipment_take_out',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  equipmentTakeOut?: string;

  @Column({
    name: 'task_main',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  taskMain?: string;

  @Column({
    name: 'task_contain',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  taskContain?: string;

  @Column({
    name: 'task_sudden',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  taskSudden?: string;

  @Column({
    name: 'pros_and_cons',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  prosAndCons?: string;

  @Column({
    name: 'task_continue',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  taskContinue?: string;

  @Column({
    name: 'book_handover',
    type: 'nvarchar',
    length: 2000,
    nullable: true,
  })
  bookHandover?: string;

  @Column({
    name: 'name_command',
    type: 'nvarchar',
    length: 250,
    nullable: true,
  })
  nameCommand?: string;

  @Column({
    name: 'name_duty_now',
    type: 'nvarchar',
    length: 250,
  })
  nameDutyNow?: string;

  @Column({
    name: 'name_duty_feature',
    type: 'nvarchar',
    length: 250,
  })
  nameDutyFeature?: string;

  @Column({
    type: 'datetime2',
  })
  date?: Date;

  @Column({
    name: 'code_org',
    type: 'nvarchar',
    length: 17,
  })
  codeOrg?: string;
  @ManyToOne(() => Organization, (organization) => organization.code)
  @JoinColumn({ name: 'code_org', referencedColumnName: 'code' })
  organization?: Organization;
}
